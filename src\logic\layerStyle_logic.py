import json
from datetime import datetime
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QMessageBox, QSizePolicy, QComboBox,
                               QLabel, QProgressDialog, QHBoxLayout)
from PySide6.QtCore import QTimer, Qt, QSize, Signal
from src.ui.Ui_layerStyle import Ui_formula_layer_style
from src.logic.layerStyleManager import (BoxItem, PalletPattern, PatternManager, SmartPalletPlanner)
from src.common.sql_lite_tool import SqliteTool
from src.logic.palletVisualization import PalletVisualization_Plan,PalletVisualization
from src.common.log import logger
from PySide6.QtWidgets import QGridLayout

class FormulaLayerStyleLogic(QWidget,Ui_formula_layer_style):
    """托盘规划应用程序主窗口"""

    # 重复信号
    Repright_signal = Signal(str)

    # 配方数据更新信号
    formula_data_updated = Signal(int)  # 发送配方ID


    def __init__(self):
        super().__init__()
        self.setupUi(self)

        self.db_tool = SqliteTool.get_instance()

        # 初始化模式管理器
        self.pattern_manager = PatternManager()
        
        # 初始化时不创建默认模式，等待加载数据
        # 如果有数据则显示，如果没有数据则保持空白
        
        # 创建可视化组件并替换占位符
        self.visualization = PalletVisualization()
        layout = QVBoxLayout(self.PalletVisualization)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.visualization)
        
        # 创建第二个可视化组件并放入plan_vision中
        self.plan_visualization = PalletVisualization_Plan()
        self.plan_visualization.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.plan_layout = QVBoxLayout(self.plan_vision)
        self.plan_layout.setContentsMargins(5, 5, 5, 5)
        self.plan_layout.addWidget(self.plan_visualization)
        
        # 设置多选按钮为可选中状态
        self.select_multiple_box.setCheckable(True)
        
        # 定义统一的标签样式
        self.label_style = "font-size: 12px"
        
        # 存储重复模式
        self.repeat_pattern_left = []
        self.repeat_pattern_right = []
        self.repeat_enabled_left = False
        self.repeat_enabled_right = False
        
        # 连接信号和槽
        self.setup_connections()

        self.setup_long_press()

        # 初始化时不设置任何默认方案，保持空白状态
        # 等待通过set_current_editing_formula加载实际数据

        # 更新界面
        self.update_ui()

        # 确保可视化组件在plan_vision中正确显示
        self.adjustPlanVisualizationSize()

        # 设置窗口大小变化时的处理
        self.installEventFilter(self)

        #初始化堆垛类型
        self.stacktypepe_init()
        
        # 初始化页面切换按钮样式（需要在所有UI初始化完成后调用）
        QTimer.singleShot(100, self.setup_page_button_styles)

    def eventFilter(self, obj, event):
        """事件过滤器，用于处理窗口大小变化等事件"""
        if obj == self and event.type() == event.Type.Resize:
            # 窗口大小变化时调整plan_vision中的可视化组件大小
            self.adjustPlanVisualizationSize()
        return super().eventFilter(obj, event)
    
    def showEvent(self, event):
        """处理窗口显示事件"""
        super().showEvent(event)
        # 确保可视化组件在窗口显示时正确调整大小
        QTimer.singleShot(100, self.adjustPlanVisualizationSize)
    
    def adjustPlanVisualizationSize(self):
        """调整plan_vision中可视化组件大小"""
        if self.pattern_manager and self.pattern_manager.get_current_pattern():
            pattern = self.pattern_manager.get_current_pattern()
            
            # 获取plan_vision的可用大小 - 不减去边距，使组件填满整个区域
            available_width = self.plan_vision.width()
            available_height = self.plan_vision.height()
            
            # 计算托盘的宽高比
            pallet_ratio = pattern.pallet_width / pattern.pallet_length
            
            # 计算可用空间的宽高比
            available_ratio = available_width / available_height
            
            # 根据宽高比决定如何缩放
            if pallet_ratio > available_ratio:
                # 宽度受限
                viz_width = available_width
                viz_height = viz_width / pallet_ratio
            else:
                # 高度受限
                viz_height = available_height
                viz_width = viz_height * pallet_ratio
            
            # 设置可视化组件的大小
            self.plan_visualization.setFixedSize(QSize(int(viz_width), int(viz_height)))
            
            # 更新布局以居中显示可视化组件
            self.plan_layout.setAlignment(Qt.AlignCenter)
            self.plan_layout.setContentsMargins(0, 0, 0, 0)  # 移除所有边距，使组件填满整个区域
            
            # 更新可视化
            self.plan_visualization.update()

    
    def setup_connections(self):
        # 方案操作按钮
        self.new_pattern_btn.clicked.connect(self.on_new_pattern) # 新增方案
        self.copy_pattern_btn.clicked.connect(self.on_copy_pattern) # 复制方案
        self.delete_pattern_btn.clicked.connect(self.on_delete_pattern) # 删除方案
        self.next_plan.clicked.connect(self.on_next_plan) # 下一个方案
        self.previous_plan.clicked.connect(self.on_prev_plan) # 上一个方案
        
        # 参数设置
        self.generate_btn.clicked.connect(self.on_generate)

        # 连接保存方案按钮 - 保存当前配方的所有层样式
        self.save_plan.clicked.connect(self.on_save_all_layer_styles)

        # 连接箱子参数保存按钮
        self.save_btn_l.clicked.connect(self.on_save_box_params)

        # 连接托盘参数保存按钮
        self.save_btn_r.clicked.connect(self.on_save_pallet_params)

        # 连接垛型保存按钮
        self.stack_save_btn_l.clicked.connect(self.on_save_stack_config)
        self.stack_save_btn_r.clicked.connect(self.on_save_stack_config)

        self.length_pallet.textChanged.connect(self.on_dimension_changed)
        self.width_pallet.textChanged.connect(self.on_dimension_changed)
        
        # 箱子操作
        self.add_box_btn.clicked.connect(self.on_add_box)
        self.delete_box_btn.clicked.connect(self.on_delete_box)
        self.select_multiple_box.clicked.connect(self.on_multi_select_toggled)
        # 旋转按钮事件 - 支持长按连续旋转
        self.setup_rotate_buttons()

        self.visualization.boxSelected.connect(self.on_box_selected)
        self.visualization.boxesSelected.connect(self.on_boxes_selected)
        self.visualization.boxMoved.connect(self.on_box_moved)
        self.visualization.boxNumberChanged.connect(self.on_box_number_changed)
        
        self.plan_visualization.boxSelected.connect(self.on_plan_box_selected)
        self.plan_visualization.boxesSelected.connect(self.on_plan_boxes_selected)
        self.plan_visualization.boxMoved.connect(self.on_plan_box_moved)
        
        # 对齐和分布按钮连接
        self.top_alignment.clicked.connect(lambda: self.on_align_boxes('top'))
        self.bottom_alignment.clicked.connect(lambda: self.on_align_boxes('bottom'))
        self.left_alignment.clicked.connect(lambda: self.on_align_boxes('left'))
        self.right_alignment.clicked.connect(lambda: self.on_align_boxes('right'))
        self.horizontal_distribution.clicked.connect(lambda: self.on_distribute_boxes('horizontal'))
        self.vertical_distribution.clicked.connect(lambda: self.on_distribute_boxes('vertical'))
        self.horizontal_flip.clicked.connect(lambda: self.on_flip_boxes('horizontal'))
        self.Vertical_flip.clicked.connect(lambda: self.on_flip_boxes('vertical'))

        # 切换界面按钮
        self.stackedWidget.setCurrentIndex(1)  # 默认显示箱子/托盘页面
        
        # 创建按钮组用于页面切换按钮
        from PySide6.QtWidgets import QButtonGroup
        self.page_button_group = QButtonGroup(self)
        self.page_button_group.addButton(self.laystyle_btn, 0)
        self.page_button_group.addButton(self.boxpallet_btn, 1)
        self.page_button_group.addButton(self.stacktype_btn, 2)
        
        # 设置按钮为可选中状态
        self.laystyle_btn.setCheckable(True)
        self.boxpallet_btn.setCheckable(True)
        self.stacktype_btn.setCheckable(True)
        
        # 设置默认选中按钮（对应默认页面）
        self.boxpallet_btn.setChecked(True)
        
        # 连接按钮组信号
        self.page_button_group.buttonClicked.connect(self.on_page_button_clicked)
        
        # 设置按钮样式
        self.setup_page_button_styles()

        # 垛型 - 使用保留选择的层数变更处理
        self.stack_layer_spinBox_l.valueChanged.connect(self.on_stack_layer_count_changed)
        self.stack_layer_spinBox_r.valueChanged.connect(self.on_stack_layer_count_changed)
        self.stack_lrl_check.checkStateChanged.connect(
            lambda state, frame1='frame_5', frame2='frame_6': self.check_stack_config(state, frame1, frame2))
        self.stack_lrr_check.checkStateChanged.connect(
            lambda state, frame1='frame_6', frame2='frame_5': self.check_stack_config(state, frame1, frame2))
        self.Repright_check_l.checkStateChanged.connect(
            lambda state: self.Repright_check(state, 'frame_5'))
        self.Repright_check_r.checkStateChanged.connect(
            lambda state: self.Repright_check(state, 'frame_6'))

        # 初始化旋转定时器
        self.rotate_timer = QTimer()
        self.rotate_timer.timeout.connect(self.on_rotate_timer)
        self.rotate_clockwise = True  # 旋转方向标志

        # 初始化加载垛层配置
        QTimer.singleShot(500, self.load_stack_config)

    def setup_rotate_buttons(self):
        """设置旋转按钮的长按功能"""
        # 顺时针旋转按钮
        self.rotate_btn.pressed.connect(lambda: self.start_rotate(False))
        self.rotate_btn.released.connect(self.stop_rotate)
        self.rotate_btn.clicked.connect(lambda: self.on_rotate_box(False))

        # 逆时针旋转按钮
        self.rotate_btn_2.pressed.connect(lambda: self.start_rotate(True))
        self.rotate_btn_2.released.connect(self.stop_rotate)
        self.rotate_btn_2.clicked.connect(lambda: self.on_rotate_box(True))

    def start_rotate(self, clockwise):
        """开始连续旋转"""
        self.rotate_clockwise = clockwise
        # 延迟500ms后开始连续旋转，避免与单击冲突
        QTimer.singleShot(500, self.start_continuous_rotate)

    def start_continuous_rotate(self):
        """开始连续旋转定时器"""
        if self.rotate_btn.isDown() or self.rotate_btn_2.isDown():
            # 每200ms旋转一次
            self.rotate_timer.start(200)

    def stop_rotate(self):
        """停止连续旋转"""
        self.rotate_timer.stop()

    def on_rotate_timer(self):
        """旋转定时器事件"""
        # 检查按钮是否仍然被按下
        if self.rotate_btn.isDown() or self.rotate_btn_2.isDown():
            self.on_rotate_box(self.rotate_clockwise)
        else:
            self.rotate_timer.stop()

    def on_dimension_changed(self):
        """处理托盘或箱子尺寸变化事件"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return

        # 获取新的尺寸
        new_pallet_width = self.safe_int_convert(self.width_pallet.text())
        new_pallet_length = self.safe_int_convert(self.length_pallet.text())
        new_box_width = self.safe_int_convert(self.width_box.text())
        new_box_length = self.safe_int_convert(self.length_box.text())
        
        # 更新方案的尺寸
        pattern.pallet_width = new_pallet_width
        pattern.pallet_length = new_pallet_length
        pattern.box_width = new_box_width
        pattern.box_length = new_box_length

        # 检查现有箱子是否仍然在有效范围内
        self.validate_existing_boxes(pattern)

        # 更新最大行列数显示
        self.update_max_layout_info(pattern)

        # 调整可视化组件大小
        self.adjustPlanVisualizationSize()

        # 更新两个可视化组件
        self.visualization.update()
        self.plan_visualization.update()

    def validate_existing_boxes(self, pattern):
        """验证现有箱子是否仍在有效范围内"""
        if not pattern.boxes:
            return

        invalid_boxes = []

        for box in pattern.boxes:
            # 检查箱子是否超出托盘边界
            if (box.x < 0 or box.y < 0 or
                box.x + box.width > pattern.pallet_width or
                box.y + box.length > pattern.pallet_length):
                invalid_boxes.append(box)

        if invalid_boxes:
            box_names = [box.id for box in invalid_boxes]
            reply = QMessageBox.question(
                self,
                "尺寸变化警告",
                f"以下箱子超出了新的托盘范围：{', '.join(box_names)}\n\n"
                f"是否自动调整这些箱子的位置到托盘边界内？\n"
                f"点击'Yes'自动调整，点击'No'删除这些箱子。",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
            )

            if reply == QMessageBox.Yes:
                # 自动调整箱子位置
                for box in invalid_boxes:
                    # 调整到托盘边界内
                    box.x = max(0, min(pattern.pallet_width - box.width, box.x))
                    box.y = max(0, min(pattern.pallet_length - box.length, box.y))

                QMessageBox.information(self, "调整完成", f"已自动调整{len(invalid_boxes)}个箱子的位置")

            elif reply == QMessageBox.No:
                # 删除超出范围的箱子
                for box in invalid_boxes:
                    pattern.boxes.remove(box)

                QMessageBox.information(self, "删除完成", f"已删除{len(invalid_boxes)}个超出范围的箱子")

                # 如果删除了选中的箱子，清除选择
                if (self.visualization.selected_box_id and
                    self.visualization.selected_box_id in box_names):
                    self.visualization.selected_box_id = None
                    self.plan_visualization.selected_box_id = None
                    self.delete_box_btn.setEnabled(False)

                # 清理多选列表
                if self.visualization.selected_box_ids:
                    self.visualization.selected_box_ids = [
                        box_id for box_id in self.visualization.selected_box_ids
                        if box_id not in box_names
                    ]
                    self.plan_visualization.selected_box_ids = self.visualization.selected_box_ids.copy()
            # 如果选择Cancel，不做任何操作

    def update_max_layout_info(self, pattern):
        """更新最大行列数信息显示"""
        try:
            # 获取当前间距设置
            row_spacing = self.safe_int_convert(self.row_spacing_spin_3.text())
            col_spacing = self.safe_int_convert(self.col_spacing_spin_3.text())

            # 创建规划器来计算最大布局
            planner = SmartPalletPlanner(
                (pattern.pallet_width, pattern.pallet_length),
                (pattern.box_width, pattern.box_length),
                pattern.gap,
                False
            )

            # 计算最大行列数
            max_rows, max_cols, can_place = planner.calculate_max_layout(row_spacing, col_spacing)

            # 计算总箱子数
            max_boxes = max_rows * max_cols if can_place else 0

            # 计算利用率
            if can_place and pattern.pallet_width > 0 and pattern.pallet_length > 0:
                box_area = pattern.box_width * pattern.box_length
                total_box_area = max_boxes * box_area
                pallet_area = pattern.pallet_width * pattern.pallet_length
                utilization = (total_box_area / pallet_area) * 100
            else:
                utilization = 0

        except Exception as e:
            pass

    def on_plan_box_selected(self, box_id):
        """处理plan_vision中箱子选中事件"""
        # 同步选中状态到主visualization
        self.visualization.selected_box_id = box_id
        if not self.visualization.multi_select_mode:
            self.visualization.selected_box_ids = [box_id] if box_id else []
        self.visualization.update()
        # 更新删除按钮状态
        self.delete_box_btn.setEnabled(bool(box_id))
    
    def on_plan_boxes_selected(self, box_ids):
        """处理plan_vision中多个箱子选中事件"""
        # 更新删除按钮状态
        self.delete_box_btn.setEnabled(bool(box_ids))
        
        # 确保在多选模式下正确同步
        if self.plan_visualization.multi_select_mode:
            # 同步选中状态到主visualization
            self.visualization.selected_box_ids = box_ids.copy()
            # 如果有单个选中的箱子，也更新单选状态
            if box_ids and self.plan_visualization.selected_box_id in box_ids:
                self.visualization.selected_box_id = self.plan_visualization.selected_box_id
            elif box_ids:
                self.visualization.selected_box_id = box_ids[0]
            else:
                self.visualization.selected_box_id = None
            self.visualization.update()
    
    def on_plan_box_moved(self, box_id, x, y):
        """处理plan_vision中箱子移动事件"""
        # 同步更新主visualization
        self.visualization.update()

    def on_choice(self, index):
        """处理方案选择事件"""
        # 确保索引处有方案
        plan_letter = chr(65 + (index % 26))  # 65是'A'的ASCII码，循环使用A-Z
        self.pattern_manager.ensure_pattern_at_index(index, f"方案 {plan_letter}")

        # 设置当前方案
        self.pattern_manager.set_current_pattern(index)

        # 更新界面
        self.update_ui()
    
    def update_ui(self):
        """更新界面"""
        # 获取当前模式
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return
        
        # 更新方案标签 - 显示实际的方案名称
        current_index = self.pattern_manager.current_pattern_index
        if hasattr(self, 'label_plan'):
            # 显示实际的方案名称，而不是字母
            self.label_plan.setText(pattern.name)

        # 更新参数设置

        self.width_pallet.setText(str(pattern.pallet_width))
        self.length_pallet.setText(str(pattern.pallet_length))
        self.width_box.setText(str(pattern.box_width))
        self.length_box.setText(str(pattern.box_length))
        self.step_spin.setText(str(pattern.gap))
        
        # 更新可视化
        self.visualization.set_pattern(pattern)
        self.plan_visualization.set_pattern(pattern)
        
        # 调整plan_vision中的可视化组件大小
        self.adjustPlanVisualizationSize()
        
        # 重置多选模式
        self.select_multiple_box.setChecked(False)
        self.visualization.set_multi_select_mode(False)
        self.plan_visualization.set_multi_select_mode(False)
        
        # 重置箱子选择
        self.delete_box_btn.setEnabled(False)

    def on_new_pattern(self):
        """处理新建方案事件"""
        # 创建新方案 - 使用更有意义的名称
        new_index = len(self.pattern_manager.patterns)

        # 生成方案名称：默认创建"方案X层样式"格式
        if new_index == 0:
            pattern_letter = "A"
        else:
            # 使用字母序列：A, B, C, ..., Z, AA, AB, ...
            pattern_letter = chr(65 + (new_index % 26))  # 65是'A'的ASCII码
            if new_index >= 26:
                pattern_letter = chr(65 + (new_index // 26 - 1)) + pattern_letter

        # 创建完整的方案名称
        pattern_name = pattern_letter

        new_pattern = PalletPattern(
            pattern_name,
            int(float(self.width_pallet.text())) if self.width_pallet.text() else 0,
            int(float(self.length_pallet.text())) if self.length_pallet.text() else 0,
            int(float(self.width_box.text())) if self.width_box.text() else 0,
            int(float(self.length_box.text())) if self.length_box.text() else 0,
            int(float(self.step_spin.text())) if self.step_spin.text() else 0,
            auto_mode=False
        )

        self.pattern_manager.add_pattern(new_pattern)

        # 设置为当前方案
        self.pattern_manager.current_pattern_index = new_index
        self.pattern_manager.current_pattern = new_pattern

        self.stacktypepe_init()

        # 更新界面
        self.update_ui()
    
    def on_copy_pattern(self):
        """处理复制方案事件"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return
        
        # 创建新方案（复制当前方案）
        new_index = len(self.pattern_manager.patterns)

        # 生成复制方案的名称
        if new_index == 0:
            pattern_name = "A"
        else:
            pattern_name = chr(65 + (new_index % 26))  # 65是'A'的ASCII码
            if new_index >= 26:
                pattern_name = chr(65 + (new_index // 26 - 1)) + pattern_name

        new_pattern = PalletPattern(
            pattern_name,
            pattern.pallet_width,
            pattern.pallet_length,
            pattern.box_width,
            pattern.box_length,
            pattern.gap,
            pattern.auto_mode,
            pattern.rows,
            pattern.cols,
            pattern.row_spacing,
            pattern.col_spacing
        )
        
        # 复制箱子
        for box in pattern.boxes:
            new_box = BoxItem(
                box.id,
                box.x,
                box.y,
                box.width,
                box.length,
                box.rotated
            )
            new_pattern.boxes.append(new_box)  # 直接添加，不检查重叠（假设原方案是有效的）
        
        # 添加新方案
        self.pattern_manager.add_pattern(new_pattern)

        # 设置为当前方案
        self.pattern_manager.current_pattern_index = new_index
        self.pattern_manager.current_pattern = new_pattern

        self.stacktypepe_init()

        # 更新界面
        self.update_ui()


    
    def on_delete_pattern(self):
        """处理删除方案事件，垛型中应用的方案不可删除"""
        current_pattern = self.pattern_manager.get_current_pattern()
        pattern_name = current_pattern.name
        child_widget = self.scrollArea_l.findChild(QWidget)
        children_combobox = child_widget.findChildren(QComboBox)
        for combobox in children_combobox:
            if combobox.currentText() == pattern_name:
                QMessageBox.warning(self, "警告", "当前方案正在被使用，无法删除")
                return
        # 确保至少保留一个方案
        if len(self.pattern_manager.patterns) <= 1:
            QMessageBox.warning(self, "警告", "必须保留至少一个方案")
            return
        
        # 删除当前方案
        index = self.pattern_manager.current_pattern_index
        self.pattern_manager.remove_pattern(index)
        # 更新stacktypepe
        self.stacktypepe_init()
        # 移动到下一个方案（如果删除的是最后一个，PatternManager会自动调整到最后一个可用方案）
        self.pattern_manager.next_plan()
        self.update_ui()
    
    def on_generate(self):
        """处理生成布局事件"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return
        
        pattern.pallet_width = self.safe_int_convert(self.width_pallet.text())
        pattern.pallet_length = self.safe_int_convert(self.length_pallet.text())
        pattern.box_width = self.safe_int_convert(self.width_box.text())
        pattern.box_length = self.safe_int_convert(self.length_box.text())
        pattern.rows = self.safe_int_convert(self.rows_spin_3.text())
        pattern.cols = self.safe_int_convert(self.cols_spin_3.text())
        pattern.row_spacing = self.safe_int_convert(self.row_spacing_spin_3.text())
        pattern.col_spacing = self.safe_int_convert(self.col_spacing_spin_3.text())

        # 创建规划器
        planner = SmartPalletPlanner(
            (pattern.pallet_width, pattern.pallet_length),
            (pattern.box_width, pattern.box_length),
            pattern.gap,  # 手动模式不使用间隙参数
            False  # 手动模式不使用旋转
        )

        # 检查布局是否可行
        max_rows, max_cols, can_place = planner.calculate_max_layout(
            pattern.row_spacing, pattern.col_spacing
        )

        if not can_place:
            QMessageBox.warning(
                self,
                "布局错误",
                f"无法在托盘({pattern.pallet_width}x{pattern.pallet_length})上放置"
                f"箱子({pattern.box_width}x{pattern.box_length})\n\n"
                f"请检查箱子尺寸是否超出托盘范围。"
            )
            return

        if pattern.rows > max_rows or pattern.cols > max_cols:
            reply = QMessageBox.question(
                self,
                "布局警告",
                f"请求的布局({pattern.rows}行x{pattern.cols}列)超出最大可能布局({max_rows}行x{max_cols}列)\n\n"
                f"最多可放置: {max_rows * max_cols}个箱子\n"
                f"当前请求: {pattern.rows * pattern.cols}个箱子\n\n"
                f"是否自动调整为最大可能布局？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                pattern.rows = max_rows
                pattern.cols = max_cols
                # 更新界面显示
                self.rows_spin_3.setText(str(max_rows))
                self.cols_spin_3.setText(str(max_cols))

            else:
                return

        # 生成手动布局
        boxes = planner.generate_manual_layout(
            pattern.rows,
            pattern.cols,
            pattern.row_spacing,
            pattern.col_spacing
        )

        if not boxes:
            QMessageBox.warning(self, "生成失败", "无法生成布局，请检查参数设置")
            return
        
        # 更新方案
        pattern.clear_boxes()
        for box in boxes:
            pattern.boxes.append(box)  # 直接添加，不检查重叠（手动生成的布局不会重叠）

        # 显示生成结果
        total_boxes = len(boxes)
        box_area = pattern.box_width * pattern.box_length
        total_box_area = total_boxes * box_area
        pallet_area = pattern.pallet_width * pattern.pallet_length
        utilization = (total_box_area / pallet_area) * 100 if pallet_area > 0 else 0



        # 更新界面
        self.update_ui()

        # 调整plan_vision中的可视化组件大小
        self.adjustPlanVisualizationSize()
    
    def on_add_box(self):
        """处理添加箱子事件"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return

        # 先同步界面的尺寸值到pattern对象
        pattern.pallet_width = int(float(self.width_pallet.text())) if self.width_pallet.text() else 0
        pattern.pallet_length = int(float(self.length_pallet.text())) if self.length_pallet.text() else 0
        pattern.box_width = int(float(self.width_box.text())) if self.width_box.text() else 0
        pattern.box_length = int(float(self.length_box.text())) if self.length_box.text() else 0

        # 检查托盘和箱子尺寸是否有效
        if pattern.pallet_width <= 0 or pattern.pallet_length <= 0:
            QMessageBox.warning(self, "警告", "托盘尺寸无效，请先设置正确的托盘长度和宽度")
            return

        if pattern.box_width <= 0 or pattern.box_length <= 0:
            QMessageBox.warning(self, "警告", "箱子尺寸无效，请先设置正确的箱子长度和宽度")
            return

        # 检查箱子是否超出托盘范围
        if pattern.box_width > pattern.pallet_width or pattern.box_length > pattern.pallet_length:
            QMessageBox.warning(
                self,
                "警告",
                f"箱子尺寸({pattern.box_width}x{pattern.box_length})超出托盘范围({pattern.pallet_width}x{pattern.pallet_length})，无法生成箱子"
            )
            return

        # 计算新箱子ID
        max_id = 0
        for box in pattern.boxes:
            # 处理不同类型的ID
            if isinstance(box.id, str):
                # 如果是字符串格式（如"box_1"），提取数字部分
                if box.id.startswith("box_"):
                    try:
                        id_num = int(box.id.split("_")[1])
                        max_id = max(max_id, id_num)
                    except (ValueError, IndexError):
                        # 如果无法解析，跳过这个ID
                        continue
                else:
                    # 尝试直接转换为整数
                    try:
                        id_num = int(box.id)
                        max_id = max(max_id, id_num)
                    except ValueError:
                        # 如果无法转换，跳过这个ID
                        continue
            elif isinstance(box.id, (int, float)):
                # 如果是数字类型，直接比较
                max_id = max(max_id, int(box.id))

        box_id = max_id + 1
        

        # 计算居中位置，确保箱子完全在托盘内，并向下取整
        import math
        center_x = max(0, math.floor((pattern.pallet_width - pattern.box_width) / 2))
        center_y = max(0, math.floor((pattern.pallet_length - pattern.box_length) / 2))

        # 创建新箱子（居中放置）
        box = BoxItem(
            box_id,
            center_x,
            center_y,
            pattern.box_width,
            pattern.box_length,
            False
        )

        # 尝试找到一个不重叠的位置
        found_position = False

        # 首先尝试居中位置
        if pattern.add_box(box):
            found_position = True

        else:
            # 如果居中位置不行，尝试从左上角开始的位置
            step = min(20, pattern.box_width // 4, pattern.box_length // 4)  # 使用较小的步长
            step = max(1, step)  # 确保步长至少为1

            max_x = int(pattern.pallet_width - pattern.box_width)
            max_y = int(pattern.pallet_length - pattern.box_length)

            if max_x < 0 or max_y < 0:
                QMessageBox.warning(self, "警告", "箱子尺寸超出托盘范围，无法生成箱子")
                return

            for y in range(0, max_y + 1, step):
                for x in range(0, max_x + 1, step):
                    box.x = x
                    box.y = y
                    if pattern.add_box(box):
                        found_position = True

                        break
                if found_position:
                    break

        if not found_position:
            QMessageBox.warning(self, "警告", "无法找到放置新箱子的位置，托盘已满或箱子尺寸过大")
            return

        # 更新界面
        self.visualization.set_pattern(pattern)
        self.plan_visualization.set_pattern(pattern)
        self.visualization.selected_box_id = box_id
        self.plan_visualization.selected_box_id = box_id
        self.visualization.update()
        self.plan_visualization.update()
        self.delete_box_btn.setEnabled(True)
    
    def on_delete_box(self):
        """处理删除箱子事件"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return
        
        if self.visualization.multi_select_mode:
            # 多选模式：删除所有选中的箱子
            selected_box_ids = self.visualization.selected_box_ids
            if not selected_box_ids:
                # 如果主visualization没有选中的箱子，尝试从plan_visualization获取
                selected_box_ids = self.plan_visualization.selected_box_ids
                
            if not selected_box_ids:
                QMessageBox.information(self, "提示", "请先选择要删除的箱子")
                return
            
            # 删除所有选中的箱子
            for box_id in selected_box_ids:
                pattern.remove_box(box_id)
            
            # 更新界面
            self.visualization.selected_box_ids = []
            self.visualization.selected_box_id = None
            self.plan_visualization.selected_box_ids = []
            self.plan_visualization.selected_box_id = None
            self.visualization.update()
            self.plan_visualization.update()
            self.delete_box_btn.setEnabled(False)
        else:
            # 单选模式：删除单个选中的箱子
            selected_box_id = self.visualization.selected_box_id
            if not selected_box_id:
                # 如果主visualization没有选中的箱子，尝试从plan_visualization获取
                selected_box_id = self.plan_visualization.selected_box_id
                
            if not selected_box_id:
                QMessageBox.information(self, "提示", "请先选择要删除的箱子")
                return
            
            # 删除箱子
            pattern.remove_box(selected_box_id)
            
            # 更新界面
            self.visualization.selected_box_id = None
            self.plan_visualization.selected_box_id = None
            self.visualization.update()
            self.plan_visualization.update()
            self.delete_box_btn.setEnabled(False)
    
    def on_box_selected(self, box_id):
        """处理箱子选中事件"""
        # 更新删除按钮状态
        self.delete_box_btn.setEnabled(bool(box_id))
        
        # 同步选中状态到plan_visualization
        self.plan_visualization.selected_box_id = box_id
        if not self.plan_visualization.multi_select_mode:
            self.plan_visualization.selected_box_ids = [box_id] if box_id else []
        self.plan_visualization.update()
    
    def on_boxes_selected(self, box_ids):
        """处理多个箱子选中事件"""
        # 更新删除按钮状态
        self.delete_box_btn.setEnabled(bool(box_ids))
        
        # 确保在多选模式下正确同步
        if self.visualization.multi_select_mode:
            # 同步选中状态到plan_visualization
            self.plan_visualization.selected_box_ids = box_ids.copy()
            # 如果有单个选中的箱子，也更新单选状态
            if box_ids and self.visualization.selected_box_id in box_ids:
                self.plan_visualization.selected_box_id = self.visualization.selected_box_id
            elif box_ids:
                self.plan_visualization.selected_box_id = box_ids[0]
            else:
                self.plan_visualization.selected_box_id = None
            self.plan_visualization.update()
    
    def on_box_moved(self, box_id, x, y):
        """处理箱子移动事件"""
        # 处理堆叠逻辑
        self.plan_visualization.handle_box_stacking(box_id)
        # 同步更新plan_visualization
        self.plan_visualization.update()
    
    def on_move_box(self, direction):
        """处理箱子方向移动事件 - 根据步长精确移动"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return

        # 获取步长
        step = int(self.step_spin.text())
        if step <= 0:
            return

        # 计算实际的托盘边界（与PalletVisualization保持一致）
        # 获取可视化组件的尺寸来计算缩放比例
        width = self.visualization.width()
        height = self.visualization.height()

        # 使用与PalletVisualization相同的边距设置
        margin_left = 20
        margin_right = 0
        margin_top = 0
        margin_bottom = -20

        # 计算缩放比例
        scale_x = (width - margin_left - margin_right) / int(pattern.pallet_width)
        scale_y = (height - margin_top - margin_bottom) / int(pattern.pallet_length)
        scale = min(scale_x, scale_y)

        # 计算实际的托盘边界
        pallet_width_boundary = pattern.pallet_width
        pallet_height_boundary = pattern.pallet_length 

        # 检查是否为多选模式
        if self.visualization.multi_select_mode and self.visualization.selected_box_ids:
            # 多选模式：移动所有选中的箱子
            moved_count = 0
            for box_id in self.visualization.selected_box_ids:
                # 获取箱子
                selected_box = None
                for box in pattern.boxes:
                    if box.id == box_id:
                        selected_box = box
                        break

                if not selected_box:
                    continue

                # 计算新位置
                new_x, new_y = selected_box.x, selected_box.y

                if direction == "right":
                    new_x = selected_box.x + step
                elif direction == "left":
                    new_x = selected_box.x - step
                elif direction == "down":
                    new_y = selected_box.y + step
                elif direction == "up":
                    new_y = selected_box.y - step

                # 检查边界约束（使用实际的托盘边界）
                new_x = max(0, min(pallet_width_boundary - selected_box.width, new_x))
                new_y = max(0, min(pallet_height_boundary - selected_box.length, new_y))

                # 只有位置真正改变时才移动
                if new_x != selected_box.x or new_y != selected_box.y:
                    selected_box.x = new_x
                    selected_box.y = new_y
                    moved_count += 1
        else:
            # 单选模式：移动单个箱子
            selected_box_id = self.visualization.selected_box_id
            if not selected_box_id:
                return

            # 获取选中的箱子
            selected_box = None
            for box in pattern.boxes:
                if box.id == selected_box_id:
                    selected_box = box
                    break

            if not selected_box:
                return

            # 记录原始位置
            original_x, original_y = selected_box.x, selected_box.y

            # 计算新位置
            new_x, new_y = original_x, original_y

            if direction == "right":
                new_x = original_x + step
            elif direction == "left":
                new_x = original_x - step
            elif direction == "down":
                new_y = original_y + step
            elif direction == "up":
                new_y = original_y - step

            # 检查边界约束（使用实际的托盘边界）
            new_x = max(0, min(pallet_width_boundary - selected_box.width, new_x))
            new_y = max(0, min(pallet_height_boundary - selected_box.length, new_y))

            # 只有位置真正改变时才移动
            if new_x != original_x or new_y != original_y:
                selected_box.x = new_x
                selected_box.y = new_y

        # 更新两个可视化组件
        self.visualization.update()
        self.plan_visualization.update()
    
    def on_rotate_box(self,clockwise):
        """处理箱子旋转事件"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return

        # 获取托盘的实际尺寸用于边界检查
        pallet_width = pattern.pallet_width
        pallet_length = pattern.pallet_length

        # 检查是否为多选模式
        if self.visualization.multi_select_mode and self.visualization.selected_box_ids:
            # 多选模式：旋转所有选中的箱子
            failed_boxes = []
            for box_id in self.visualization.selected_box_ids:
                if not self.rotate_box_with_boundary_check(pattern, box_id, clockwise, pallet_width, pallet_length):
                    failed_boxes.append(box_id)

            if failed_boxes:
                QMessageBox.warning(self, "警告", f"以下箱子旋转失败：{', '.join(failed_boxes)}")
        else:
            # 单选模式：旋转单个箱子
            selected_box_id = self.visualization.selected_box_id
            if not selected_box_id:
                return

            # 旋转箱子
            if not self.rotate_box_with_boundary_check(pattern, selected_box_id, clockwise, pallet_width, pallet_length):
                QMessageBox.warning(self, "警告", "旋转后会超出托盘边界")

        # 更新两个可视化组件
        self.visualization.update()

    def rotate_box_with_boundary_check(self, pattern, box_id, clockwise, max_x, max_y):
        """带边界检查的箱子旋转"""
        # 获取要旋转的箱子
        box_to_rotate = pattern.get_box_by_id(box_id)
        if not box_to_rotate:
            return False

        # 保存原始状态
        original_x = box_to_rotate.x
        original_y = box_to_rotate.y
        original_width = box_to_rotate.width
        original_length = box_to_rotate.length
        original_rotated = box_to_rotate.rotated
        original_rotation_angle = box_to_rotate.rotation_angle

        # 计算箱子中心点坐标（作为旋转基点）
        center_x = box_to_rotate.x + box_to_rotate.width / 2
        center_y = box_to_rotate.y + box_to_rotate.length / 2

        # 旋转箱子（交换宽度和长度）
        box_to_rotate.width, box_to_rotate.length = box_to_rotate.length, box_to_rotate.width

        # 更新旋转角度
        if clockwise:
            # 顺时针旋转90度
            box_to_rotate.rotation_angle = (box_to_rotate.rotation_angle + 90) % 360
        else:
            # 逆时针旋转90度
            box_to_rotate.rotation_angle = (box_to_rotate.rotation_angle - 90) % 360

        # 更新旋转状态（用于兼容性）
        box_to_rotate.rotated = (box_to_rotate.rotation_angle % 180) != 0

        # 根据新的尺寸重新计算位置，保持中心点不变
        box_to_rotate.x = center_x - box_to_rotate.width / 2
        box_to_rotate.y = center_y - box_to_rotate.length / 2

        # 检查是否超出托盘边界
        if (box_to_rotate.x < 0 or box_to_rotate.y < 0 or
            box_to_rotate.x + box_to_rotate.width > max_x or
            box_to_rotate.y + box_to_rotate.length > max_y):
            # 恢复原始状态
            box_to_rotate.x = original_x
            box_to_rotate.y = original_y
            box_to_rotate.width = original_width
            box_to_rotate.length = original_length
            box_to_rotate.rotated = original_rotated
            box_to_rotate.rotation_angle = original_rotation_angle
            return False

        # 旋转成功
        return True
    
    
    def setup_long_press(self):
        # 创建定时器
        self.up_timer = QTimer(self)
        self.down_timer = QTimer(self)
        self.left_timer = QTimer(self)
        self.right_timer = QTimer(self)
        
        # 设置定时器间隔
        self.up_timer.setInterval(100)
        self.down_timer.setInterval(100)
        self.left_timer.setInterval(100)
        self.right_timer.setInterval(100)

        self.up_btn.clicked.connect(lambda: self.on_move_box("up"))
        self.down_btn.clicked.connect(lambda: self.on_move_box("down"))
        self.left_btn.clicked.connect(lambda: self.on_move_box("left"))
        self.right_btn.clicked.connect(lambda: self.on_move_box("right"))
        
        # 连接定时器超时信号到移动函数
        self.up_timer.timeout.connect(lambda: self.on_move_box("up"))
        self.down_timer.timeout.connect(lambda: self.on_move_box("down"))
        self.left_timer.timeout.connect(lambda: self.on_move_box("left"))
        self.right_timer.timeout.connect(lambda: self.on_move_box("right"))

        # 连接按钮按下和释放事件
        self.up_btn.pressed.connect(self.up_timer.start)
        self.up_btn.released.connect(self.up_timer.stop)
        
        self.down_btn.pressed.connect(self.down_timer.start)
        self.down_btn.released.connect(self.down_timer.stop)
        
        self.left_btn.pressed.connect(self.left_timer.start)
        self.left_btn.released.connect(self.left_timer.stop)
        
        self.right_btn.pressed.connect(self.right_timer.start)
        self.right_btn.released.connect(self.right_timer.stop)

    def on_align_boxes(self, align_type):
        """对齐选中的箱子"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return
        
        # 获取选中的箱子ID列表
        if self.visualization.multi_select_mode:
            selected_box_ids = self.visualization.selected_box_ids
        else:
            selected_box_id = self.visualization.selected_box_id
            if selected_box_id:
                selected_box_ids = [selected_box_id]
            else:
                selected_box_ids = []
        
        if len(selected_box_ids) <= 1:
            QMessageBox.information(self, "提示", "请选择多个箱子进行对齐")
            return
        
        # 获取选中的箱子对象
        selected_boxes = []
        for box_id in selected_box_ids:
            for box in pattern.boxes:
                if box.id == box_id:
                    selected_boxes.append(box)
                    break
        
        # 计算选中箱子的边界（作为一个整体）
        min_x = min(box.x for box in selected_boxes)
        max_x = max(box.x + box.width for box in selected_boxes)
        min_y = min(box.y for box in selected_boxes)
        max_y = max(box.y + box.length for box in selected_boxes)
        
        # 临时存储移动后的箱子位置，用于检测重叠
        new_positions = {}
        
        # 根据对齐类型执行不同的对齐操作
        if align_type == 'top': 
            # 顶对齐：所有箱子与最高的箱子对齐
            # 找到最高的箱子（y值最小的）
            top_box = min(selected_boxes, key=lambda box: box.y)
            target_y = top_box.y
            
            # 计算每个箱子需要移动的距离
            for box in selected_boxes:
                if box.id != top_box.id:  # 不移动参考箱子
                    offset_y = target_y - box.y
                    new_positions[box.id] = (box.x, box.y + offset_y)
        
        elif align_type == 'bottom':
            # 底对齐：所有箱子与最低的箱子对齐
            # 找到最低的箱子（y+length值最大的）
            bottom_box = max(selected_boxes, key=lambda box: box.y + box.length)
            target_bottom = bottom_box.y + bottom_box.length
            
            # 计算每个箱子需要移动的距离
            for box in selected_boxes:
                if box.id != bottom_box.id:  # 不移动参考箱子
                    new_y = target_bottom - box.length
                    new_positions[box.id] = (box.x, new_y)
        
        elif align_type == 'left':
            # 左对齐：所有箱子与最左的箱子对齐
            # 找到最左的箱子（x值最小的）
            left_box = min(selected_boxes, key=lambda box: box.x)
            target_x = left_box.x
            
            # 计算每个箱子需要移动的距离
            for box in selected_boxes:
                if box.id != left_box.id:  # 不移动参考箱子
                    offset_x = target_x - box.x
                    new_positions[box.id] = (box.x + offset_x, box.y)
        
        elif align_type == 'right':
            # 右对齐：所有箱子与最右的箱子对齐
            # 找到最右的箱子（x+width值最大的）
            right_box = max(selected_boxes, key=lambda box: box.x + box.width)
            target_right = right_box.x + right_box.width
            
            # 计算每个箱子需要移动的距离
            for box in selected_boxes:
                if box.id != right_box.id:  # 不移动参考箱子
                    new_x = target_right - box.width
                    new_positions[box.id] = (new_x, box.y)
        
        # 检查移动后是否有重叠
        if new_positions:  # 只有当有箱子需要移动时才进行检查
            has_overlap = False
            
            # 先临时应用新位置进行检查
            backup_positions = {}
            for box in selected_boxes:
                if box.id in new_positions:
                    backup_positions[box.id] = (box.x, box.y)
                    box.x, box.y = new_positions[box.id]
            
            # 检查选中箱子之间和与未选中箱子之间是否有重叠
            for i, box1 in enumerate(pattern.boxes):
                for j, box2 in enumerate(pattern.boxes):
                    if i != j:  # 不与自己比较
                        if (box1.x < box2.x + box2.width and
                            box1.x + box1.width > box2.x and
                            box1.y < box2.y + box2.length and
                            box1.y + box1.length > box2.y):
                            has_overlap = True
                            break
                if has_overlap:
                    break
            
            # 恢复原来位置
            for box_id, (x, y) in backup_positions.items():
                for box in selected_boxes:
                    if box.id == box_id:
                        box.x, box.y = x, y
                        break
            
            if has_overlap:
                QMessageBox.warning(self, "警告", "对齐后会造成箱子重叠，操作取消")
                return
            
            # 应用新位置
            for box_id, (new_x, new_y) in new_positions.items():
                pattern.update_box_position(box_id, new_x, new_y)
        
        # 更新两个可视化组件
        self.visualization.update()
        self.plan_visualization.update()
    
    def on_distribute_boxes(self, distribute_type):
        """分布选中的箱子"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return

        # 获取选中的箱子ID列表
        if self.visualization.multi_select_mode:
            selected_box_ids = self.visualization.selected_box_ids
        else:
            selected_box_id = self.visualization.selected_box_id
            if selected_box_id:
                selected_box_ids = [selected_box_id]
            else:
                selected_box_ids = []

        if len(selected_box_ids) < 2:
            QMessageBox.information(self, "提示", "请选择至少两个箱子进行分布")
            return

        # 获取选中的箱子对象
        selected_boxes = []
        for box_id in selected_box_ids:
            for box in pattern.boxes:
                if box.id == box_id:
                    selected_boxes.append(box)
                    break

        if distribute_type == 'horizontal':
            self.distribute_boxes_horizontally(pattern, selected_boxes)
        elif distribute_type == 'vertical':
            self.distribute_boxes_vertically(pattern, selected_boxes)

        # 更新两个可视化组件
        self.visualization.update()
        self.plan_visualization.update()

    def distribute_boxes_horizontally(self, pattern, selected_boxes):
        """水平分布箱子"""
        if len(selected_boxes) < 2:
            return

        # 按X坐标排序
        selected_boxes.sort(key=lambda box: box.x)

        # 计算分布范围
        leftmost_box = selected_boxes[0]
        rightmost_box = selected_boxes[-1]

        start_x = leftmost_box.x
        end_x = rightmost_box.x + rightmost_box.width

        # 计算总的可用宽度
        total_width = end_x - start_x

        # 计算所有箱子的总宽度
        total_box_width = sum(box.width for box in selected_boxes)

        # 计算可用于间距的宽度
        available_spacing_width = total_width - total_box_width

        # 如果箱子数量大于1，计算间距
        if len(selected_boxes) > 1:
            spacing = available_spacing_width / (len(selected_boxes) - 1)
        else:
            spacing = 0

        # 重新分布箱子
        current_x = start_x
        for i, box in enumerate(selected_boxes):
            new_x = current_x

            # 边界检查
            if new_x < 0:
                new_x = 0
            elif new_x + box.width > pattern.pallet_width:
                new_x = pattern.pallet_width - box.width

            # 使用改进的安全位置查找
            new_x, _ = self.find_safe_position(pattern, box, new_x, box.y, selected_boxes)

            # 更新位置
            pattern.update_box_position(box.id, new_x, box.y)

            # 计算下一个箱子的起始位置
            current_x = new_x + box.width + spacing

    def distribute_boxes_vertically(self, pattern, selected_boxes):
        """垂直分布箱子"""
        if len(selected_boxes) < 2:
            return

        # 按Y坐标排序
        selected_boxes.sort(key=lambda box: box.y)

        # 计算分布范围
        topmost_box = selected_boxes[0]
        bottommost_box = selected_boxes[-1]

        start_y = topmost_box.y
        end_y = bottommost_box.y + bottommost_box.length

        # 计算总的可用高度
        total_height = end_y - start_y

        # 计算所有箱子的总高度
        total_box_height = sum(box.length for box in selected_boxes)

        # 计算可用于间距的高度
        available_spacing_height = total_height - total_box_height

        # 如果箱子数量大于1，计算间距
        if len(selected_boxes) > 1:
            spacing = available_spacing_height / (len(selected_boxes) - 1)
        else:
            spacing = 0

        # 重新分布箱子
        current_y = start_y
        for i, box in enumerate(selected_boxes):
            new_y = current_y

            # 边界检查
            if new_y < 0:
                new_y = 0
            elif new_y + box.length > pattern.pallet_length:
                new_y = pattern.pallet_length - box.length

            # 使用改进的安全位置查找
            _, new_y = self.find_safe_position(pattern, box, box.x, new_y, selected_boxes)

            # 更新位置
            pattern.update_box_position(box.id, box.x, new_y)

            # 计算下一个箱子的起始位置
            current_y = new_y + box.length + spacing

    def find_non_overlapping_position_x(self, pattern, target_box, desired_x, y, exclude_boxes):
        """查找不重叠的X位置"""
        # 检查是否与其他箱子重叠（排除选中的箱子）
        for box in pattern.boxes:
            if box.id == target_box.id or box in exclude_boxes:
                continue

            # 检查是否重叠
            if (desired_x < box.x + box.width and
                desired_x + target_box.width > box.x and
                y < box.y + box.length and
                y + target_box.length > box.y):

                # 有重叠，尝试调整位置
                # 尝试放在重叠箱子的右边
                new_x = box.x + box.width
                if new_x + target_box.width <= pattern.pallet_width:
                    return self.find_non_overlapping_position_x(pattern, target_box, new_x, y, exclude_boxes)

                # 尝试放在重叠箱子的左边
                new_x = box.x - target_box.width
                if new_x >= 0:
                    return self.find_non_overlapping_position_x(pattern, target_box, new_x, y, exclude_boxes)

        return desired_x

    def find_non_overlapping_position_y(self, pattern, target_box, x, desired_y, exclude_boxes):
        """查找不重叠的Y位置"""
        # 检查是否与其他箱子重叠（排除选中的箱子）
        for box in pattern.boxes:
            if box.id == target_box.id or box in exclude_boxes:
                continue

            # 检查是否重叠
            if (x < box.x + box.width and
                x + target_box.width > box.x and
                desired_y < box.y + box.length and
                desired_y + target_box.length > box.y):

                # 有重叠，尝试调整位置
                # 尝试放在重叠箱子的下边
                new_y = box.y + box.length
                if new_y + target_box.length <= pattern.pallet_length:
                    return self.find_non_overlapping_position_y(pattern, target_box, x, new_y, exclude_boxes)

                # 尝试放在重叠箱子的上边
                new_y = box.y - target_box.length
                if new_y >= 0:
                    return self.find_non_overlapping_position_y(pattern, target_box, x, new_y, exclude_boxes)

        return desired_y

    def on_flip_boxes(self, flip_type):
        """翻转选中的箱子"""
        # 获取当前方案
        pattern = self.pattern_manager.get_current_pattern()
        if not pattern:
            return

        # 获取选中的箱子ID列表
        if self.visualization.multi_select_mode:
            selected_box_ids = self.visualization.selected_box_ids
        else:
            selected_box_id = self.visualization.selected_box_id
            if selected_box_id:
                selected_box_ids = [selected_box_id]
            else:
                selected_box_ids = []

        if not selected_box_ids:
            QMessageBox.information(self, "提示", "请选择要翻转的箱子")
            return

        # 获取选中的箱子对象
        selected_boxes = []
        for box_id in selected_box_ids:
            for box in pattern.boxes:
                if box.id == box_id:
                    selected_boxes.append(box)
                    break

        if flip_type == 'vertical':
            self.flip_boxes_horizontally(pattern, selected_boxes)
        elif flip_type == 'horizontal': 
            self.flip_boxes_vertically(pattern, selected_boxes)

        # 更新两个可视化组件
        self.visualization.update()
        self.plan_visualization.update()

    def flip_boxes_horizontally(self, pattern, selected_boxes):
        """水平翻转箱子"""
        if not selected_boxes:
            return

        # 计算选中箱子的边界
        min_x = min(box.x for box in selected_boxes)
        max_x = max(box.x + box.width for box in selected_boxes)
        import math
        center_x = math.floor((min_x + max_x) / 2)

        # 存储新位置，先计算所有新位置再统一更新
        new_positions = []

        for box in selected_boxes:
            # 计算翻转后的新X位置
            new_x = 2 * center_x - (box.x + box.width)

            # 边界检查
            if new_x < 0:
                new_x = 0
            elif new_x + box.width > pattern.pallet_width:
                new_x = pattern.pallet_width - box.width

            # 使用改进的安全位置查找
            new_x, _ = self.find_safe_position(pattern, box, new_x, box.y, selected_boxes)

            new_positions.append((box.id, new_x, box.y))

        # 统一更新所有位置
        for box_id, new_x, new_y in new_positions:
            pattern.update_box_position(box_id, new_x, new_y)

    def flip_boxes_vertically(self, pattern, selected_boxes):
        """垂直翻转箱子"""
        if not selected_boxes:
            return

        # 计算选中箱子的边界
        min_y = min(box.y for box in selected_boxes)
        max_y = max(box.y + box.length for box in selected_boxes)
        import math
        center_y = math.floor((min_y + max_y) / 2)

        # 存储新位置，先计算所有新位置再统一更新
        new_positions = []

        for box in selected_boxes:
            # 计算翻转后的新Y位置
            new_y = 2 * center_y - (box.y + box.length)

            # 边界检查
            if new_y < 0:
                new_y = 0
            elif new_y + box.length > pattern.pallet_length:
                new_y = pattern.pallet_length - box.length

            # 使用改进的安全位置查找
            _, new_y = self.find_safe_position(pattern, box, box.x, new_y, selected_boxes)

            new_positions.append((box.id, box.x, new_y))

        # 统一更新所有位置
        for box_id, new_x, new_y in new_positions:
            pattern.update_box_position(box_id, new_x, new_y)

    def check_box_overlap(self, box1, box2):
        """检查两个箱子是否重叠"""
        return (box1.x < box2.x + box2.width and
                box1.x + box1.width > box2.x and
                box1.y < box2.y + box2.length and
                box1.y + box1.length > box2.y)

    def find_safe_position(self, pattern, target_box, desired_x, desired_y, exclude_boxes):
        """查找安全的不重叠位置"""
        # 首先检查期望位置是否安全
        if self.is_position_safe(pattern, target_box, desired_x, desired_y, exclude_boxes):
            return desired_x, desired_y

        # 如果期望位置不安全，尝试在附近找到安全位置
        search_radius = 50  # 搜索半径
        step = 10  # 搜索步长

        for radius in range(step, search_radius + 1, step):
            # 在当前半径的圆周上搜索
            for angle in range(0, 360, 30):  # 每30度搜索一次
                import math
                offset_x = radius * math.cos(math.radians(angle))
                offset_y = radius * math.sin(math.radians(angle))

                test_x = desired_x + offset_x
                test_y = desired_y + offset_y

                # 边界检查
                if (test_x >= 0 and test_x + target_box.width <= pattern.pallet_width and
                    test_y >= 0 and test_y + target_box.length <= pattern.pallet_length):

                    if self.is_position_safe(pattern, target_box, test_x, test_y, exclude_boxes):
                        return test_x, test_y

        # 如果找不到安全位置，返回边界内的位置
        safe_x = max(0, min(desired_x, pattern.pallet_width - target_box.width))
        safe_y = max(0, min(desired_y, pattern.pallet_length - target_box.length))
        return safe_x, safe_y

    def is_position_safe(self, pattern, target_box, x, y, exclude_boxes):
        """检查位置是否安全（不重叠且在边界内）"""
        # 边界检查
        if (x < 0 or y < 0 or
            x + target_box.width > pattern.pallet_width or
            y + target_box.length > pattern.pallet_length):
            return False

        # 重叠检查
        for box in pattern.boxes:
            if box.id == target_box.id or box in exclude_boxes:
                continue

            if (x < box.x + box.width and
                x + target_box.width > box.x and
                y < box.y + box.length and
                y + target_box.length > box.y):
                return False

        return True

    def on_multi_select_toggled(self, checked):
        """处理多选模式切换"""
        # 设置两个可视化组件的多选模式
        self.visualization.set_multi_select_mode(checked)
        self.plan_visualization.set_multi_select_mode(checked)
        
        # 清空选中状态，确保两个组件状态一致
        if checked:
            # 进入多选模式
            if self.visualization.selected_box_id:
                # 如果当前有选中的箱子，将其添加到多选列表中
                self.visualization.selected_box_ids = [self.visualization.selected_box_id]
                self.plan_visualization.selected_box_ids = [self.visualization.selected_box_id]
            else:
                # 否则清空选中列表
                self.visualization.selected_box_ids = []
                self.plan_visualization.selected_box_ids = []
        else:
            # 退出多选模式，清空多选列表
            self.visualization.selected_box_ids = []
            self.plan_visualization.selected_box_ids = []
            
        # 更新两个可视化组件
        self.visualization.update()
        self.plan_visualization.update()
        
        # 根据当前选中状态更新删除按钮状态
        has_selection = (self.visualization.selected_box_id is not None or 
                        len(self.visualization.selected_box_ids) > 0)
        self.delete_box_btn.setEnabled(has_selection)

    def on_box_number_changed(self, old_id, new_id):
        """处理箱子编号变更事件"""
        # 这里可以添加额外的处理逻辑
        # 例如更新其他界面元素、记录变更历史等
        # 目前只需要通过可视化组件的update方法更新显示即可
        self.visualization.update()
        
        # 同步更新plan_visualization
        self.plan_visualization.update()

    def on_next_plan(self):
        """处理下一个方案事件"""
        self.pattern_manager.next_plan()
        self.update_ui()

    def on_prev_plan(self):
        """处理上一个方案事件"""
        self.pattern_manager.prev_plan()
        self.update_ui()

    def switch_page(self, index):
        """切换页面，并在切换到垛型页面时加载保存的配置"""
        self.stackedWidget.setCurrentIndex(index)
        
        # 更新按钮组的选中状态
        if hasattr(self, 'page_button_group'):
            button = self.page_button_group.button(index)
            if button:
                button.setChecked(True)
                self.update_page_button_styles()

        # 如果切换到垛型页面(index=2)，需要重新加载垛型配置
        if index == 2:
            formula_id = self.get_current_formula_id()
            if formula_id:
                # 重新加载垛型配置，包括层样式选择
                self.load_stack_config_from_db(formula_id)

    # 旧的初始化垛型选择方法已移除，使用新的方法

    # 修改后的load_stack_config方法
    def load_stack_config(self):
        """动态加载垛层配置，使用两列布局"""
        layer_l = self.stack_layer_spinBox_l.value()
        layer_r = self.stack_layer_spinBox_r.value()

        # 如果两个值都小于1，则不进行操作
        if layer_r < 1 and layer_l < 1:
            return

        # 获取当前配方的层样式列表
        formula_id = self.get_current_formula_id()
        if formula_id:
            patterns_list = self.get_formula_layer_style_names(formula_id)
        else:
            patterns_list = []

        # 如果没有层样式，使用默认选项
        if not patterns_list:
            patterns_list = ["无层样式"]

        # 处理左侧滚动区域
        if layer_l >= 1:
            self.setup_two_column_combos(self.scrollArea_l, layer_l, patterns_list, "left")

        # 处理右侧滚动区域
        if layer_r >= 1:
            self.setup_two_column_combos(self.scrollArea_r, layer_r, patterns_list, "right")

    def setup_two_column_combos(self, scroll_area, count, items, prefix):
        """在滚动区域中设置两列的QComboBox布局"""
        try:
            # 临时隐藏滚动区域以减少视觉跳动
            scroll_area.setVisible(False)

            # 获取或创建滚动区域的内容控件
            content_widget = scroll_area.widget()
            if content_widget is None:
                content_widget = QWidget()
                scroll_area.setWidget(content_widget)

            # 检查是否需要应用重复模式
            apply_repeat = False
            repeat_pattern = []
            if prefix == "left" and self.repeat_enabled_left:
                apply_repeat = True
                repeat_pattern = self.repeat_pattern_left
            elif prefix == "right" and self.repeat_enabled_right:
                apply_repeat = True
                repeat_pattern = self.repeat_pattern_right

            # 清除现有布局和控件
            if content_widget.layout():
                # 删除旧布局中的所有控件
                old_layout = content_widget.layout()
                while old_layout.count():
                    item = old_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
                # 删除旧布局
                QWidget().setLayout(old_layout)

            # 创建网格布局
            grid_layout = QGridLayout(content_widget)
            grid_layout.setSpacing(10)
            grid_layout.setContentsMargins(10, 10, 10, 10)

            # 计算每列的项目数
            items_per_column = (count + 1) // 2

            # 创建两列布局
            for i in range(count):
                # 确定行和列
                col = 0 if i < items_per_column else 1
                row = i if i < items_per_column else i - items_per_column

                # 添加层标签
                label = QLabel(f"层 {i + 1}")
                label.setMinimumSize(40, 20)
                label.setMaximumSize(60, 30)
                # 使用统一的标签样式
                label.setStyleSheet(self.label_style)
                grid_layout.addWidget(label, row, col * 2)  # 每列占用两个网格单元

                # 添加下拉框
                combo = QComboBox()
                combo.setMinimumSize(120, 30)
                combo.setMaximumSize(200, 30)
                combo.setObjectName(f"{prefix}_layer_{i + 1}")
                combo.setStyleSheet("font-size: 12px;")

                # 先添加所有选项
                combo.addItems(items)

                # 如果需要应用重复模式，直接设置当前值
                if apply_repeat and repeat_pattern:
                    pattern_index = i % len(repeat_pattern)
                    combo.setCurrentText(repeat_pattern[pattern_index])

                grid_layout.addWidget(combo, row, col * 2 + 1)  # 放在标签右侧

            # 设置内容控件的布局
            content_widget.setLayout(grid_layout)

            # 确保滚动区域可以正确显示内容
            content_widget.adjustSize()
            scroll_area.setWidgetResizable(True)  # 确保滚动区域可以调整大小以适应内容

            # 重新显示滚动区域
            scroll_area.setVisible(True)

            # 强制更新界面
            QTimer.singleShot(50, content_widget.update)
            QTimer.singleShot(50, scroll_area.update)

        except Exception as e:

            # 确保滚动区域可见
            scroll_area.setVisible(True)

    def check_stack_config(self, state, frame_value, frame_name):
        """左右一致功能：将一侧的设置应用到另一侧"""
        if state.value == 0:
            return
            
        if state.value == 2:
            # 获取源框架中的所有下拉框值
            frame_v = self.findChild(QWidget, frame_value)
            if not frame_v:
                return
                
            combobox_left = frame_v.findChildren(QComboBox)
            value_new = [combo.currentText() for combo in combobox_left]
            
            # 将值应用到目标框架
            frame_n = self.findChild(QWidget, frame_name)
            if not frame_n:
                return
                
            combobox_right = frame_n.findChildren(QComboBox)
            for i, combo in enumerate(combobox_right):
                if i < len(value_new):
                    combo.setCurrentText(value_new[i])

    def Repright_check(self, state, frame_value):
        """处理重复开关状态变化"""
        # 设置重复状态和模式
        if frame_value == 'frame_5':
            self.repeat_enabled_left = (state.value == 2)
            if self.repeat_enabled_left:
                self.repeat_pattern_left = self.get_current_pattern_values(self.scrollArea_l)
                self.Repright_signal.emit(frame_value)
        elif frame_value == 'frame_6':
            self.repeat_enabled_right = (state.value == 2)
            if self.repeat_enabled_right:
                self.repeat_pattern_right = self.get_current_pattern_values(self.scrollArea_r)
                self.Repright_signal.emit(frame_value)

    def get_current_pattern_values(self, scroll_area):
        """获取当前所有控件的值作为重复模式"""
        pattern_values = []
        content_widget = scroll_area.widget()
        if content_widget:
            comboboxes = content_widget.findChildren(QComboBox)
            for combo in comboboxes:
                pattern_values.append(combo.currentText())
        return pattern_values

    # ==================== 配方数据管理方法 ====================

    def get_current_formula_id(self):
        """获取当前配方ID"""
        return getattr(self, 'current_editing_formula_id', None)

    def get_current_editing_formula_id(self):
        """获取当前正在编辑的配方ID"""
        return getattr(self, 'current_editing_formula_id', None)

    def set_current_editing_formula(self, formula_id, formula_name):
        """设置当前正在编辑的配方"""
        self.current_editing_formula_id = formula_id
        self.current_editing_formula_name = formula_name

        # 加载配方数据
        self.load_formula_data(formula_id)

        # 更新层选择器
        self.update_layer_selector(formula_id)

        # 注意：不在这里调用stacktypepe_init()，因为load_formula_data已经处理了垛型配置加载

        # 更新界面标题或提示
        if hasattr(self, 'setWindowTitle'):
            self.setWindowTitle(f"层样式设置 - 编辑配方: {formula_name}")

    def get_current_timestamp(self):
        """获取当前时间戳"""
        return datetime.now().isoformat()

    # ==================== 层样式保存和加载方法 ====================

    def on_save_all_layer_styles(self):
        """保存当前配方的层样式 - 点击save_plan按钮触发"""
        try:
            # 获取当前配方ID
            formula_id = self.get_current_formula_id()
            if not formula_id:
                QMessageBox.warning(self, "保存失败", "未找到当前配方信息")
                return

            # 获取配方名称
            formula_name = getattr(self, 'current_editing_formula_name', f'配方{formula_id}')

            # 获取所有有效的层样式方案
            patterns = self.pattern_manager.patterns
            valid_patterns = [p for p in patterns if p.boxes]  # 只考虑有箱子的方案

            if not valid_patterns:
                QMessageBox.warning(self, "保存失败", "没有可保存的层样式数据，请先添加箱子")
                return

            # 直接保存所有方案为不同层，每个方案对应一层
            self.save_patterns_as_layers(formula_id, formula_name, valid_patterns)

        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存层样式配置时发生异常：{str(e)}")

    def save_patterns_as_layers(self, formula_id, formula_name, valid_patterns):
        """将方案保存为层样式"""
        try:
            # 显示保存进度对话框
            progress_dialog = QProgressDialog("正在保存层样式数据...", "取消", 0, len(valid_patterns), self)
            progress_dialog.setWindowTitle("保存进度")
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.setMinimumDuration(0)
            progress_dialog.setValue(0)

            # 获取配置参数
            auto_mode = 0  # 固定为手动模式
            gap_size = int(float(self.step_spin.text())) if self.step_spin.text() else 0
            rows_count = int(float(self.rows_spin_3.text())) if self.rows_spin_3.text() else 0
            cols_count = int(float(self.cols_spin_3.text())) if self.cols_spin_3.text() else 0
            row_spacing = int(float(self.row_spacing_spin_3.text())) if self.row_spacing_spin_3.text() else 0
            col_spacing = int(float(self.col_spacing_spin_3.text())) if self.col_spacing_spin_3.text() else 0

            # 确保表结构正确
            self.ensure_table_structure()

            # 删除现有的层样式配置（只删除当前配方的）
            delete_query = self.db_tool.execute_sql("DELETE FROM layer_style_configs WHERE formula_id=?", [formula_id])
            delete_success = not delete_query.lastError().isValid()
            logger.info(f"删除配方{formula_id}的层样式配置: {delete_success}")

            if not delete_success:
                logger.error(f"删除配方{formula_id}的层样式配置失败: {delete_query.lastError().text()}")

            # 验证删除结果
            check_query = self.db_tool.execute_sql("SELECT COUNT(*) FROM layer_style_configs WHERE formula_id=?", [formula_id])
            if hasattr(check_query, 'next') and check_query.next():
                remaining_count = check_query.value(0)
                logger.info(f"删除后剩余记录数: {remaining_count}")

            # 保存每个方案为一层
            success_count = 0

            for layer_index, pattern in enumerate(valid_patterns, 1):
                # 更新进度
                progress_dialog.setValue(layer_index - 1)
                if progress_dialog.wasCanceled():
                    break

                # 序列化当前方案数据，确保包含正确的名称
                pattern_data = self.serialize_pattern_data(pattern)

                # 创建层样式数据
                current_time = self.get_current_timestamp()
                layer_data = {
                    'formula_id': formula_id,
                    'layer_index': layer_index,  # 层索引
                    'pattern_data': pattern_data,  # 使用当前方案的数据
                    'auto_mode': auto_mode,
                    'gap_size': gap_size,
                    'rows_count': rows_count,
                    'cols_count': cols_count,
                    'row_spacing': row_spacing,
                    'col_spacing': col_spacing,
                    'created_at': current_time,
                    'updated_at': current_time
                }

                logger.info(f"准备插入层数据: formula_id={formula_id}, layer_index={layer_index}, pattern_name={pattern.name}")
                logger.info(f"层数据详情: auto_mode={auto_mode}, gap_size={gap_size}, pattern_data长度={len(pattern_data)}")
                logger.info(f"layer_data字段数量: {len(layer_data)}, 字段: {list(layer_data.keys())}")

                # 验证数据完整性
                if not pattern_data or pattern_data == '{}':
                    logger.info(f"方案'{pattern.name}'的数据为空，跳过保存")
                    continue

                # 插入层数据
                try:
                    # 先删除可能存在的重复记录
                    self.db_tool.execute_sql(
                        "DELETE FROM layer_style_configs WHERE formula_id=? AND layer_index=?",
                        [formula_id, layer_index]
                    )

                    # 直接使用SQL插入，避免参数匹配问题
                    sql = """INSERT INTO layer_style_configs
                             (formula_id, layer_index, pattern_data, auto_mode, gap_size,
                              rows_count, cols_count, row_spacing, col_spacing, created_at, updated_at)
                             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""

                    params = [
                        formula_id, layer_index, pattern_data, auto_mode, gap_size,
                        rows_count, cols_count, row_spacing, col_spacing, current_time, current_time
                    ]

                    query = self.db_tool.execute_sql(sql, params)

                    if query.lastError().isValid():
                        error_text = query.lastError().text()
                        logger.error(f"保存配方{formula_id}的方案'{pattern.name}'为第{layer_index}层失败: {error_text}")
                    else:
                        success_count += 1
                        logger.info(f"成功保存配方{formula_id}的方案'{pattern.name}'为第{layer_index}层")

                except Exception as insert_e:
                    logger.error("插入过程中发生异常:{insert_e}")
                    import traceback
                    traceback.print_exc()

            # 关闭进度对话框
            progress_dialog.setValue(len(valid_patterns))

            # 显示保存结果
            if success_count == len(valid_patterns):
                pattern_names = [pattern.name for pattern in valid_patterns]
                QMessageBox.information(
                    self,
                    "保存成功",
                    f"配方 '{formula_name}' 的层样式已成功保存\n"
                    f"共保存 {len(valid_patterns)} 个方案为 {len(valid_patterns)} 层\n"
                    f"方案列表: {', '.join(pattern_names)}"
                )
                # 保存后刷新界面，但保持当前层样式选择
                self.refresh_after_layer_save(formula_id)
            elif success_count > 0:
                QMessageBox.warning(
                    self,
                    "部分保存成功",
                    f"成功保存 {success_count}/{len(valid_patterns)} 个方案\n"
                    f"部分方案保存失败，请检查数据库连接"
                )
            else:
                QMessageBox.warning(self, "保存失败", "所有层样式方案保存失败，请检查控制台输出获取详细错误信息")

        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存层样式方案时发生异常：{str(e)}")
            import traceback
            traceback.print_exc()

    def serialize_pattern_data(self, pattern):
        """将方案数据序列化为JSON格式"""
        pattern_dict = {
            'name': pattern.name,
            'pallet_width': pattern.pallet_width,
            'pallet_length': pattern.pallet_length,
            'box_width': pattern.box_width,
            'box_length': pattern.box_length,
            'gap': pattern.gap,
            'auto_mode': pattern.auto_mode,
            'boxes': []
        }

        # 序列化箱子数据 - 保存中心点坐标
        for box in pattern.boxes:
            # 计算箱子中心点坐标
            center_x = box.x + box.width / 2
            center_y = box.y + box.length / 2

            box_dict = {
                'id': box.id,
                'center_x': center_x,  # 保存中心点X坐标
                'center_y': center_y,  # 保存中心点Y坐标
                'width': box.width,
                'length': box.length,
                'rotated': box.rotated,
                'rotation_angle': getattr(box, 'rotation_angle', 0),
                # 为了兼容性，也保存左上角坐标
                'x': box.x,
                'y': box.y
            }
            pattern_dict['boxes'].append(box_dict)

        return json.dumps(pattern_dict, ensure_ascii=False)

    def load_formula_data(self, formula_id):
        """加载指定配方的数据到界面"""
        try:
            # 加载箱子/托盘参数
            self.load_box_pallet_params(formula_id)

            # 加载该配方的所有层样式到方案管理器
            self.load_all_layer_styles(formula_id)

            # 加载垛型配置（包括层样式选择）
            self.load_stack_config_from_db(formula_id)

        except Exception as e:
            logger.error(f"加载配方数据失败: {e}")

    def load_box_pallet_params(self, formula_id):
        """加载箱子/托盘参数"""
        try:
            # 查询箱子/托盘参数
            params = self.db_tool.select("box_pallet_params", condition="formula_id=?", params=[formula_id])

            if params:
                param = params[0]

                # 只有当参数值大于0时才设置界面参数，避免覆盖用户输入的有效值
                if param.get('box_width', 0) > 0:
                    self.width_box.setText(str(param.get('box_width', 0)))
                if param.get('box_length', 0) > 0:
                    self.length_box.setText(str(param.get('box_length', 0)))
                if param.get('box_height', 0) > 0:
                    self.hight_box.setText(str(param.get('box_height', 0)))
                if param.get('box_weight', 0) > 0:
                    self.weight_box.setText(str(param.get('box_weight', 0)))

                if param.get('pallet_width', 0) > 0:
                    self.width_pallet.setText(str(param.get('pallet_width', 0)))
                if param.get('pallet_length', 0) > 0:
                    self.length_pallet.setText(str(param.get('pallet_length', 0)))
                if param.get('pallet_height', 0) > 0:
                    self.hight_pallet.setText(str(param.get('pallet_height', 0)))

        except Exception as e:
            logger.error(f"加载箱子/托盘参数失败: {e}")

    def load_layer_style_config(self, formula_id, layer_index=1):
        """加载指定配方的层样式配置"""
        try:
            # 查询指定配方和层的层样式配置
            configs = self.db_tool.select(
                "layer_style_configs",
                condition="formula_id=? AND layer_index=?",
                params=[formula_id, layer_index]
            )

            if not configs:
                # 如果没有找到指定层，尝试加载该配方的第一层
                configs = self.db_tool.select(
                    "layer_style_configs",
                    condition="formula_id=? ORDER BY layer_index LIMIT 1",
                    params=[formula_id]
                )

            if configs:
                config = configs[0]

                # 设置基本配置
                # auto_mode已移除，跳过设置
                self.step_spin.setText(str(config.get('gap_size', 10)))
                self.rows_spin_3.setText(str(config.get('rows_count', 0)))
                self.cols_spin_3.setText(str(config.get('cols_count', 0)))
                self.row_spacing_spin_3.setText(str(config.get('row_spacing', 0)))
                self.col_spacing_spin_3.setText(str(config.get('col_spacing', 0)))

                # 解析并加载层样式数据
                pattern_data_str = config.get('pattern_data', '{}')
                if pattern_data_str:
                    try:
                        pattern_data = json.loads(pattern_data_str)
                        self.load_pattern_from_data(pattern_data)

                        # 显示加载的层信息
                        loaded_layer = config.get('layer_index', 1)

                        # 查询该配方的总层数
                        total_layers = self.get_total_layers_count(formula_id)

                    except json.JSONDecodeError as e:
                        pass
            else:
                pass

        except Exception as e:
            logger.error(f"加载层样式配置失败: {e}")

    def load_pattern_from_data(self, pattern_data):
        """从数据加载方案到界面"""
        try:
            # 创建新的方案
            pattern_name = pattern_data.get('name', 'A')
            pallet_width = pattern_data.get('pallet_width', 1200)
            pallet_length = pattern_data.get('pallet_length', 1000)
            box_width = pattern_data.get('box_width', 300)
            box_length = pattern_data.get('box_length', 200)
            gap = pattern_data.get('gap', 10)
            auto_mode = pattern_data.get('auto_mode', True)

            # 创建方案
            pattern = PalletPattern(pattern_name, pallet_width, pallet_length, box_width, box_length, gap, auto_mode)

            # 加载箱子数据
            boxes_data = pattern_data.get('boxes', [])
            for box_data in boxes_data:
                # 优先使用中心点坐标，如果没有则使用左上角坐标
                if 'center_x' in box_data and 'center_y' in box_data:
                    # 从中心点坐标计算左上角坐标
                    center_x = box_data.get('center_x', 0)
                    center_y = box_data.get('center_y', 0)
                    width = box_data.get('width', box_width)
                    length = box_data.get('length', box_length)

                    # 计算左上角坐标，按照实际坐标计算并向下取整
                    import math
                    x = math.floor(center_x - width / 2)
                    y = math.floor(center_y - length / 2)
                else:
                    # 使用原有的左上角坐标（兼容旧数据）
                    x = box_data.get('x', 0)
                    y = box_data.get('y', 0)
                    width = box_data.get('width', box_width)
                    length = box_data.get('length', box_length)

                box = BoxItem(
                    box_data.get('id', 'box'),
                    x,
                    y,
                    width,
                    length
                )
                box.rotated = box_data.get('rotated', False)
                box.rotation_angle = box_data.get('rotation_angle', 0)
                pattern.boxes.append(box)

            # 清空现有方案并添加加载的方案
            self.pattern_manager.patterns.clear()
            self.pattern_manager.add_pattern(pattern)
            self.pattern_manager.current_pattern = pattern

            # 更新可视化
            self.visualization.set_pattern(pattern)
            self.plan_visualization.set_pattern(pattern)

        except Exception as e:
            pass

    def load_all_layer_styles(self, formula_id):
        """加载指定配方的所有层样式到方案管理器"""
        try:
            # 查询该配方的所有层样式配置
            configs = self.db_tool.select(
                "layer_style_configs",
                condition="formula_id=? ORDER BY layer_index",
                params=[formula_id]
            )

            if not configs:
                # 创建一个默认的A方案
                self.pattern_manager.patterns.clear()
                default_pattern = PalletPattern("A", 1200, 1000, 300, 200, 10)
                self.pattern_manager.add_pattern(default_pattern)
                self.pattern_manager.current_pattern = default_pattern
                return

            # 清空现有方案
            self.pattern_manager.patterns.clear()

            # 加载每个层样式为一个方案
            loaded_patterns = []
            for config in configs:
                try:
                    pattern_data_str = config.get('pattern_data', '{}')
                    pattern_data = json.loads(pattern_data_str)

                    # 创建方案
                    pattern_name = pattern_data.get('name', f"层样式{config.get('layer_index', 1)}")
                    pallet_width = pattern_data.get('pallet_width', 1200)
                    pallet_length = pattern_data.get('pallet_length', 1000)
                    box_width = pattern_data.get('box_width', 300)
                    box_length = pattern_data.get('box_length', 200)
                    gap = pattern_data.get('gap', 10)
                    auto_mode = pattern_data.get('auto_mode', True)

                    pattern = PalletPattern(pattern_name, pallet_width, pallet_length, box_width, box_length, gap, auto_mode)

                    # 加载箱子数据
                    boxes_data = pattern_data.get('boxes', [])
                    for box_data in boxes_data:
                        box = BoxItem(
                            box_data.get('id', 'box'),
                            box_data.get('x', 0),
                            box_data.get('y', 0),
                            box_data.get('width', box_width),
                            box_data.get('length', box_length)
                        )
                        box.rotated = box_data.get('rotated', False)
                        box.rotation_angle = box_data.get('rotation_angle', 0)
                        pattern.boxes.append(box)

                    # 添加到方案管理器
                    self.pattern_manager.add_pattern(pattern)
                    loaded_patterns.append(pattern_name)


                except json.JSONDecodeError as e:
                    continue
                except Exception as e:
                    continue

            # 如果有层样式数据，设置第一个为当前方案（默认显示A样式）
            if self.pattern_manager.patterns:
                # 总是设置第一个方案为当前方案（A样式）
                self.pattern_manager.current_pattern = self.pattern_manager.patterns[0]

                # 更新可视化组件显示
                self.visualization.set_pattern(self.pattern_manager.current_pattern)
                self.plan_visualization.set_pattern(self.pattern_manager.current_pattern)

                # 加载第一个方案的配置参数
                first_config = configs[0]
                # auto_mode已移除，跳过设置
                self.step_spin.setText(str(first_config.get('gap_size', 10)))
                self.rows_spin_3.setText(str(first_config.get('rows_count', 0)))
                self.cols_spin_3.setText(str(first_config.get('cols_count', 0)))
                self.row_spacing_spin_3.setText(str(first_config.get('row_spacing', 0)))
                self.col_spacing_spin_3.setText(str(first_config.get('col_spacing', 0)))

            else:
                # 如果没有层样式数据，保持空白状态
                self.pattern_manager.current_pattern = None
                self.visualization.set_pattern(None)
                self.plan_visualization.set_pattern(None)

        except Exception as e:
            import traceback
            traceback.print_exc()

    # ==================== 垛型配置方法 ====================

    def load_stack_config_from_db(self, formula_id):
        """从数据库加载垛型配置"""
        try:
            # 查询垛型配置
            configs = self.db_tool.select("stack_type_configs", condition="formula_id=?", params=[formula_id])

            if configs:
                config = configs[0]

                # 设置垛型参数
                left_layers = config.get('left_stack_layers', 1)
                right_layers = config.get('right_stack_layers', 1)

                self.stack_layer_spinBox_l.setValue(left_layers)
                self.stack_layer_spinBox_r.setValue(right_layers)
                self.Repright_check_l.setChecked(config.get('left_repeat_enabled', 0) == 1)
                self.Repright_check_r.setChecked(config.get('right_repeat_enabled', 0) == 1)
                self.stack_lrl_check.setChecked(config.get('left_right_consistent', 0) == 1)

                # 获取保存的层样式选择
                left_layer_styles = config.get('left_stack_layer_styles', '')
                right_layer_styles = config.get('right_stack_layer_styles', '')



                # 先初始化垛型选择器，确保使用最新的层样式数据
                self.stacktypepe_init()

                # 然后加载垛层配置界面
                self.load_stack_config()

                # 使用QTimer延迟设置，确保界面完全加载后再设置选择
                if left_layer_styles or right_layer_styles:
                    # 使用更长的延迟确保界面完全渲染
                    QTimer.singleShot(300, lambda: self.restore_stack_layer_selections(left_layer_styles, right_layer_styles))
            else:
                # 没有保存的垛型配置，初始化默认的垛型选择器
                self.stacktypepe_init()

        except Exception as e:
            pass

    def on_save_stack_config(self):
        """保存垛型配置"""
        try:
            # 获取当前配方ID
            formula_id = self.get_current_formula_id()

            # 获取垛型配置参数
            left_stack_layers = self.stack_layer_spinBox_l.value()
            right_stack_layers = self.stack_layer_spinBox_r.value()
            left_repeat_enabled = 1 if self.Repright_check_l.isChecked() else 0
            right_repeat_enabled = 1 if self.Repright_check_r.isChecked() else 0
            left_right_consistent = 1 if self.stack_lrl_check.isChecked() else 0

            # 获取左垛和右垛的层样式选择
            left_layer_styles = self.get_stack_layer_style_selections("left", left_stack_layers)
            right_layer_styles = self.get_stack_layer_style_selections("right", right_stack_layers)

            # 检查是否已存在该配方的垛型配置
            existing_config = self.db_tool.select(
                "stack_type_configs",
                condition="formula_id=?",
                params=[formula_id]
            )

            data = {
                'formula_id': formula_id,
                'left_stack_layers': left_stack_layers,
                'right_stack_layers': right_stack_layers,
                'left_repeat_enabled': left_repeat_enabled,
                'right_repeat_enabled': right_repeat_enabled,
                'left_right_consistent': left_right_consistent,
                'left_stack_layer_styles': left_layer_styles,
                'right_stack_layer_styles': right_layer_styles,
                'updated_at': self.get_current_timestamp()
            }

            if existing_config:
                # 更新现有记录
                success = self.db_tool.update(
                    "stack_type_configs",
                    data,
                    f"formula_id={formula_id}"
                )
            else:
                # 插入新记录
                data['created_at'] = self.get_current_timestamp()
                success = self.db_tool.insert("stack_type_configs", data)

            if success:
                QMessageBox.information(self, "保存成功", "垛型配置已成功保存到数据库")

                # 垛型配置保存后不需要重新加载数据，只需要发出更新信号
                self.formula_data_updated.emit(formula_id)

                # 刷新主界面的配方列表（不影响当前垛型选择）
                self.refresh_formula_interface(formula_id)


            else:
                QMessageBox.warning(self, "保存失败", "保存垛型配置时发生错误")

        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存垛型配置时发生异常：{str(e)}")

    def get_stack_layer_style_selections(self, side, layer_count):
        """获取垛型层样式选择"""
        try:
            layer_styles = []

            # 根据侧边获取对应的滚动区域
            if side == "left":
                scroll_area = self.scrollArea_l
            elif side == "right":
                scroll_area = self.scrollArea_r
            else:
                return json.dumps([])

            # 获取滚动区域的内容控件
            content_widget = scroll_area.widget()
            if not content_widget:
                return json.dumps([])

            # 遍历所有下拉框，获取选择的层样式
            for i in range(layer_count):
                combo_name = f"{side}_layer_{i + 1}"
                combo = content_widget.findChild(QComboBox, combo_name)
                if combo:
                    selected_style = combo.currentText()
                    layer_styles.append(selected_style)
                else:
                    layer_styles.append("无层样式")

            return json.dumps(layer_styles, ensure_ascii=False)

        except Exception as e:
            return json.dumps([])

    def set_stack_layer_style_selections(self, side, layer_styles_json):
        """设置垛型层样式选择"""
        try:
            if not layer_styles_json:
                return

            layer_styles = json.loads(layer_styles_json)

            # 根据侧边获取对应的滚动区域
            if side == "left":
                scroll_area = self.scrollArea_l
            elif side == "right":
                scroll_area = self.scrollArea_r
            else:
                return

            # 获取滚动区域的内容控件
            content_widget = scroll_area.widget()
            if not content_widget:
                return

            # 设置每个下拉框的选择
            for i, style_name in enumerate(layer_styles):
                combo_name = f"{side}_layer_{i + 1}"
                combo = content_widget.findChild(QComboBox, combo_name)
                if combo:
                    # 查找并设置对应的选项
                    index = combo.findText(style_name)
                    if index >= 0:
                        combo.setCurrentIndex(index)

        except Exception as e:
            import traceback
            traceback.print_exc()

    def restore_stack_layer_selections(self, left_layer_styles, right_layer_styles):
        """恢复垛型层样式选择 - 延迟执行以确保界面完全加载"""
        try:
            # 恢复左垛层样式选择
            if left_layer_styles:
                self.set_stack_layer_style_selections("left", left_layer_styles)

                # 验证设置结果
                self.verify_stack_layer_selections("left", left_layer_styles)

            # 恢复右垛层样式选择
            if right_layer_styles:
                self.set_stack_layer_style_selections("right", right_layer_styles)

                # 验证设置结果
                self.verify_stack_layer_selections("right", right_layer_styles)

        except Exception as e:
            import traceback
            traceback.print_exc()

    def verify_stack_layer_selections(self, side, expected_styles_json):
        """验证垛型层样式选择是否正确设置"""
        try:
            if not expected_styles_json:
                return

            expected_styles = json.loads(expected_styles_json)

            # 根据侧边获取对应的滚动区域
            if side == "left":
                scroll_area = self.scrollArea_l
            elif side == "right":
                scroll_area = self.scrollArea_r
            else:
                return

            # 获取滚动区域的内容控件
            content_widget = scroll_area.widget()
            if not content_widget:
                return

            # 验证每个下拉框的选择
            for i, expected_style in enumerate(expected_styles):
                combo_name = f"{side}_layer_{i + 1}"
                combo = content_widget.findChild(QComboBox, combo_name)
                if combo:
                    current_text = combo.currentText()
                    if current_text != expected_style:
                        # 尝试重新设置
                        index = combo.findText(expected_style)
                        if index >= 0:
                            combo.setCurrentIndex(index)

        except Exception as e:
            pass

    # ==================== 层样式查询和管理方法 ====================

    def get_formula_layer_style_names(self, formula_id):
        """从数据库获取指定配方的所有层样式名称"""
        try:
            # 查询该配方的所有层样式配置
            configs = self.db_tool.select(
                "layer_style_configs",
                fields=["layer_index", "pattern_data"],
                condition="formula_id=? ORDER BY layer_index",
                params=[formula_id]
            )

            if not configs:
                return []

            # 提取每个层样式的名称
            layer_names = []
            for config in configs:
                pattern_data_str = config.get('pattern_data', '{}')
                try:
                    pattern_data = json.loads(pattern_data_str)
                    # 获取方案名称，如果没有则使用默认名称
                    pattern_name = pattern_data.get('name', f"层样式{config.get('layer_index', 0)}")
                    layer_names.append(pattern_name)
                except json.JSONDecodeError:
                    # 如果JSON解析失败，使用默认名称
                    layer_names.append(f"层样式{config.get('layer_index', 0)}")

            return layer_names

        except Exception as e:
            return []

    def get_layer_style_by_name(self, formula_id, layer_style_name):
        """根据配方ID和层样式名称获取层样式数据"""
        try:
            # 查询该配方的所有层样式配置
            configs = self.db_tool.select(
                "layer_style_configs",
                condition="formula_id=? ORDER BY layer_index",
                params=[formula_id]
            )

            if not configs:
                return None

            # 查找匹配的层样式
            for config in configs:
                pattern_data_str = config.get('pattern_data', '{}')
                try:
                    pattern_data = json.loads(pattern_data_str)
                    pattern_name = pattern_data.get('name', f"层样式{config.get('layer_index', 0)}")

                    if pattern_name == layer_style_name:
                        return config

                except json.JSONDecodeError:
                    # 如果JSON解析失败，检查默认名称
                    default_name = f"层样式{config.get('layer_index', 0)}"
                    if default_name == layer_style_name:
                        return config

            return None

        except Exception as e:
            return None

    def get_total_layers_count(self, formula_id):
        """获取配方的总层数"""
        try:
            result = self.db_tool.execute_sql(
                "SELECT COUNT(*) as count FROM layer_style_configs WHERE formula_id=?",
                [formula_id]
            )
            if result and len(result) > 0:
                return result[0].get('count', 0)
            return 0
        except Exception as e:
            return 0

    def get_layer_list(self, formula_id):
        """获取配方的所有层索引列表"""
        try:
            configs = self.db_tool.select(
                "layer_style_configs",
                fields=["layer_index"],
                condition="formula_id=? ORDER BY layer_index",
                params=[formula_id]
            )
            return [config['layer_index'] for config in configs]
        except Exception as e:
            return []

    # ==================== 垛型选择器更新方法 ====================

    def stacktypepe_init(self, preserve_selections=False):
        """初始化垛型选择 - 从数据库获取当前配方的层样式"""
        # 获取当前配方的层样式列表
        formula_id = self.get_current_formula_id()
        if formula_id:
            patterns_list = self.get_formula_layer_style_names(formula_id)
        else:
            patterns_list = []

        # 如果没有层样式，使用默认选项
        if not patterns_list:
            patterns_list = ["无层样式"]

        # 如果需要保留现有选择，先保存当前选择
        saved_left_selections = None
        saved_right_selections = None

        if preserve_selections:
            try:
                left_layers = self.stack_layer_spinBox_l.value() if hasattr(self, 'stack_layer_spinBox_l') else 1
                right_layers = self.stack_layer_spinBox_r.value() if hasattr(self, 'stack_layer_spinBox_r') else 1

                saved_left_selections = self.get_stack_layer_style_selections("left", left_layers)
                saved_right_selections = self.get_stack_layer_style_selections("right", right_layers)
            except:
                pass

        # 获取左右垛的层数
        left_layers = self.stack_layer_spinBox_l.value() if hasattr(self, 'stack_layer_spinBox_l') else 1
        right_layers = self.stack_layer_spinBox_r.value() if hasattr(self, 'stack_layer_spinBox_r') else 1

        # 更新左右垛的下拉框
        if hasattr(self, 'scrollArea_l'):
            self.setup_two_column_combos(self.scrollArea_l, left_layers, patterns_list, "left")
        if hasattr(self, 'scrollArea_r'):
            self.setup_two_column_combos(self.scrollArea_r, right_layers, patterns_list, "right")

        # 如果需要保留选择，恢复之前的选择
        if preserve_selections and (saved_left_selections or saved_right_selections):
            QTimer.singleShot(100, lambda: self.restore_saved_selections(saved_left_selections, saved_right_selections))

    def restore_saved_selections(self, left_selections, right_selections):
        """恢复保存的选择"""
        try:
            if left_selections:
                self.set_stack_layer_style_selections("left", left_selections)
            if right_selections:
                self.set_stack_layer_style_selections("right", right_selections)
        except Exception as e:
            pass

    def on_stack_layer_count_changed(self):
        """处理垛型层数变更 - 保留现有的层样式选择"""
        try:
            # 获取当前层数
            left_layers = self.stack_layer_spinBox_l.value()
            right_layers = self.stack_layer_spinBox_r.value()

            # 保存当前的层样式选择
            current_left_selections = self.get_current_stack_selections("left")
            current_right_selections = self.get_current_stack_selections("right")

            # 重新加载垛型配置界面（保留选择）
            self.load_stack_config()

            # 延迟恢复选择，确保界面完全加载
            QTimer.singleShot(100, lambda: self.restore_selections_after_layer_change(
                current_left_selections, current_right_selections, left_layers, right_layers
            ))

        except Exception as e:
            import traceback
            traceback.print_exc()



    def get_current_stack_selections(self, side):
        """获取当前垛型的层样式选择"""
        try:
            selections = []

            # 根据侧边获取对应的滚动区域
            if side == "left":
                scroll_area = self.scrollArea_l
            elif side == "right":
                scroll_area = self.scrollArea_r
            else:
                return selections

            # 获取滚动区域的内容控件
            content_widget = scroll_area.widget()
            if not content_widget:
                return selections

            # 获取所有下拉框的当前选择
            all_combos = content_widget.findChildren(QComboBox)
            for combo in all_combos:
                if combo.objectName().startswith(f"{side}_layer_"):
                    selections.append(combo.currentText())

            return selections

        except Exception as e:
            return []

    def restore_selections_after_layer_change(self, old_left_selections, old_right_selections, new_left_layers, new_right_layers):
        """层数变更后恢复选择"""
        try:
            # 恢复左垛选择
            if old_left_selections:
                self.restore_side_selections("left", old_left_selections, new_left_layers)

            # 恢复右垛选择
            if old_right_selections:
                self.restore_side_selections("right", old_right_selections, new_right_layers)

        except Exception as e:
            pass

    def restore_side_selections(self, side, old_selections, new_layer_count):
        """恢复单侧垛型的选择"""
        try:
            # 根据侧边获取对应的滚动区域
            if side == "left":
                scroll_area = self.scrollArea_l
            elif side == "right":
                scroll_area = self.scrollArea_r
            else:
                return

            # 获取滚动区域的内容控件
            content_widget = scroll_area.widget()
            if not content_widget:
                return

            # 恢复每一层的选择
            for i in range(new_layer_count):
                combo_name = f"{side}_layer_{i + 1}"
                combo = content_widget.findChild(QComboBox, combo_name)

                if combo:
                    # 如果旧选择中有这一层的数据，使用旧数据
                    if i < len(old_selections):
                        target_selection = old_selections[i]
                        index = combo.findText(target_selection)
                        if index >= 0:
                            combo.setCurrentIndex(index)
                    else:
                        # 如果是新增的层，使用第一个可用选项或最后一层的选择
                        if old_selections:
                            # 使用最后一层的选择作为新层的默认选择
                            last_selection = old_selections[-1]
                            index = combo.findText(last_selection)
                            if index >= 0:
                                combo.setCurrentIndex(index)

        except Exception as e:
            pass



    # 重复的load_stack_config方法已删除，使用上面的方法

    # ==================== 层选择器方法 ====================

    def setup_layer_selector(self):
        """设置层选择器"""
        try:
            # 创建层选择下拉框

            # 创建容器
            self.layer_selector_widget = QWidget(self)
            self.layer_selector_widget.setGeometry(10, 10, 200, 30)

            # 创建水平布局
            layout = QHBoxLayout(self.layer_selector_widget)
            layout.setContentsMargins(0, 0, 0, 0)

            # 创建标签
            label = QLabel("当前层:", self.layer_selector_widget)
            layout.addWidget(label)

            # 创建下拉框
            self.layer_selector = QComboBox(self.layer_selector_widget)
            self.layer_selector.setMinimumWidth(100)
            layout.addWidget(self.layer_selector)

            # 连接信号
            self.layer_selector.currentIndexChanged.connect(self.on_layer_selected)

            # 默认隐藏
            self.layer_selector_widget.hide()

        except Exception as e:
            pass

    def update_layer_selector(self, formula_id):
        """更新层选择器的选项 - 显示层样式名称"""
        try:
            if not hasattr(self, 'layer_selector'):
                return

            # 清空现有选项
            self.layer_selector.clear()

            # 获取层样式配置
            configs = self.db_tool.select(
                "layer_style_configs",
                fields=["layer_index", "pattern_data"],
                condition="formula_id=? ORDER BY layer_index",
                params=[formula_id]
            )

            if configs:
                # 添加层选项，显示层样式名称
                for config in configs:
                    layer_index = config.get('layer_index', 1)
                    pattern_data_str = config.get('pattern_data', '{}')

                    try:
                        pattern_data = json.loads(pattern_data_str)
                        pattern_name = pattern_data.get('name', f"层样式{layer_index}")
                        display_text = f"{pattern_name} (第{layer_index}层)"
                    except json.JSONDecodeError:
                        display_text = f"层样式{layer_index} (第{layer_index}层)"

                    self.layer_selector.addItem(display_text, layer_index)

                # 显示选择器
                self.layer_selector_widget.show()
            else:
                # 隐藏选择器
                self.layer_selector_widget.hide()

        except Exception as e:
            pass

    def on_layer_selected(self, index):
        """处理层选择事件"""
        try:
            if index < 0 or not hasattr(self, 'layer_selector'):
                return

            # 获取选中的层索引
            layer_index = self.layer_selector.itemData(index)

            # 获取当前配方ID
            formula_id = self.get_current_formula_id()

            # 加载选中层的数据
            self.load_layer_style_config(formula_id, layer_index)

        except Exception as e:
            pass

    def get_all_patterns_info(self):
        """获取所有方案的信息"""
        try:
            patterns = self.pattern_manager.patterns
            if not patterns:
                return "没有可用的层样式方案"

            info_lines = []
            for pattern in patterns:
                box_count = len(pattern.boxes)
                info_lines.append(f"方案 {pattern.name}: {box_count} 个箱子")

            return "\n".join(info_lines)

        except Exception as e:
            return f"获取方案信息失败: {e}"

    def ensure_table_structure(self):
        """确保layer_style_configs表结构正确"""
        try:
            # 检查表是否存在
            check_query = self.db_tool.execute_sql("SELECT name FROM sqlite_master WHERE type='table' AND name='layer_style_configs'")

            table_exists = False
            if hasattr(check_query, 'next') and check_query.next():
                table_exists = True

            if not table_exists:
                self.create_layer_style_table()
            else:
                # 检查表结构
                schema_query = self.db_tool.execute_sql("PRAGMA table_info(layer_style_configs)")
                columns = []

                if hasattr(schema_query, 'next') and schema_query.next():
                    while schema_query.isValid():
                        col_name = schema_query.value(1)
                        columns.append(col_name)
                        if not schema_query.next():
                            break

                expected_columns = ['id', 'formula_id', 'layer_index', 'pattern_data', 'auto_mode',
                                  'gap_size', 'rows_count', 'cols_count', 'row_spacing', 'col_spacing',
                                  'created_at', 'updated_at']

                if set(columns) != set(expected_columns):

                    # 备份数据
                    backup_data = []
                    backup_query = self.db_tool.execute_sql("SELECT * FROM layer_style_configs")
                    if hasattr(backup_query, 'next') and backup_query.next():
                        while backup_query.isValid():
                            row_data = {}
                            for i, col in enumerate(columns):
                                row_data[col] = backup_query.value(i)
                            backup_data.append(row_data)
                            if not backup_query.next():
                                break

                    # 删除旧表
                    self.db_tool.execute_sql("DROP TABLE layer_style_configs")

                    # 创建新表
                    self.create_layer_style_table()

                    # 恢复兼容的数据
                    for row in backup_data:
                        try:
                            compatible_data = {
                                'formula_id': row.get('formula_id'),
                                'layer_index': row.get('layer_index', 1),
                                'pattern_data': row.get('pattern_data', '{}'),
                                'auto_mode': row.get('auto_mode', 1),
                                'gap_size': row.get('gap_size', 10.0),
                                'rows_count': row.get('rows_count', 0),
                                'cols_count': row.get('cols_count', 0),
                                'row_spacing': row.get('row_spacing', 0.0),
                                'col_spacing': row.get('col_spacing', 0.0),
                                'created_at': row.get('created_at', self.get_current_timestamp()),
                                'updated_at': row.get('updated_at', self.get_current_timestamp())
                            }

                            if compatible_data['formula_id'] and compatible_data['pattern_data']:
                                self.db_tool.insert("layer_style_configs", compatible_data)
                        except:
                            continue

        except Exception as e:
            pass

    def create_layer_style_table(self):
        """创建layer_style_configs表"""
        create_sql = """CREATE TABLE layer_style_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            formula_id INTEGER NOT NULL,
            layer_index INTEGER DEFAULT 1,
            pattern_data TEXT NOT NULL,
            auto_mode INTEGER DEFAULT 1,
            gap_size REAL DEFAULT 10,
            rows_count INTEGER DEFAULT 0,
            cols_count INTEGER DEFAULT 0,
            row_spacing REAL DEFAULT 0,
            col_spacing REAL DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (formula_id) REFERENCES formulas(id) ON DELETE CASCADE,
            UNIQUE(formula_id, layer_index)
        )"""

        result = self.db_tool.execute_sql(create_sql)

    # ==================== 界面刷新方法 ====================

    def refresh_formula_interface(self, formula_id):
        """刷新配方界面数据，确保显示最新值"""
        try:
            # 重新加载配方数据到界面
            self.load_formula_data(formula_id)

            # 通知主界面刷新配方列表
            if hasattr(self, 'parent') and self.parent():
                main_window = self.parent()
                if hasattr(main_window, 'formula_page'):
                    # 刷新配方页面的数据
                    main_window.formula_page.load_formulas_from_database()

        except Exception as e:
            pass

    def refresh_after_save(self, formula_id, save_type=""):
        """保存后的统一刷新处理"""
        try:
            # 保存当前显示的层样式名称和更多信息
            current_pattern_name = None
            current_pattern_index = None
            if hasattr(self, 'pattern_manager') and self.pattern_manager.current_pattern:
                current_pattern_name = self.pattern_manager.current_pattern.name
                # 记录当前方案在列表中的索引
                try:
                    current_pattern_index = self.pattern_manager.patterns.index(self.pattern_manager.current_pattern)
                except ValueError:
                    current_pattern_index = None

            # 重新加载当前配方数据
            self.load_formula_data(formula_id)

            # 尝试恢复到原来的层样式
            pattern_restored = False
            if current_pattern_name and hasattr(self, 'pattern_manager') and self.pattern_manager.patterns:
                # 方法1: 按名称查找
                for pattern in self.pattern_manager.patterns:
                    if pattern.name == current_pattern_name:
                        self.pattern_manager.current_pattern = pattern
                        # 更新可视化显示
                        self.visualization.set_pattern(pattern)
                        self.plan_visualization.set_pattern(pattern)
                        pattern_restored = True
                        break

                # 方法2: 如果按名称找不到，尝试按索引恢复
                if not pattern_restored and current_pattern_index is not None:
                    if 0 <= current_pattern_index < len(self.pattern_manager.patterns):
                        pattern = self.pattern_manager.patterns[current_pattern_index]
                        self.pattern_manager.current_pattern = pattern
                        self.visualization.set_pattern(pattern)
                        self.plan_visualization.set_pattern(pattern)
                        pattern_restored = True

            # 如果没有成功恢复，确保有一个当前方案
            if not pattern_restored and hasattr(self, 'pattern_manager') and self.pattern_manager.patterns:
                if not self.pattern_manager.current_pattern:
                    # 只有在没有当前方案时才设置为第一个
                    self.pattern_manager.current_pattern = self.pattern_manager.patterns[0]
                    self.visualization.set_pattern(self.pattern_manager.current_pattern)
                    self.plan_visualization.set_pattern(self.pattern_manager.current_pattern)

            # 更新垛型选择器
            self.stacktypepe_init()

            # 更新层选择器
            if hasattr(self, 'update_layer_selector'):
                self.update_layer_selector(formula_id)

            # 发出数据更新信号
            self.formula_data_updated.emit(formula_id)

            # 刷新配方界面
            self.refresh_formula_interface(formula_id)

        except Exception as e:
            pass

    def refresh_after_layer_save(self, formula_id):
        """层样式保存后的专门刷新处理 - 保持当前层样式选择"""
        try:
            # 保存当前显示的层样式的完整信息
            current_pattern_name = None
            current_pattern_boxes_count = 0
            if hasattr(self, 'pattern_manager') and self.pattern_manager.current_pattern:
                current_pattern_name = self.pattern_manager.current_pattern.name
                current_pattern_boxes_count = len(self.pattern_manager.current_pattern.boxes)

            # 保存当前的pattern_manager引用
            old_pattern_manager = self.pattern_manager

            # 重新加载层样式数据
            self.load_all_layer_styles(formula_id)

            # 尝试恢复到原来的层样式
            pattern_restored = False
            if current_pattern_name and hasattr(self, 'pattern_manager') and self.pattern_manager.patterns:
                # 查找同名且箱子数量相同的层样式（更精确的匹配）
                for pattern in self.pattern_manager.patterns:
                    if (pattern.name == current_pattern_name and
                        len(pattern.boxes) == current_pattern_boxes_count):
                        self.pattern_manager.current_pattern = pattern
                        # 立即更新可视化显示
                        self.visualization.set_pattern(pattern)
                        self.plan_visualization.set_pattern(pattern)
                        pattern_restored = True
                        break

                # 如果精确匹配失败，尝试只按名称匹配
                if not pattern_restored:
                    for pattern in self.pattern_manager.patterns:
                        if pattern.name == current_pattern_name:
                            self.pattern_manager.current_pattern = pattern
                            self.visualization.set_pattern(pattern)
                            self.plan_visualization.set_pattern(pattern)
                            pattern_restored = True
                            break

            # 如果仍然没有恢复成功，保持原有的current_pattern
            if not pattern_restored and old_pattern_manager and old_pattern_manager.current_pattern:
                # 尝试在新加载的patterns中找到相同名称的pattern
                for pattern in self.pattern_manager.patterns:
                    if pattern.name == old_pattern_manager.current_pattern.name:
                        self.pattern_manager.current_pattern = pattern
                        self.visualization.set_pattern(pattern)
                        self.plan_visualization.set_pattern(pattern)
                        break

            # 更新垛型选择器（保留现有选择）
            self.stacktypepe_init(preserve_selections=True)

            # 发出数据更新信号
            self.formula_data_updated.emit(formula_id)

        except Exception as e:
            pass

    # ==================== 箱子/托盘参数保存方法 ====================

    def on_save_box_params(self):
        """保存箱子参数"""
        try:
            # 获取当前配方ID
            formula_id = self.get_current_formula_id()
            if not formula_id:
                QMessageBox.warning(self, "保存失败", "未找到当前配方信息")
                return

            # 获取箱子参数
            direction = self.box_direction.currentText()
            box_width = int(float(self.width_box.text())) if self.width_box.text() else 0
            box_length = int(float(self.length_box.text())) if self.length_box.text() else 0
            box_height = int(float(self.hight_box.text())) if self.hight_box.text() else 0  # 注意：UI中的属性名是hight_box（拼写错误）
            box_weight = int(float(self.weight_box.text())) if self.weight_box.text() else 0

            # 查询现有参数
            existing_params = self.db_tool.select(
                "box_pallet_params",
                condition="formula_id=?",
                params=[formula_id]
            )

            current_time = self.get_current_timestamp()

            if existing_params:
                # 更新现有记录
                update_data = {
                    'direction' :direction,
                    'box_width': box_width,
                    'box_length': box_length,
                    'box_height': box_height,
                    'box_weight': box_weight,
                    'updated_at': current_time
                }

                success = self.db_tool.update(
                    "box_pallet_params",
                    update_data,
                    f"formula_id={formula_id}"
                )
            else:
                # 插入新记录
                insert_data = {
                    'direction' :direction,
                    'formula_id': formula_id,
                    'box_width': box_width,
                    'box_length': box_length,
                    'box_height': box_height,
                    'box_weight': box_weight,
                    # 'pallet_width': 1200.0,  # 默认托盘参数
                    # 'pallet_length': 1200.0,
                    # 'pallet_height': 100.0,
                    'created_at': current_time,
                    'updated_at': current_time
                }

                success = self.db_tool.insert("box_pallet_params", insert_data)

            if success:
                QMessageBox.information(self, "保存成功", f"箱子参数已保存：{box_width}x{box_length}x{box_height} ({box_weight}kg)")

                # 更新当前方案的箱子尺寸
                pattern = self.pattern_manager.get_current_pattern()
                if pattern:
                    pattern.box_width = box_width
                    pattern.box_length = box_length

                # 更新可视化显示
                self.visualization.update()
                self.plan_visualization.update()

                # 不调用refresh_after_save，避免重新加载参数
            else:
                QMessageBox.warning(self, "保存失败", "保存箱子参数时发生错误")

        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存箱子参数时发生异常：{str(e)}")

    def on_save_pallet_params(self):
        """保存托盘参数"""
        try:
            # 获取当前配方ID
            formula_id = self.get_current_formula_id()
            if not formula_id:
                QMessageBox.warning(self, "保存失败", "未找到当前配方信息")
                return

            # 获取托盘参数
            pallet_width = int(float(self.width_pallet.text())) if self.width_pallet.text() else 0
            pallet_length = int(float(self.length_pallet.text())) if self.length_pallet.text() else 0
            pallet_height = int(float(self.hight_pallet.text())) if self.hight_pallet.text() else 0 

            # 查询现有参数
            existing_params = self.db_tool.select(
                "box_pallet_params",
                condition="formula_id=?",
                params=[formula_id]
            )

            current_time = self.get_current_timestamp()

            if existing_params:
                # 更新现有记录
                update_data = {
                    'pallet_width': pallet_width,
                    'pallet_length': pallet_length,
                    'pallet_height': pallet_height,
                    'updated_at': current_time
                }

                success = self.db_tool.update(
                    "box_pallet_params",
                    update_data,
                    f"formula_id={formula_id}"
                )
            else:
                # 插入新记录
                insert_data = {
                    'formula_id': formula_id,
                    # 'box_width': 100.0,  # 默认箱子参数
                    # 'box_length': 100.0,
                    # 'box_height': 100.0,
                    # 'box_weight': 100.0,
                    'pallet_width': pallet_width,
                    'pallet_length': pallet_length,
                    'pallet_height': pallet_height,
                    'created_at': current_time,
                    'updated_at': current_time
                }

                success = self.db_tool.insert("box_pallet_params", insert_data)

            if success:
                QMessageBox.information(self, "保存成功", f"托盘参数已保存：{pallet_width}x{pallet_length}x{pallet_height}")

                # 更新当前方案的托盘尺寸
                pattern = self.pattern_manager.get_current_pattern()
                if pattern:
                    pattern.pallet_width = pallet_width
                    pattern.pallet_length = pallet_length

                # 更新可视化显示
                self.visualization.update()
                self.plan_visualization.update()

                # 不调用refresh_after_save，避免重新加载参数
            else:
                QMessageBox.warning(self, "保存失败", "保存托盘参数时发生错误")

        except Exception as e:
            QMessageBox.critical(self, "保存错误", f"保存托盘参数时发生异常：{str(e)}")

    def on_page_button_clicked(self, button):
        """处理页面切换按钮点击事件"""
        # 获取按钮在按钮组中的ID
        button_id = self.page_button_group.id(button)
        
        # 调用原来的switch_page方法
        self.switch_page(button_id)
        
        # 更新按钮样式
        self.update_page_button_styles()
    
    def setup_page_button_styles(self):
        """设置页面切换按钮的样式"""
        # 设置未选中状态的样式（默认样式）
        default_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                color: rgb(85, 85, 85);
                font-size:16px
            }
        """
        
        # 设置选中状态的样式
        selected_style = """
            QPushButton {
                background-image: url(:/layerstyle/layerstyle/button.png);
                background-color: #FFFFFF;
                box-shadow: 4px 0px 0px 0px #E2EAF5;
                border-radius: 20px 20px 20px 20px;
                color: rgb(255, 255, 255);
            }
        """
        
        # 初始化样式
        self.laystyle_btn.setStyleSheet(default_style)
        self.boxpallet_btn.setStyleSheet(selected_style)  # 默认选中
        self.stacktype_btn.setStyleSheet(default_style)
    
    def update_page_button_styles(self):
        """更新页面切换按钮的样式"""
        # 设置未选中状态的样式
        default_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                color: rgb(85, 85, 85);
            }
        """
        # 设置选中状态的样式
        selected_style = """
            QPushButton {
                background-image: url(:/layerstyle/layerstyle/button.png);
                background-color: #FFFFFF;
                box-shadow: 4px 0px 0px 0px #E2EAF5;
                border-radius: 20px 20px 20px 20px;
                color: rgb(255, 255, 255);
            }
        """
        
        # 为所有按钮设置默认样式
        self.laystyle_btn.setStyleSheet(default_style)
        self.boxpallet_btn.setStyleSheet(default_style)
        self.stacktype_btn.setStyleSheet(default_style)
        
        # 为选中的按钮设置选中样式
        checked_button = self.page_button_group.checkedButton()
        if checked_button:
            checked_button.setStyleSheet(selected_style)

    @staticmethod
    def safe_int_convert(text_value, default=0):
        """安全地将文本转换为整数"""
        if not text_value or text_value.strip() == "":
            return default
        try:
            # 先转换为浮点数，再转换为整数
            return int(float(text_value.strip()))
        except (ValueError, TypeError):
            return default

