<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>IOWidget</class>
 <widget class="QWidget" name="IOWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>830</width>
    <height>486</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>智能分拣系统 - IO控制</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background-color: #e8f0ff;
    font-family: &quot;Microsoft YaHei&quot;, Arial, sans-serif;
}

QFrame {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #d0d7de;
}

QPushButton {
    border: none;
    border-radius: 8px;
    font-weight: bold;
    font-size: 12px;
    color: white;
}

QLabel {
    color: #2d3748;
    font-size: 12px;
}

QComboBox {
    background-color: #f1f5f9;
    border: 1px solid #cbd5e0;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 11px;
    color: #374151;
    min-height: 24px;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    width: 12px;
    height: 12px;
}

.status-indicator-active {
    background-color: #22c55e;
    border-radius: 12px;
    min-width: 24px;
    min-height: 24px;
    max-width: 24px;
    max-height: 24px;
}

.status-indicator-inactive {
    background-color: #9ca3af;
    border-radius: 12px;
    min-width: 24px;
    min-height: 24px;
    max-width: 24px;
    max-height: 24px;
}

.save-button {
    background-color: #22c55e;
    color: white;
    min-height: 40px;
    font-size: 14px;
    font-weight: bold;
    border-radius: 8px;
}</string>
  </property>
  <layout class="QHBoxLayout" name="mainHorizontalLayout">
   <property name="spacing">
    <number>16</number>
   </property>
   <property name="leftMargin">
    <number>16</number>
   </property>
   <property name="topMargin">
    <number>16</number>
   </property>
   <property name="rightMargin">
    <number>16</number>
   </property>
   <property name="bottomMargin">
    <number>16</number>
   </property>
   <item>
    <widget class="QFrame" name="diFrame">
     <property name="minimumSize">
      <size>
       <width>390</width>
       <height>450</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>390</width>
       <height>450</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="diMainLayout">
      <property name="spacing">
       <number>16</number>
      </property>
      <property name="leftMargin">
       <number>16</number>
      </property>
      <property name="topMargin">
       <number>16</number>
      </property>
      <property name="rightMargin">
       <number>16</number>
      </property>
      <property name="bottomMargin">
       <number>16</number>
      </property>
      <item>
       <widget class="QLabel" name="diTitleLabel">
        <property name="styleSheet">
         <string notr="true">font-size: 20px; font-weight: bold; color: #1f2937;</string>
        </property>
        <property name="text">
         <string>DI</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QVBoxLayout" name="diItemsLayout">
        <property name="spacing">
         <number>16</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="diRow1Layout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="diStartLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="diStartControlLayout">
              <item>
               <widget class="QLabel" name="diStartLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>启动</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="diStartSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="diStartStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="diStartComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="diPauseLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="diPauseControlLayout">
              <item>
               <widget class="QLabel" name="diPauseLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>暂停</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="diPauseSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="diPauseStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="diPauseComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="diRow2Layout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="diStopLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="diStopControlLayout">
              <item>
               <widget class="QLabel" name="diStopLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>停止</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="diStopSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="diStopStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="diStopComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="diGrabSignalLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="diGrabSignalControlLayout">
              <item>
               <widget class="QLabel" name="diGrabSignalLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>抓取信号</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="diGrabSignalSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="diGrabSignalStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="diGrabSignalComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="diRow3Layout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="diPressureDetectLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="diPressureDetectControlLayout">
              <item>
               <widget class="QLabel" name="diPressureDetectLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>气压检测</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="diPressureDetectSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="diPressureDetectStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="diPressureDetectComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="diVacuumDetectLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="diVacuumDetectControlLayout">
              <item>
               <widget class="QLabel" name="diVacuumDetectLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>真空检测</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="diVacuumDetectSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="diVacuumDetectStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="diVacuumDetectComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="diRow4Layout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="diLeftTrayDetectLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="diLeftTrayDetectControlLayout">
              <item>
               <widget class="QLabel" name="diLeftTrayDetectLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>左垛盘检测</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="diLeftTrayDetectSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="diLeftTrayDetectStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="diLeftTrayDetectComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="diRightTrayDetectLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="diRightTrayDetectControlLayout">
              <item>
               <widget class="QLabel" name="diRightTrayDetectLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>右垛盘检测</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="diRightTrayDetectSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="diRightTrayDetectStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="diRightTrayDetectComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="diVerticalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="diSaveButton">
        <property name="styleSheet">
         <string notr="true">background-color: #22c55e; color: white; min-height: 40px; font-size: 14px; font-weight: bold; border-radius: 8px;
background:url(:/io/io/IO保存.png);
background-repeat: no-repeat;
background-position: center;
background-attachment: fixed;</string>
        </property>
        <property name="text">
         <string>保存</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="doFrame">
     <property name="minimumSize">
      <size>
       <width>390</width>
       <height>450</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>390</width>
       <height>450</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="doMainLayout">
      <property name="spacing">
       <number>16</number>
      </property>
      <property name="leftMargin">
       <number>16</number>
      </property>
      <property name="topMargin">
       <number>16</number>
      </property>
      <property name="rightMargin">
       <number>16</number>
      </property>
      <property name="bottomMargin">
       <number>16</number>
      </property>
      <item>
       <widget class="QLabel" name="doTitleLabel">
        <property name="styleSheet">
         <string notr="true">font-size: 20px; font-weight: bold; color: #1f2937;</string>
        </property>
        <property name="text">
         <string>DO</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QVBoxLayout" name="doItemsLayout">
        <property name="spacing">
         <number>17</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="doRow1Layout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="doStartLightLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="doStartLightControlLayout">
              <item>
               <widget class="QLabel" name="doStartLightLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>启动灯</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="doStartLightSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="doStartLightStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="doStartLightComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="doPauseLightLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="doPauseLightControlLayout">
              <item>
               <widget class="QLabel" name="doPauseLightLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>暂停灯</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="doPauseLightSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="doPauseLightStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="doPauseLightComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="doRow2Layout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="doStopLightLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="doStopLightControlLayout">
              <item>
               <widget class="QLabel" name="doStopLightLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>停止灯</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="doStopLightSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="doStopLightStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="doStopLightComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="doGrabLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="doGrabControlLayout">
              <item>
               <widget class="QLabel" name="doGrabLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>抓取</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="doGrabSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="doGrabStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="doGrabComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="doRow3Layout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="doReleaseLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="doReleaseControlLayout">
              <item>
               <widget class="QLabel" name="doReleaseLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>释放</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="doReleaseSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="doReleaseStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="doReleaseComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="doLeftRunningLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="doLeftRunningControlLayout">
              <item>
               <widget class="QLabel" name="doLeftRunningLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>左垛进行中</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="doLeftRunningSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="doLeftRunningStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="doLeftRunningComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="doRow4Layout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="doLeftTrayCleanLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="doLeftTrayCleanControlLayout">
              <item>
               <widget class="QLabel" name="doLeftTrayCleanLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>左垛盘清除</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="doLeftTrayCleanSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="doLeftTrayCleanStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="doLeftTrayCleanComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="doRightRunningLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="doRightRunningControlLayout">
              <item>
               <widget class="QLabel" name="doRightRunningLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>右垛运行中</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="doRightRunningSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="doRightRunningStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="doRightRunningComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="doRow5Layout">
          <property name="spacing">
           <number>20</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="doRightTrayCleanLayout">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="doRightTrayCleanControlLayout">
              <item>
               <widget class="QLabel" name="doRightTrayCleanLabel">
                <property name="styleSheet">
                 <string notr="true">font-weight: bold; font-size: 12px;
border:0px;</string>
                </property>
                <property name="text">
                 <string>右垛盘清除</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="doRightTrayCleanSpacer">
                <property name="orientation">
                 <enum>Qt::Orientation::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QPushButton" name="doRightTrayCleanStatusIndicator">
                <property name="styleSheet">
                 <string notr="true">background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QComboBox" name="doRightTrayCleanComboBox">
              <property name="minimumSize">
               <size>
                <width>160</width>
                <height>38</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>0</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>2</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>3</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>4</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>空</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="doRow5Spacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>187</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="doVerticalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="doSaveButton">
        <property name="styleSheet">
         <string notr="true">background-color: #22c55e; color: white; min-height: 40px; font-size: 14px; font-weight: bold; border-radius: 8px;

background:url(:/io/io/IO保存.png);
background-repeat: no-repeat;
background-position: center;
background-attachment: fixed;</string>
        </property>
        <property name="text">
         <string>保存</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../resources/images.qrc"/>
 </resources>
 <connections/>
</ui>
