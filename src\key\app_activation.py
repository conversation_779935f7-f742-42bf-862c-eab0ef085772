import hashlib
import json
import base64
import base58
import struct
from Crypto.Cipher import AES

from .Locking_key import get_cpu_id_fingerprint, get_short_machine_code

DEVELOPER_SECRET_KEY = hashlib.sha256("a_different_secret_for_dev_tool".encode('utf-8')).digest()
ACTIVATION_CODE_PREFIX = "frmeta-"
KEY_DERIVATION_SALT = b"some_unique_salt_for_key_derivation_v2"
MAGIC_NUMBER = 0xABCD

# --- 数据库时间加密密钥 (这个密钥只存在于客户端代码中) ---
DB_TIME_ENCRYPTION_KEY = hashlib.sha256("a_secret_key_for_db_time_encryption".encode('utf-8')).digest()[:16]


# --- 核心逻辑函数 ---
def derive_key_and_nonce(user_short_code: str, developer_secret_key: bytes) -> tuple[bytes, bytes]:
    """与生成器中完全相同的密钥和Nonce派生函数。"""
    combined_input = user_short_code.encode('utf-8') + developer_secret_key + KEY_DERIVATION_SALT
    derived_bytes = hashlib.sha256(combined_input).digest()
    encryption_key = derived_bytes[:16]
    nonce = derived_bytes[16:28]
    return encryption_key, nonce

def validate_activation_code(activation_code: str) -> tuple[bool, dict]:
    """验证终极短格式的激活码。"""
    # 移除强制小写转换，保持原始大小写
    # activation_code = activation_code.lower()  # 注释掉这行
    if not activation_code.startswith(ACTIVATION_CODE_PREFIX):
        return False, {"error": "激活码前缀不正确。"}

    current_hw_fingerprint = get_cpu_id_fingerprint()
    if not current_hw_fingerprint:
        return False, {"error": "无法获取当前机器的硬件指纹。"}
    current_short_code = get_short_machine_code(current_hw_fingerprint)

    decryption_key, nonce = derive_key_and_nonce(current_short_code, DEVELOPER_SECRET_KEY)

    encoded_payload = activation_code[len(ACTIVATION_CODE_PREFIX):]

    try:
        decoded_data = base58.b58decode(encoded_payload)
        print(f"解码后的数据长度: {len(decoded_data)}, 内容: {decoded_data.hex()}")

        if len(decoded_data) != 8:
            raise ValueError("解码后的数据长度不正确。")

        cipher = AES.new(decryption_key, AES.MODE_GCM, nonce=nonce)

        # 只解密，不验证tag
        decrypted_payload = cipher.decrypt(decoded_data)
        print(f"解密后的数据: {decrypted_payload.hex()}")

        # 使用与生成器完全匹配的 '!HH4s' 格式解包
        magic, validity_hours, padding = struct.unpack('!HH4s', decrypted_payload)
        print(f"解包结果 - 魔法数字: {hex(magic)}, 小时数: {validity_hours}, 填充: {padding.hex()}")

        if magic != MAGIC_NUMBER:
            print(f"Magic number mismatch. Expected {hex(MAGIC_NUMBER)}, got {hex(magic)}")
            return False, {"error": "激活码与本机不匹配或已损坏。"}

        print(f"验证成功 - 有效期: {validity_hours} 小时")
        return True, {"validity_hours": validity_hours}

    except Exception as e:
        print(f"激活码验证过程中发生错误: {e}")
        return False, {"error": "激活码无效或格式错误。"}


# --- 数据库时间戳加密/解密函数 ---
def encrypt_time_data(data_dict: dict) -> str:
    """将包含时间戳和秒数的字典加密成安全的字符串。"""
    payload_json = json.dumps(data_dict, separators=(',', ':')).encode('utf-8')
    cipher = AES.new(DB_TIME_ENCRYPTION_KEY, AES.MODE_GCM)
    cipher_text, tag = cipher.encrypt_and_digest(payload_json)
    # 组合 GCM 输出: nonce + tag + ciphertext
    encrypted_data = cipher.nonce + tag + cipher_text
    return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')


def decrypt_time_data(encrypted_str: str) -> dict | None:
    """从字符串解密出时间字典，如果失败（被篡改）则返回None。"""
    try:
        encrypted_data = base64.urlsafe_b64decode(encrypted_str.encode('utf-8'))
        nonce = encrypted_data[:16]
        tag = encrypted_data[16:32]
        cipher_text = encrypted_data[32:]
        cipher = AES.new(DB_TIME_ENCRYPTION_KEY, AES.MODE_GCM, nonce=nonce)
        decrypted_payload_bytes = cipher.decrypt_and_verify(cipher_text, tag)
        return json.loads(decrypted_payload_bytes.decode('utf-8'))
    except Exception as e:
        # 任何错误（解码、解密、验证失败）都意味着数据被篡改
        print(f"解密时间数据失败: {e}")
        return None