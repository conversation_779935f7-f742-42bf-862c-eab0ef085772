<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form_1</class>
 <widget class="QWidget" name="Form_1">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>839</width>
    <height>430</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>600</width>
    <height>300</height>
   </size>
  </property>
  <property name="styleSheet">
   <string notr="true">QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #4F94CD, stop:1 #63B8FF);
    color: white;
    border-radius: 6px;
    padding: 6px;
    font-weight: bold;
    min-height: 25px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                              stop:0 #63B8FF, stop:1 #97FFFF);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                              stop:0 #3A5FCD, stop:1 #4F94CD);
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,5,0">
   <property name="spacing">
    <number>4</number>
   </property>
   <property name="leftMargin">
    <number>6</number>
   </property>
   <property name="topMargin">
    <number>6</number>
   </property>
   <property name="rightMargin">
    <number>6</number>
   </property>
   <property name="bottomMargin">
    <number>6</number>
   </property>
   <item>
    <widget class="QGroupBox" name="groupBox_5">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>160</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background-image: url(:/picture/例图.png);
background-repeat: no-repeat;
background-position: center;
background-size: contain;
border: none;</string>
     </property>
     <property name="title">
      <string/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3"/>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="spacing">
      <number>4</number>
     </property>
     <item>
      <widget class="QGroupBox" name="groupBox">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>1</verstretch>
        </sizepolicy>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox { border: none; }</string>
       </property>
       <property name="title">
        <string>抓取点设置</string>
       </property>
       <layout class="QGridLayout" name="gridLayout">
        <item row="0" column="0">
         <widget class="QLabel" name="label">
          <property name="text">
           <string>X</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="lineEdit">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>80</width>
            <height>25</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>Y</string>
          </property>
         </widget>
        </item>
        <item row="0" column="3">
         <widget class="QLineEdit" name="lineEdit_2">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>80</width>
            <height>25</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_3">
          <property name="text">
           <string>Z</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLineEdit" name="lineEdit_3">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>80</width>
            <height>25</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>RZ</string>
          </property>
         </widget>
        </item>
        <item row="1" column="3">
         <widget class="QLineEdit" name="lineEdit_4">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>80</width>
            <height>25</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="label_17">
          <property name="text">
           <string>基坐标Z</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1" colspan="3">
         <widget class="QLineEdit" name="lineEdit_11">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>200</width>
            <height>25</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="3" column="0" colspan="4">
         <widget class="QLabel" name="label_7">
          <property name="text">
           <string>示教时不需要带箱子操作</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0" colspan="4">
         <widget class="QLabel" name="label_8">
          <property name="text">
           <string>示教一次改变箱子后无需每次示教</string>
          </property>
         </widget>
        </item>
        <item row="5" column="0" colspan="2">
         <widget class="QPushButton" name="pushButton">
          <property name="styleSheet">
           <string notr="true">background: #0FFFFF;</string>
          </property>
          <property name="text">
           <string>返回示教点</string>
          </property>
         </widget>
        </item>
        <item row="5" column="2" colspan="2">
         <widget class="QPushButton" name="pushButton_2">
          <property name="styleSheet">
           <string notr="true">background: #1FFFFF;
</string>
          </property>
          <property name="text">
           <string>示教点位</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_2">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>1</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox { border: none; }</string>
       </property>
       <property name="title">
        <string/>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QLabel" name="label_27">
          <property name="text">
           <string>抓取等待高度</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="lineEdit_21">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>120</width>
            <height>25</height>
           </size>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_28">
          <property name="text">
           <string>爬取抬升高度</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLineEdit" name="lineEdit_22">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>120</width>
            <height>25</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="label_29">
          <property name="text">
           <string>抬升后等待时间</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLineEdit" name="lineEdit_23">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>120</width>
            <height>25</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="3" column="0" colspan="2">
         <widget class="QPushButton" name="pushButton_8">
          <property name="styleSheet">
           <string notr="true">background: #1FFFFF;</string>
          </property>
          <property name="text">
           <string>保存</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="spacing">
      <number>4</number>
     </property>
     <item>
      <widget class="QGroupBox" name="groupBox_3">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>1</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>0</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox { border: none; }</string>
       </property>
       <property name="title">
        <string/>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QGroupBox" name="groupBox_8">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>1</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true">QGroupBox { border: none; }</string>
          </property>
          <property name="title">
           <string>抓取后过渡点位</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_3">
           <item row="1" column="4">
            <widget class="QLineEdit" name="lineEdit_12"/>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="lineEdit_15"/>
           </item>
           <item row="3" column="0" colspan="2">
            <widget class="QPushButton" name="pushButton_6">
             <property name="styleSheet">
              <string notr="true">background: #1FFFFF;</string>
             </property>
             <property name="text">
              <string>返回示教点</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_21">
             <property name="text">
              <string>X</string>
             </property>
            </widget>
           </item>
           <item row="3" column="2">
            <widget class="QPushButton" name="pushButton_clear_11">
             <property name="styleSheet">
              <string notr="true">background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #FF6B6B, stop:1 #FF8E8E);
color: white;
border-radius: 6px;
padding: 6px;
font-weight: bold;</string>
             </property>
             <property name="text">
              <string>清除</string>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLabel" name="label_20">
             <property name="text">
              <string>Y</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="lineEdit_13"/>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_19">
             <property name="text">
              <string>Z</string>
             </property>
            </widget>
           </item>
           <item row="0" column="4">
            <widget class="QLineEdit" name="lineEdit_14"/>
           </item>
           <item row="3" column="3" colspan="2">
            <widget class="QPushButton" name="pushButton_5">
             <property name="styleSheet">
              <string notr="true">background: #1FFFFF;</string>
             </property>
             <property name="text">
              <string>示教</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1" colspan="4">
            <widget class="QLineEdit" name="lineEdit_5"/>
           </item>
           <item row="1" column="3">
            <widget class="QLabel" name="label_18">
             <property name="text">
              <string>RZ</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>基坐标Z</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_9">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>1</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="styleSheet">
           <string notr="true">QGroupBox { border: none; }</string>
          </property>
          <property name="title">
           <string>放置后过渡点位</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_4">
           <item row="1" column="3">
            <widget class="QLineEdit" name="lineEdit_19"/>
           </item>
           <item row="2" column="1" colspan="3">
            <widget class="QLineEdit" name="lineEdit_16"/>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="lineEdit_20"/>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_22">
             <property name="text">
              <string>基坐标Z</string>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLineEdit" name="lineEdit_18"/>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_23">
             <property name="text">
              <string>Z</string>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="label_24">
             <property name="text">
              <string>Y</string>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="lineEdit_17"/>
           </item>
           <item row="1" column="2">
            <widget class="QLabel" name="label_25">
             <property name="text">
              <string>RZ</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_26">
             <property name="text">
              <string>X</string>
             </property>
            </widget>
           </item>
           <item row="3" column="3">
            <widget class="QPushButton" name="pushButton_7">
             <property name="styleSheet">
              <string notr="true">background: #1FFFFF;</string>
             </property>
             <property name="text">
              <string>示教</string>
             </property>
            </widget>
           </item>
           <item row="3" column="2">
            <widget class="QPushButton" name="pushButton_clear_14">
             <property name="styleSheet">
              <string notr="true">background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #FF6B6B, stop:1 #FF8E8E);
color: white;
border-radius: 6px;
padding: 6px;
font-weight: bold;</string>
             </property>
             <property name="text">
              <string>清除</string>
             </property>
            </widget>
           </item>
           <item row="3" column="0" colspan="2">
            <widget class="QPushButton" name="pushButton_10">
             <property name="styleSheet">
              <string notr="true">background: #1FFFFF;</string>
             </property>
             <property name="text">
              <string>返回示教点</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_4">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>1</verstretch>
        </sizepolicy>
       </property>
       <property name="styleSheet">
        <string notr="true">QGroupBox { border: none; }</string>
       </property>
       <property name="title">
        <string/>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QGroupBox" name="groupBox_10">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>1</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QGroupBox { border: none; }</string>
          </property>
          <property name="title">
           <string/>
          </property>
          <layout class="QGridLayout" name="gridLayout_5">
           <item row="2" column="0">
            <widget class="QLabel" name="label_31">
             <property name="text">
              <string>放置后抬升高度</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="lineEdit_24"/>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_30">
             <property name="text">
              <string>放置前悬停高度</string>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLineEdit" name="lineEdit_25"/>
           </item>
           <item row="4" column="0">
            <widget class="QPushButton" name="pushButton_9">
             <property name="styleSheet">
              <string notr="true">background: #1FFFFF;</string>
             </property>
             <property name="text">
              <string>保存</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_12">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>1</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QGroupBox { border: none; }</string>
          </property>
          <property name="title">
           <string>左托盘零点位置</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_6">
           <item row="3" column="1">
            <widget class="QLineEdit" name="lineEdit_29"/>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="lineEdit_27"/>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="lineEdit_28"/>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_33">
             <property name="text">
              <string>Y</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_32">
             <property name="text">
              <string>X</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="lineEdit_26"/>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_35">
             <property name="text">
              <string>RZ</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_34">
             <property name="text">
              <string>Z</string>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <widget class="QPushButton" name="pushButton_clear_12">
             <property name="styleSheet">
              <string notr="true">background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #FF6B6B, stop:1 #FF8E8E);
color: white;
border-radius: 6px;
padding: 6px;
font-weight: bold;</string>
             </property>
             <property name="text">
              <string>清除</string>
             </property>
            </widget>
           </item>
           <item row="6" column="1">
            <widget class="QPushButton" name="pushButton_11">
             <property name="styleSheet">
              <string notr="true">background: #1FFFFF;</string>
             </property>
             <property name="text">
              <string>保存</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_13">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>1</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QGroupBox { border: none; }</string>
          </property>
          <property name="title">
           <string>右托盘零点位置</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_7">
           <item row="3" column="1">
            <widget class="QLineEdit" name="lineEdit_31"/>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="lineEdit_32"/>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="lineEdit_33"/>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_37">
             <property name="text">
              <string>X</string>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_36">
             <property name="text">
              <string>RZ</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_38">
             <property name="text">
              <string>Y</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="lineEdit_30"/>
           </item>
           <item row="4" column="0">
            <widget class="QPushButton" name="pushButton_clear_13">
             <property name="styleSheet">
              <string notr="true">background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                               stop:0 #FF6B6B, stop:1 #FF8E8E);
color: white;
border-radius: 6px;
padding: 6px;
font-weight: bold;</string>
             </property>
             <property name="text">
              <string>清除</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_39">
             <property name="text">
              <string>Z</string>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QPushButton" name="pushButton_12">
             <property name="styleSheet">
              <string notr="true">background: #1FFFFF;</string>
             </property>
             <property name="text">
              <string>保存</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../resources/fetching/resources_fetching.qrc"/>
 </resources>
 <connections/>
</ui>
