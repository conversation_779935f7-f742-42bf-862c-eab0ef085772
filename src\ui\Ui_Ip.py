# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Ip.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import QCoreApplication, Qt, QSize
from PySide6.QtGui import QFont
from PySide6.QtWidgets import (QDialog, QFrame, QHBoxLayout,
    <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, QPush<PERSON><PERSON><PERSON>, QSize<PERSON><PERSON><PERSON>,
    QS<PERSON><PERSON><PERSON><PERSON>, QVBoxLayout)

class Ui_Dialog(object):
    def setupUi(self, Dialog):
        if not Dialog.objectName():
            Dialog.setObjectName("Dialog")

        # 设置对话框基本属性
        Dialog.resize(480, 400)
        Dialog.setWindowTitle("IP设置")

        # 简化样式，避免复杂的渐变可能导致的问题
        Dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 15px;
            }
        """)

        # 主布局
        self.verticalLayout = QVBoxLayout(Dialog)
        self.verticalLayout.setSpacing(20)
        self.verticalLayout.setContentsMargins(30, 25, 30, 25)

        # 标题标签
        self.titleLabel = QLabel("IP 地址设置")
        self.titleLabel.setObjectName("titleLabel")

        # 设置字体
        font = QFont()
        font.setFamily("Microsoft YaHei UI")
        font.setPointSize(18)
        font.setBold(True)
        self.titleLabel.setFont(font)

        # 标题样式
        self.titleLabel.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: transparent;
                padding: 10px 0px;
                border-bottom: 2px solid #3498db;
                margin-bottom: 10px;
            }
        """)
        self.titleLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.verticalLayout.addWidget(self.titleLabel)

        # IP输入框架
        self.ipInputFrame = QFrame()
        self.ipInputFrame.setObjectName("ipInputFrame")
        self.ipInputFrame.setStyleSheet("""
            QFrame {
                background: white;
                border: 2px solid #e1e8ed;
                border-radius: 12px;
                padding: 20px;
            }
            QFrame:hover {
                border-color: #3498db;
            }
        """)
        self.ipInputFrame.setFrameShape(QFrame.Shape.StyledPanel)

        # IP框架布局
        self.ipFrameLayout = QVBoxLayout(self.ipInputFrame)
        self.ipFrameLayout.setSpacing(15)

        # IP标签
        self.ipLabel = QLabel("机器人 IP 地址")
        self.ipLabel.setObjectName("ipLabel")

        font1 = QFont()
        font1.setFamily("Microsoft YaHei UI")
        font1.setPointSize(12)
        font1.setBold(True)
        self.ipLabel.setFont(font1)

        self.ipLabel.setStyleSheet("""
            QLabel {
                color: #34495e;
                background: transparent;
                padding: 5px 0px;
            }
        """)
        self.ipFrameLayout.addWidget(self.ipLabel)

        # IP输入框
        self.process_electricInput_lineEdit = QLineEdit()
        self.process_electricInput_lineEdit.setObjectName("process_electricInput_lineEdit")

        font2 = QFont()
        font2.setFamily("Microsoft YaHei UI")
        font2.setPointSize(11)
        self.process_electricInput_lineEdit.setFont(font2)

        self.process_electricInput_lineEdit.setStyleSheet("""
            QLineEdit {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 12px 15px;
                font-size: 14px;
                color: #495057;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background: white;
                outline: none;
            }
            QLineEdit:hover {
                border-color: #74b9ff;
            }
        """)
        self.process_electricInput_lineEdit.setPlaceholderText("请输入IP地址，例如：*************")
        self.ipFrameLayout.addWidget(self.process_electricInput_lineEdit)

        # 提示标签
        self.ipHintLabel = QLabel("💡 提示：请确保输入正确的机器人IP地址")
        self.ipHintLabel.setObjectName("ipHintLabel")

        font3 = QFont()
        font3.setFamily("Microsoft YaHei UI")
        font3.setPointSize(9)
        self.ipHintLabel.setFont(font3)

        self.ipHintLabel.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background: transparent;
                padding: 5px 0px;
            }
        """)
        self.ipFrameLayout.addWidget(self.ipHintLabel)

        self.verticalLayout.addWidget(self.ipInputFrame)

        # 垂直间隔
        self.verticalSpacer = QSpacerItem(20, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        self.verticalLayout.addItem(self.verticalSpacer)

        # 按钮布局
        self.buttonLayout = QHBoxLayout()
        self.buttonLayout.setSpacing(15)

        # 左侧间隔
        self.leftSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.buttonLayout.addItem(self.leftSpacer)

        # 取消按钮
        self.process_weldingParameterApply_button = QPushButton("取消")
        self.process_weldingParameterApply_button.setObjectName("process_weldingParameterApply_button")
        self.process_weldingParameterApply_button.setMinimumSize(QSize(120, 45))

        font4 = QFont()
        font4.setFamily("Microsoft YaHei UI")
        font4.setPointSize(11)
        font4.setBold(True)
        self.process_weldingParameterApply_button.setFont(font4)

        # 简化按钮样式，避免复杂渐变
        self.process_weldingParameterApply_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #a4b4b5;
            }
            QPushButton:pressed {
                background-color: #7f8c8d;
            }
        """)
        self.buttonLayout.addWidget(self.process_weldingParameterApply_button)

        # 保存按钮
        self.process_weldingParameterApply_button_2 = QPushButton("保存")
        self.process_weldingParameterApply_button_2.setObjectName("process_weldingParameterApply_button_2")
        self.process_weldingParameterApply_button_2.setMinimumSize(QSize(120, 45))
        self.process_weldingParameterApply_button_2.setFont(font4)

        self.process_weldingParameterApply_button_2.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:pressed {
                background-color: #229954;
            }
        """)
        self.buttonLayout.addWidget(self.process_weldingParameterApply_button_2)

        # 右侧间隔
        self.rightSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.buttonLayout.addItem(self.rightSpacer)

        self.verticalLayout.addLayout(self.buttonLayout)

    def retranslateUi(self, Dialog):
        # 这个方法可以为空，因为我们已经在setupUi中设置了所有文本
        pass

