<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>boxpallet</class>
 <widget class="QWidget" name="boxpallet">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>842</width>
    <height>510</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>842</width>
    <height>510</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>842</width>
    <height>510</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>20</y>
     <width>841</width>
     <height>471</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout">
    <item row="1" column="2">
     <widget class="QFrame" name="frame_3">
      <property name="minimumSize">
       <size>
        <width>409</width>
        <height>413</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>409</width>
        <height>413</height>
       </size>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <widget class="QWidget" name="layoutWidget_4">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>381</width>
         <height>32</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout_5">
        <item row="0" column="2">
         <widget class="QPushButton" name="save_btn_l_4">
          <property name="minimumSize">
           <size>
            <width>52</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>52</width>
            <height>30</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>保存</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="stack_label_l_4">
          <property name="minimumSize">
           <size>
            <width>63</width>
            <height>16</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>63</width>
            <height>16</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">text-align: center;</string>
          </property>
          <property name="text">
           <string>托盘参数</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Policy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>200</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QFrame" name="frame_8">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>50</y>
         <width>283</width>
         <height>167</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>283</width>
         <height>167</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>283</width>
         <height>167</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color: #185FAB</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::Shape::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Shadow::Raised</enum>
       </property>
      </widget>
      <widget class="QLabel" name="label_5">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>240</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
        </font>
       </property>
       <property name="text">
        <string>长度</string>
       </property>
      </widget>
      <widget class="QTextEdit" name="length_pallet">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>260</y>
         <width>350</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>350</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>350</width>
         <height>30</height>
        </size>
       </property>
      </widget>
      <widget class="QTextEdit" name="width_pallet">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>320</y>
         <width>350</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>350</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>350</width>
         <height>30</height>
        </size>
       </property>
      </widget>
      <widget class="QLabel" name="width">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>300</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
        </font>
       </property>
       <property name="text">
        <string>宽度</string>
       </property>
      </widget>
      <widget class="QTextEdit" name="textEdit_18">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>370</y>
         <width>350</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>350</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>350</width>
         <height>30</height>
        </size>
       </property>
      </widget>
      <widget class="QLabel" name="label_18">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>350</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
        </font>
       </property>
       <property name="text">
        <string>高度</string>
       </property>
      </widget>
     </widget>
    </item>
    <item row="0" column="0" colspan="3">
     <widget class="QFrame" name="frame">
      <property name="minimumSize">
       <size>
        <width>839</width>
        <height>50</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>839</width>
        <height>50</height>
       </size>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QPushButton" name="pushButton_3">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>50</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="text">
          <string>箱子/托盘</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_4">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>50</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="text">
          <string>层样式</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_5">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>50</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="text">
          <string>垛型</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QFrame" name="frame_2">
      <property name="minimumSize">
       <size>
        <width>409</width>
        <height>413</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>409</width>
        <height>413</height>
       </size>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <widget class="QWidget" name="layoutWidget">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>381</width>
         <height>32</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="2">
         <widget class="QPushButton" name="save_btn_l">
          <property name="minimumSize">
           <size>
            <width>52</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>52</width>
            <height>30</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>保存</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="box_size">
          <property name="minimumSize">
           <size>
            <width>63</width>
            <height>16</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>63</width>
            <height>16</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">text-align: center;</string>
          </property>
          <property name="text">
           <string>箱子参数</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Policy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>200</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QFrame" name="frame_4">
       <property name="geometry">
        <rect>
         <x>60</x>
         <y>50</y>
         <width>283</width>
         <height>167</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>283</width>
         <height>167</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>283</width>
         <height>167</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color: #185FAB</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::Shape::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Shadow::Raised</enum>
       </property>
      </widget>
      <widget class="QLabel" name="label">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>240</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
        </font>
       </property>
       <property name="text">
        <string>来料方向</string>
       </property>
      </widget>
      <widget class="QTextEdit" name="box_direction">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>260</y>
         <width>350</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>350</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>350</width>
         <height>30</height>
        </size>
       </property>
      </widget>
      <widget class="QLabel" name="label_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>300</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
        </font>
       </property>
       <property name="text">
        <string>长度</string>
       </property>
      </widget>
      <widget class="QTextEdit" name="length_textEdit">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>320</y>
         <width>160</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>160</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>160</width>
         <height>30</height>
        </size>
       </property>
      </widget>
      <widget class="QTextEdit" name="width_box">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>320</y>
         <width>160</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>160</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>160</width>
         <height>30</height>
        </size>
       </property>
      </widget>
      <widget class="QLabel" name="label_3">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>300</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
        </font>
       </property>
       <property name="text">
        <string>宽度</string>
       </property>
      </widget>
      <widget class="QLabel" name="label_4">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>350</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
        </font>
       </property>
       <property name="text">
        <string>重量</string>
       </property>
      </widget>
      <widget class="QTextEdit" name="hight_box">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>370</y>
         <width>160</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>160</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>160</width>
         <height>30</height>
        </size>
       </property>
      </widget>
      <widget class="QLabel" name="hight_textEdit">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>350</y>
         <width>71</width>
         <height>16</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
        </font>
       </property>
       <property name="text">
        <string>高度</string>
       </property>
      </widget>
      <widget class="QTextEdit" name="weight_box">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>370</y>
         <width>160</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>160</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>160</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
