import time
from PySide6.QtCore import Signal
from PySide6.QtGui import Qt
from PySide6.QtWidgets import QVBoxLayout
from PySide6.QtWidgets import QWidget, QTableWidgetItem
from ..common.sql_lite_tool import SqliteTool
from ..ui.Ui_logPage import Ui_Form
from ..ui.Ui_logPage_operation import Ui_Form_1
from ..ui.Ui_logPage_alarm import Ui_Form_2
from loguru import logger
import logging
from ..common.robot_status import RobotStatus


class LogLogic(QWidget, Ui_Form):

    send_cmd_to_robot = Signal(str)  # 下发指令的信号
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)

        # 初始化基础组件
        self.db_tool = SqliteTool.get_instance()
        self.robot_status = RobotStatus()

        # 设置UI
        self.setupUi(self)
        self._init_ui_components()
        self._setup_connections()

        # 初始化默认状态
        self.OperationButton.setChecked(True)
        self._switch_interface()

        # 确保初始样式正确应用
        from PySide6.QtCore import QTimer
        QTimer.singleShot(100, self._update_button_styles)

        # 监听窗口尺寸变化
        self._setup_resize_handler()

        self.logger.info("operationLogic初始化完成")


    def _setup_connections(self):
        """安全设置信号连接"""
        # 设置按钮属性
        self.OperationButton.setProperty("buttonType", "operation")
        self.AlarmButton.setProperty("buttonType", "Alarm")

        for button in [self.OperationButton, self.AlarmButton]:
            button.setCheckable(True)
            button.setAutoExclusive(True)
            button.clicked.connect(self._handle_button_click)

        # 设置初始按钮样式
        self._update_button_styles()

    def _update_button_styles(self):
        """更新按钮背景颜色样式 - 响应式设计"""
        # 获取当前窗口尺寸来调整样式
        try:
            # 获取父窗口尺寸
            parent_width = self.parent().width() if self.parent() else 800
            parent_height = self.parent().height() if self.parent() else 600

            # 根据窗口尺寸调整padding和字体
            if parent_width < 600 or parent_height < 400:
                # 小尺寸窗口
                padding = "2px 4px"
                border_radius = "3px"
                min_height = "18px"
                max_height = "30px"
            else:
                # 正常尺寸窗口
                padding = "4px 8px"
                border_radius = "4px"
                min_height = "22px"
                max_height = "35px"

        except:
            # 默认值
            padding = "4px 6px"
            border_radius = "4px"
            min_height = "20px"
            max_height = "35px"

        # 定义按钮样式 - 无边框
        selected_style = f"""
            QPushButton {{
                background-color: #4F94CD;
                color: white;
                border: none;
                border-radius: {border_radius};
                padding: {padding};
                font-weight: bold;
                min-height: {min_height};
                max-height: {max_height};
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: #63B8FF;
            }}
        """

        unselected_style = f"""
            QPushButton {{
                background-color: white;
                color: #333333;
                border: none;
                border-radius: {border_radius};
                padding: {padding};
                font-weight: bold;
                min-height: {min_height};
                max-height: {max_height};
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: #F0F0F0;
            }}
        """

        # 应用样式
        if self.OperationButton.isChecked():
            # 操作日志按钮选中 - 蓝色背景
            self.OperationButton.setStyleSheet(selected_style)
            # 报警信息按钮未选中 - 白色背景
            self.AlarmButton.setStyleSheet(unselected_style)
        else:
            # 报警信息按钮选中 - 蓝色背景
            self.AlarmButton.setStyleSheet(selected_style)
            # 操作日志按钮未选中 - 白色背景
            self.OperationButton.setStyleSheet(unselected_style)

    def _setup_resize_handler(self):
        """设置窗口尺寸变化处理"""
        try:
            # 创建一个定时器来检测尺寸变化
            from PySide6.QtCore import QTimer
            self.resize_timer = QTimer()
            self.resize_timer.timeout.connect(self._on_resize_check)
            self.resize_timer.start(500)  # 每500ms检查一次

            # 记录当前尺寸
            self.last_size = None
        except Exception as e:
            self.logger.warning(f"设置窗口尺寸监听失败: {e}")

    def _on_resize_check(self):
        """检查窗口尺寸是否变化"""
        try:
            if self.parent():
                current_size = (self.parent().width(), self.parent().height())
                if self.last_size != current_size:
                    self.last_size = current_size
                    # 延迟更新样式，避免频繁更新
                    from PySide6.QtCore import QTimer
                    QTimer.singleShot(100, self._update_button_styles)
        except Exception:
            pass  # 忽略错误，避免影响正常功能

    def _handle_button_click(self):
        """统一处理按钮点击事件"""
        sender = self.sender()
        if not sender.isChecked():
            sender.setChecked(True)
            return

        self.logger.debug(f"按钮点击: {sender.objectName()}")
        self._update_button_styles()  # 更新按钮样式
        self._switch_interface()

    def _switch_interface(self):
        """执行界面切换操作"""

        self._adjust_interface_size()
        # 确保布局存在
        if not self.widget_qiehuan.layout():
            layout = QVBoxLayout()
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(0)
            self.widget_qiehuan.setLayout(layout)
            self.logger.debug("为widget_qiehuan创建了新布局")

        # 获取当前布局
        layout = self.widget_qiehuan.layout()

        # 安全清理旧控件
        self._clear_layout(layout)

        # 添加新界面
        target_widget = self.operation_widget if self.OperationButton.isChecked() else self.Alarm_widget
        target_name = "操作日志" if self.OperationButton.isChecked() else "报警信息"

        layout.addWidget(target_widget)
        target_widget.show()

        # 根据切换的界面加载相应数据
        if self.OperationButton.isChecked():
            # 切换到操作日志界面，加载操作日志
            self.load_operation_logs()
        else:
            # 切换到报警信息界面，加载报警记录
            self.load_alarm_records()

        # 刷新布局
        layout.activate()
        self.widget_qiehuan.updateGeometry()

        self.logger.info(f"已切换到: {target_name}")

    def _clear_layout(self, layout):
        """安全清除布局中的所有控件"""
        while layout.count():
            item = layout.takeAt(0)
            if item and item.widget():
                w = item.widget()
                w.hide()
                layout.removeWidget(w)
                self.logger.debug(f"移除控件: {w.objectName()}")

    def _adjust_interface_size(self):
        """动态调整界面尺寸"""
        # 获取当前活动界面
        current_widget = self.operation_widget if self.OperationButton.isChecked() else self.Alarm_widget

        # 设置最小尺寸（根据实际需要调整数值）
        current_widget.setMinimumSize(800, 500)

        # 对齐方式（居中显示）
        if layout := self.widget_qiehuan.layout():
            layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 强制更新布局
        current_widget.updateGeometry()
        self.widget_qiehuan.update()

    def clean_up(self):
        """清理资源"""
        for button in [self.OperationButton, self.AlarmButton]:
            try:
                button.clicked.disconnect()
            except RuntimeError:
                pass

        if layout := self.widget_qiehuan.layout():
            self._clear_layout(layout)
            self.widget_qiehuan.setLayout(None)

    def _init_ui_components(self):
        """初始化所有子界面组件"""
        # 示教界面
        self.operation_widget = QWidget()
        self.operation_ui = Ui_Form_1()
        self.operation_ui.setupUi(self.operation_widget)

        self.Alarm_widget = QWidget()
        self.Alarm_ui = Ui_Form_2()
        self.Alarm_ui.setupUi(self.Alarm_widget)

        # 初始化抓取/放置界面按钮连接
        self._init_fetch_place_buttons()
        self._init_Alarm_place_buttons()

    def _init_fetch_place_buttons(self):
        """初始化操作日志界面的按钮连接"""
        try:
            # 加载操作日志
            self.load_operation_logs()

            # 设置刷新按钮（如果存在）
            if hasattr(self.operation_ui, 'refreshButton'):
                self.operation_ui.refreshButton.clicked.connect(self.load_operation_logs)

            self.logger.info("操作日志界面初始化完成")
        except Exception as e:
            self.logger.error(f"初始化操作日志界面时出错: {e}")

    def _init_Alarm_place_buttons(self):

        try:
            self.load_alarm_records()

            # 移除有问题的receivers检查，PySide6的Signal对象没有receivers方法
            self.logger.debug("send_cmd_to_robot 信号已设置")

            self.logger.info("报警界面初始化完成")
        except Exception as e:
            self.logger.error(f"初始化报警界面时出错: {e}")

    def load_alarm_records(self):
        """从数据库加载报警记录并显示到表格中"""
        # 查询 alarm_record 表中所有记录（移除status限制）
        query = self.db_tool.select(
            "alarm_record",
            fields=["id", "time", "alarm_code", "alarm_des", "status"],
            condition="1=1 ORDER BY id DESC LIMIT 50"
        )

        # 清空表格
        self.Alarm_ui.alarmTableWidget.clearContents()

        # 始终显示表格，不隐藏
        self.Alarm_ui.alarmTableWidget.setVisible(True)
        if hasattr(self.Alarm_ui, 'noAlarmLabel'):
            self.Alarm_ui.noAlarmLabel.setVisible(False)

        # 检查是否有报警记录
        if not query or len(query) == 0:
            # 没有报警记录时显示空表格，但保持一定的行数以显示表格结构
            self.Alarm_ui.alarmTableWidget.setRowCount(5)  # 显示5个空行
            self.Alarm_ui.alarmTableWidget.setColumnCount(3)  # 确保列数正确

            # 清空所有单元格内容，但保持表格结构
            for row in range(5):
                for col in range(3):
                    empty_item = QTableWidgetItem("")
                    empty_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.Alarm_ui.alarmTableWidget.setItem(row, col, empty_item)

            self.logger.info("没有找到报警记录，显示空表格")
            return

        # 设置表格行数
        self.Alarm_ui.alarmTableWidget.setRowCount(len(query))

        # 设置表格列数（确保与查询字段一致）
        self.Alarm_ui.alarmTableWidget.setColumnCount(3)

        # 生成序号列的降序数字序列
        sequence_numbers = list(range(1, len(query) + 1))  # 生成从1到len(query)的升序序列

        # 填充表格数据
        for row_idx, record in enumerate(query):
            # 序号列（居中对齐）
            item_id = QTableWidgetItem(str(sequence_numbers[row_idx]))  # 使用降序序列
            item_id.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.Alarm_ui.alarmTableWidget.setItem(row_idx, 0, item_id)

            # 报警值列（居中对齐）
            item_code = QTableWidgetItem(record["alarm_des"])
            item_code.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.Alarm_ui.alarmTableWidget.setItem(row_idx, 1, item_code)

            # 日期列（居中对齐）
            item_time = QTableWidgetItem(record["time"])
            item_time.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.Alarm_ui.alarmTableWidget.setItem(row_idx, 2, item_time)

            # 仅当状态未解决时设置红色背景（使用更显眼的颜色组合）
            if record["status"] == 0:
                for col in range(self.Alarm_ui.alarmTableWidget.columnCount()):
                    cell_item = self.Alarm_ui.alarmTableWidget.item(row_idx, col)
                    cell_item.setBackground(Qt.GlobalColor.red)
                    cell_item.setForeground(Qt.GlobalColor.white)  # 白字更清晰

    def load_operation_logs(self, limit=50):
        """从数据库加载操作日志并显示到表格中"""
        try:
            # 查询 operation_log 表中的记录
            query = self.db_tool.select(
                "operation_log",
                fields=["id", "operation_type", "operation_content", "result", "created_time"],
                condition=f"1=1 ORDER BY id DESC LIMIT {limit}"
            )

            # 清空表格
            self.operation_ui.tableWidget.clearContents()

            # 始终显示表格
            self.operation_ui.tableWidget.setVisible(True)

            # 检查是否有操作记录
            if not query or len(query) == 0:
                # 没有操作记录时显示空表格，但保持一定的行数以显示表格结构
                self.operation_ui.tableWidget.setRowCount(5)  # 显示5个空行
                self.operation_ui.tableWidget.setColumnCount(3)  # 确保列数正确

                # 清空所有单元格内容，但保持表格结构
                for row in range(5):
                    for col in range(3):
                        empty_item = QTableWidgetItem("")
                        empty_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        self.operation_ui.tableWidget.setItem(row, col, empty_item)

                self.logger.info("没有找到操作日志记录，显示空表格")
                return

            # 设置表格行数和列数
            self.operation_ui.tableWidget.setRowCount(len(query))
            self.operation_ui.tableWidget.setColumnCount(3)

            # 生成序号
            sequence_numbers = list(range(1, len(query) + 1))

            # 填充表格数据
            for row_idx, record in enumerate(query):
                # 序号列（居中对齐）
                item_id = QTableWidgetItem(str(sequence_numbers[row_idx]))
                item_id.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.operation_ui.tableWidget.setItem(row_idx, 0, item_id)

                # 操作内容列（居中对齐）
                item_content = QTableWidgetItem(record["operation_content"])
                item_content.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.operation_ui.tableWidget.setItem(row_idx, 1, item_content)

                # 时间列（居中对齐）
                item_time = QTableWidgetItem(record["created_time"])
                item_time.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.operation_ui.tableWidget.setItem(row_idx, 2, item_time)

                # 根据操作结果设置行颜色
                if record["result"] == "failed":
                    # 失败操作用红色背景
                    for col in range(3):
                        cell_item = self.operation_ui.tableWidget.item(row_idx, col)
                        cell_item.setBackground(Qt.GlobalColor.red)
                        cell_item.setForeground(Qt.GlobalColor.white)
                elif record["result"] == "warning":
                    # 警告操作用黄色背景
                    for col in range(3):
                        cell_item = self.operation_ui.tableWidget.item(row_idx, col)
                        cell_item.setBackground(Qt.GlobalColor.yellow)
                        cell_item.setForeground(Qt.GlobalColor.black)

            self.logger.info(f"成功加载 {len(query)} 条操作日志记录")

        except Exception as e:
            self.logger.error(f"加载操作日志时出错: {e}")

    @staticmethod
    def log_operation(operation_type, operation_content, operation_details=None, result="success", error_message=None, user_name="system"):
        """
        记录操作日志到数据库（静态方法，可以在任何地方调用）

        :param operation_type: 操作类型（如：start、stop、teach_point、save_settings等）
        :param operation_content: 操作内容描述
        :param operation_details: 操作详细信息（字典格式，会转换为JSON字符串）
        :param result: 操作结果：success、failed、warning
        :param error_message: 错误信息（如果操作失败）
        :param user_name: 操作用户
        """
        try:
            db_tool = SqliteTool.get_instance()

            # 准备插入数据
            log_data = {
                'operation_type': operation_type,
                'operation_content': operation_content,
                'result': result,
                'user_name': user_name,
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S')
            }

            # 添加可选字段
            if operation_details:
                log_data['operation_details'] = str(operation_details)
            if error_message:
                log_data['error_message'] = error_message

            # 插入数据库
            success = db_tool.insert("operation_log", log_data)

            if not success:
                print(f"操作日志记录失败: {operation_content}")

            return success

        except Exception as e:
            print(f"记录操作日志时出错: {e}")
            return False

    def get_operation_logs_by_type(self, operation_type, limit=20):
        """
        根据操作类型获取操作日志

        :param operation_type: 操作类型
        :param limit: 返回记录数限制
        :return: 操作日志列表
        """
        try:
            query = self.db_tool.select(
                "operation_log",
                fields=["id", "operation_type", "operation_content", "operation_details", "result", "created_time"],
                condition=f"operation_type = '{operation_type}' ORDER BY id DESC LIMIT {limit}"
            )
            return query if query else []
        except Exception as e:
            self.logger.error(f"根据类型获取操作日志时出错: {e}")
            return []

    def get_operation_logs_by_date_range(self, start_date, end_date, limit=100):
        """
        根据日期范围获取操作日志

        :param start_date: 开始日期（格式：YYYY-MM-DD）
        :param end_date: 结束日期（格式：YYYY-MM-DD）
        :param limit: 返回记录数限制
        :return: 操作日志列表
        """
        try:
            condition = f"DATE(created_time) BETWEEN '{start_date}' AND '{end_date}' ORDER BY id DESC LIMIT {limit}"
            query = self.db_tool.select(
                "operation_log",
                fields=["id", "operation_type", "operation_content", "operation_details", "result", "created_time"],
                condition=condition
            )
            return query if query else []
        except Exception as e:
            self.logger.error(f"根据日期范围获取操作日志时出错: {e}")
            return []

    def get_failed_operations(self, limit=20):
        """
        获取失败的操作记录

        :param limit: 返回记录数限制
        :return: 失败操作日志列表
        """
        try:
            query = self.db_tool.select(
                "operation_log",
                fields=["id", "operation_type", "operation_content", "error_message", "created_time"],
                condition=f"result = 'failed' ORDER BY id DESC LIMIT {limit}"
            )
            return query if query else []
        except Exception as e:
            self.logger.error(f"获取失败操作记录时出错: {e}")
            return []

    def clear_old_operation_logs(self, days=30):
        """
        清理指定天数之前的操作日志

        :param days: 保留天数
        :return: 是否清理成功
        """
        try:
            condition = f"created_time < datetime('now', '-{days} days')"

            # 先查询要删除的记录数
            count_query = self.db_tool.select(
                "operation_log",
                fields=["COUNT(*) as count"],
                condition=condition
            )

            if count_query and count_query[0]["count"] > 0:
                delete_count = count_query[0]["count"]

                # 执行删除
                delete_sql = f"DELETE FROM operation_log WHERE {condition}"
                query = self.db_tool.execute_sql(delete_sql)

                if not query.lastError().isValid():
                    self.logger.info(f"成功清理 {delete_count} 条 {days} 天前的操作日志")
                    # 记录清理操作
                    self.log_operation(
                        "system_maintenance",
                        f"清理了 {delete_count} 条 {days} 天前的操作日志",
                        {"deleted_count": delete_count, "retention_days": days}
                    )
                    return True
                else:
                    self.logger.error(f"清理操作日志失败: {query.lastError().text()}")
                    return False
            else:
                self.logger.info(f"没有找到 {days} 天前的操作日志需要清理")
                return True

        except Exception as e:
            self.logger.error(f"清理操作日志时出错: {e}")
            return False