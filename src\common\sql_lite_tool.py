from PySide6.QtSql import QSqlDatabase, QSqlQuery, QSqlTableModel
from PySide6.QtCore import QObject, Slot, Signal
from typing import List, Dict, Optional
import os
from src.common.log import Logger as log  # 引入日志模块

"""
SQLite 数据库工具类 使用方法 在main中初始化 调用 initialize() 初始化数据库连接
       在其他地方使用 引入依赖后 调用 SqliteTool.get_instance() 获取单例实例
"""

class SqliteTool(QObject):
    """
    PySide6 原生 SQLite 数据库工具类（单例模式）
    功能：连接管理、CRUD 操作、事务支持、模型-视图集成
    """
    _instance = None  # 单例实例
    db_error = Signal(str)  # 错误信号（用于 GUI 提示）
    _initialized = False  # 标记是否已初始化

    @classmethod
    def get_instance(cls):
        """获取全局唯一实例（延迟初始化）"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    @classmethod
    def initialize(cls):
        """手动初始化数据库连接"""
        if cls._instance is None:
            cls._instance = cls()  # 创建实例并触发 __new__ 和 _init_db

    def __new__(cls, *args, **kwargs):
        """单例模式确保全局唯一实例"""
        if not cls._instance:
            cls._instance = super().__new__(cls, *args, **kwargs)
            cls._instance._init_db()  # 初始化数据库连接
        return cls._instance

    def _init_db(self):
        """初始化数据库连接（私有方法）"""
        if self.__class__._initialized:
            return
        self.__class__._initialized = True
        # 数据库配置 目前数据库放在 文档-PalletizerData 文件夹下
        documents_path = os.path.expanduser("~/Documents")
        self.db_dir = os.path.join(documents_path, "PalletizerData")
        if not os.path.exists(self.db_dir):
            os.makedirs(self.db_dir)
        self.db_path = os.path.join(self.db_dir, "robot.db")
        self.conn_name = "robot_sqllite_conn"  # 连接名（多线程时需不同连接名）

        # 避免重复创建连接
        if self.conn_name in QSqlDatabase.connectionNames():
            self.db = QSqlDatabase.database(self.conn_name)
        else:
            self.db = QSqlDatabase.addDatabase("QSQLITE", self.conn_name)
            self.db.setDatabaseName(self.db_path)

        # 打开数据库（失败时发送错误信号）
        if not self.db.open():
            self.db_error.emit(f"数据库连接失败: {self.db.lastError().text()}")
        else:
            # 启用外键约束（支持级联删除）
            self.execute_sql("PRAGMA foreign_keys = ON")

    @Slot()
    def execute_sql(self, sql: str, params: Optional[List] = None) -> QSqlQuery:
        """
        执行原生 SQL 语句（支持参数化查询）
        :param sql: SQL 语句（如 "SELECT * FROM user WHERE id=?")
        :param params: 参数列表（按顺序填充 SQL 中的占位符）
        :return: QSqlQuery 对象（可通过 query.lastError() 检查错误）
        """
        query = QSqlQuery(self.db)
        query.prepare(sql)

        # 绑定参数（防止 SQL 注入）
        if params:
            for idx, param in enumerate(params):
                query.bindValue(idx, param)

        # 记录 SQL 到日志
        log_message = f"Executing SQL: {sql} with parameters: {params if params else 'None'}"
        if 'SELECT' not in sql:
            log.info(log_message)

        # 执行 SQL
        if not query.exec():
            self.db_error.emit(f"SQL 执行失败: {query.lastError().text()}")
            log.error(f"SQL 执行失败: {query.lastError().text()}")
        return query

    def insert(self, table: str, data: Dict) -> bool:
        """
        插入单条记录（自动生成 INSERT 语句）
        :param table: 表名
        :param data: 字段-值字典（如 {"name": "张三", "age": 25}）
        :return: 插入是否成功
        """
        fields = ", ".join(data.keys())
        placeholders = ", ".join(["?"] * len(data))
        sql = f"INSERT INTO {table} ({fields}) VALUES ({placeholders})"
        params = list(data.values())
        # log.info(f"Generated INSERT SQL: {sql} with parameters: {params}")  # 记录生成的 INSERT SQL
        query = self.execute_sql(sql, params)
        return not query.lastError().isValid()

    def update(self, table: str, data: Dict, condition: str) -> bool:
        """
        更新记录（自动生成 UPDATE 语句）
        :param table: 表名
        :param data: 字段-值字典（要更新的内容）
        :param condition: 更新条件（如 "id=1"）
        :return: 更新是否成功
        """
        set_clause = ", ".join([f"{k}=?" for k in data.keys()])
        sql = f"UPDATE {table} SET {set_clause} WHERE {condition}"
        params = list(data.values())
        # log.info(f"Generated UPDATE SQL: {sql} with parameters: {params}")  # 记录生成的 UPDATE SQL
        query = self.execute_sql(sql, params)
        return not query.lastError().isValid()

    def delete(self, table: str, condition: str) -> bool:
        """
        删除记录（自动生成 DELETE 语句）
        :param table: 表名
        :param condition: 删除条件（如 "id=1"）
        :return: 删除是否成功
        """
        sql = f"DELETE FROM {table} WHERE {condition}"
        # log.info(f"Generated DELETE SQL: {sql}")  # 记录生成的 DELETE SQL
        query = self.execute_sql(sql)
        return not query.lastError().isValid()

    def select(self, table: str, fields: List[str] = None, condition: str = None, params: List = None) -> List[Dict]:
        """查询记录（自动生成 SELECT 语句）
        :param table: 表名
        :param fields: 要查询的字段列表（默认查询所有字段）
        :param condition: 查询条件（如 "age=?"）
        :param params: 参数列表，用于填充条件中的占位符
        :return: 结果列表（每个元素是字段-值字典）
        """
        fields_clause = ", ".join(fields) if fields else "*"
        where_clause = f"WHERE {condition}" if condition else ""
        sql = f"SELECT {fields_clause} FROM {table} {where_clause}"
        query = self.execute_sql(sql, params)

        results = []
        while query.next():
            record = query.record()
            row = {}
            for i in range(record.count()):
                row[record.fieldName(i)] = query.value(i)
            results.append(row)
        return results

    def get_table_model(self, table: str) -> QSqlTableModel:
        """
        获取 QSqlTableModel（用于模型-视图绑定）
        :param table: 表名
        :return: QSqlTableModel 实例（可直接绑定到 QTableView）
        """
        model = QSqlTableModel(db=self.db)
        model.setTable(table)
        model.select()  # 加载数据
        return model

    def begin_transaction(self) -> bool:
        """开始事务"""
        return self.db.transaction()

    def commit_transaction(self) -> bool:
        """提交事务"""
        return self.db.commit()

    def rollback_transaction(self) -> bool:
        """回滚事务"""
        return self.db.rollback()

    def close(self):
        """关闭数据库连接（程序退出时调用）"""
        if self.db.isOpen():
            self.db.close()
            # QSqlDatabase.removeDatabase(self.conn_name)

    @classmethod
    def is_initialized(cls) -> bool:
        """检查数据库是否已经初始化"""
        return cls._initialized