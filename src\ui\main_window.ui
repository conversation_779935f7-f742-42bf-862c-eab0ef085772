<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>795</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="mainVerticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="frame_5">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>80</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>80</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">    background-image: url(:/标题栏背景/标题栏背景2x.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    border: none;
</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="topHorizontalLayout">
      <property name="spacing">
       <number>10</number>
      </property>
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <widget class="QLabel" name="NameLabel_2">
        <property name="minimumSize">
         <size>
          <width>325</width>
          <height>43</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">font-family: MiSans;
font-weight: 400;
font-size: 14px;
color: #F53D3A;
text-align: left;
background-image: url(:/logo/LOGO.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="topHorizontalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="Speedlabel_3">
        <property name="minimumSize">
         <size>
          <width>16</width>
          <height>16</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">font-family: MiSans;
font: 18pt &quot;Microsoft YaHei UI&quot;;
font-weight: 400;
font-size: 18px;
color: #030B1C;
text-align: left;
background-image: url(:/time/时间.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="TimeLabel_2">
        <property name="minimumSize">
         <size>
          <width>141</width>
          <height>29</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">font-family: MiSans;
font-weight: 400;
font: 16pt &quot;Microsoft YaHei UI&quot;;
color: #B4B4B4;
text-align: left;
</string>
        </property>
        <property name="text">
         <string>2025/07/04  11:36:20</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="translateButtonsLayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QPushButton" name="Translate1Label_2">
          <property name="minimumSize">
           <size>
            <width>30</width>
            <height>29</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>30</width>
            <height>29</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="toolTip">
           <string>中文</string>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/Chinese/中英文中文.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="Translate2Label_2">
          <property name="minimumSize">
           <size>
            <width>30</width>
            <height>29</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>30</width>
            <height>29</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="toolTip">
           <string>English</string>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/English/中英文 英文.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QPushButton" name="Scale_downButton_2">
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>29</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>30</width>
          <height>29</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>11</pointsize>
         </font>
        </property>
        <property name="toolTip">
         <string>最小化窗口</string>
        </property>
        <property name="styleSheet">
         <string notr="true">background-image: url(:/small/缩小.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="Scale_upButton_2">
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>29</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>30</width>
          <height>29</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>11</pointsize>
         </font>
        </property>
        <property name="toolTip">
         <string>最大化/还原窗口</string>
        </property>
        <property name="styleSheet">
         <string notr="true">color: #F53D3A;
background-image: url(:/big/窗口.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="CloseButton_2">
        <property name="minimumSize">
         <size>
          <width>30</width>
          <height>29</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>30</width>
          <height>29</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>11</pointsize>
         </font>
        </property>
        <property name="toolTip">
         <string>关闭窗口</string>
        </property>
        <property name="styleSheet">
         <string notr="true">background-image: url(:/close/关闭.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="frame_2">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>71</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>71</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px;</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="controlHorizontalLayout">
      <property name="spacing">
       <number>10</number>
      </property>
      <property name="leftMargin">
       <number>30</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <widget class="QLabel" name="Speedlabel_2">
        <property name="minimumSize">
         <size>
          <width>41</width>
          <height>21</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">font-family: MiSans;
font: 18pt &quot;Microsoft YaHei UI&quot;;
font-weight: 400;
font-size: 18px;
color: #030B1C;
text-align: left;</string>
        </property>
        <property name="text">
         <string>速度</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignmentFlag::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="speedControlLayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QPushButton" name="DownButton_2">
          <property name="minimumSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/Down/减.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSlider" name="pushSlider_2">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="UPButton_2">
          <property name="minimumSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/Up/加.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="Speedvaluelabel_2">
          <property name="minimumSize">
           <size>
            <width>41</width>
            <height>21</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">font-family: MiSans;
font: 18pt &quot;Microsoft YaHei UI&quot;;
font-weight: 400;
font-size: 18px;
color: #030B1C;
text-align: left;</string>
          </property>
          <property name="text">
           <string>50%</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="controlHorizontalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>30</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="ModeButton_2">
        <property name="minimumSize">
         <size>
          <width>105</width>
          <height>45</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>14</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="ClearalarmButton_2">
        <property name="minimumSize">
         <size>
          <width>124</width>
          <height>45</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>14</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">background-image: url(:/clearAlarm/清除报警(1).png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="EStopButton_9">
        <property name="minimumSize">
         <size>
          <width>84</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>84</width>
          <height>45</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>16</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">background-image: url(:/Estop/STOP(1).png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="mainContentLayout">
     <property name="spacing">
      <number>10</number>
     </property>
     <property name="leftMargin">
      <number>10</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>10</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <layout class="QVBoxLayout" name="leftVerticalLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <item>
        <spacer name="leftVerticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Policy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>5</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="RunButton_2">
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>110</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>191</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>16</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="FormulaButton_2">
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>110</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>191</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>16</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="TeachingButton_2">
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>110</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>191</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>16</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="IOButton_2">
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>110</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>191</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>16</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="LogButton_2">
         <property name="minimumSize">
          <size>
           <width>150</width>
           <height>110</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>191</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>16</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="leftVerticalSpacer">
         <property name="orientation">
          <enum>Qt::Orientation::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Policy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>5</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QStackedWidget" name="stackedWidget">
       <property name="minimumSize">
        <size>
         <width>600</width>
         <height>400</height>
        </size>
       </property>
       <widget class="QWidget" name="page_3"/>
       <widget class="QWidget" name="page_4"/>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QFrame" name="frame_4">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>80</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>80</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background: #FAFBFD;</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="bottomHorizontalLayout">
      <property name="spacing">
       <number>15</number>
      </property>
      <property name="leftMargin">
       <number>20</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="leftBottomLayout">
        <property name="spacing">
         <number>10</number>
        </property>
        <item>
         <widget class="QLabel" name="AlarmLabel">
          <property name="minimumSize">
           <size>
            <width>68</width>
            <height>64</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="Alarminforlabel_2">
          <property name="minimumSize">
           <size>
            <width>193</width>
            <height>29</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">font-family: MiSans;
font-weight: 400;
font-size: 14px;
color: #F53D3A;
text-align: left;</string>
          </property>
          <property name="text">
           <string>6轴软限位6轴软限位6轴软限位</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="dateTimeLayout">
        <item>
         <widget class="QLabel" name="datalabel_2">
          <property name="minimumSize">
           <size>
            <width>81</width>
            <height>29</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">font-family: MiSans;
font-weight: 400;
font-size: 14px;
color: #B4B4B4;
text-align: left;</string>
          </property>
          <property name="text">
           <string>2025/07/04</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="hourlabel_2">
          <property name="minimumSize">
           <size>
            <width>81</width>
            <height>29</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">font-family: MiSans;
font-weight: 400;
font-size: 14px;
color: #B4B4B4;
text-align: left;</string>
          </property>
          <property name="text">
           <string>11：57：30</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="collisionLayout">
        <property name="spacing">
         <number>10</number>
        </property>
        <item>
         <widget class="QLabel" name="ColliLabel">
          <property name="minimumSize">
           <size>
            <width>56</width>
            <height>58</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">font-family: MiSans;
font: 18pt &quot;Microsoft YaHei UI&quot;;
font-weight: 400;
font-size: 18px;
color: #030B1C;
text-align: left;
background-image: url(:/collision/碰撞.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;</string>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="collisionControlLayout">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="downcopushButton_2">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>11</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/Down/减.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QSlider" name="pushSlider">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="UpcoButton_2">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>11</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/Up/加.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="collilabellabel_2">
            <property name="minimumSize">
             <size>
              <width>31</width>
              <height>21</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">font-family: MiSans;
font: 18pt &quot;Microsoft YaHei UI&quot;;
font-weight: 400;
font-size: 14px;
color: #030B1C;
text-align: left;</string>
            </property>
            <property name="text">
             <string>50%</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="networkLayout">
        <property name="spacing">
         <number>10</number>
        </property>
        <item>
         <spacer name="bottomHorizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Policy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>30</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="IpButton_2">
          <property name="minimumSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">color: #F53D3A;
background-image: url(:/IP/IP设置.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="IpButton_3">
          <property name="minimumSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>40</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">color: #F53D3A;
border: none;
background-image: url(:/connect/已连接.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="Ipinforlabel_2">
          <property name="minimumSize">
           <size>
            <width>41</width>
            <height>29</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">font-family: MiSans;
font-weight: 400;
font-size: 14px;
color: #F53D3A;
text-align: left;</string>
          </property>
          <property name="text">
           <string>已连接</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="bottomHorizontalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Policy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>30</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="versionLayout">
        <item>
         <widget class="QLabel" name="CVlabel_2">
          <property name="minimumSize">
           <size>
            <width>101</width>
            <height>29</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">font-family: MiSans;
font-weight: 400;
font-size: 14px;
color: #B4B4B4;
text-align: left;</string>
          </property>
          <property name="text">
           <string>CV: FR20-3.8.2</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="Versionlabel_2">
          <property name="minimumSize">
           <size>
            <width>91</width>
            <height>29</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">font-family: MiSans;
font-weight: 400;
font-size: 14px;
color: #B4B4B4;
text-align: left;</string>
          </property>
          <property name="text">
           <string>Version: 1.0.0</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../resources/main_window/resources.qrc"/>
 </resources>
 <connections/>
</ui>
