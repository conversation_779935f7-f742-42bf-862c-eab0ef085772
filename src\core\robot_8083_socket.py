import socket
from threading import Thread, Lock
from PySide6.QtCore import QObject, Signal
import struct
import time
from ..common.log import Logger

"""
该方法的作用是连接robot 8083端口，并持续监听返回值，
将推送的数据进行解析，通过信号的方式发送出去，
需要使用状态数据的地方可以连接该信号获取数据
机器人8083反馈数据解析类
"""


class Robot8083Socket(QObject):
    data_signal = Signal(dict)
    # 新增状态信号，方便UI更新
    connected = Signal()
    disconnected = Signal()

    _instance = None
    _lock = Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls, ip=None, port=None):
        if cls._instance is None:
            if ip is None or port is None:
                raise ValueError("首次获取实例时必须提供 ip 和 port")
            cls._instance = cls(ip, port)
        return cls._instance

    def __init__(self, ip, port):
        super().__init__()
        if hasattr(self, '_initialized'):
            return

        self.ip = ip
        self.port = port
        self.socket = None
        self._is_running = False
        self.control_thread = None
        self.buffer = b''
        self._initialized = True

        self.start()

    def start(self):
        """启动主控制线程。"""
        if self._is_running:
            Logger.warning("8083: 控制线程已在运行。")
            return

        Logger.info("8083: 启动控制线程。")
        self._is_running = True
        self.control_thread = Thread(target=self._main_loop, daemon=True)
        self.control_thread.start()

    def _main_loop(self):
        """
        主控制循环：负责连接、接收数据和自动重连。
        这是整个类的核心驱动力。
        """
        while self._is_running:
            try:
                # --- 连接阶段 ---
                Logger.info(f"8083: 正在尝试连接 -> {self.ip}:{self.port}...")
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.socket.settimeout(5.0)  # 为连接设置超时
                self.socket.connect((self.ip, self.port))
                self.socket.settimeout(5.0)

                Logger.info("8083: 连接成功。")
                self.connected.emit()

                # --- 数据接收阶段 ---
                self._receive_data_loop()

            except (socket.timeout, OSError) as e:
                Logger.warning(f"8083: 连接或通信失败: {e}")

            finally:
                if self.socket:
                    self.socket.close()
                self.disconnected.emit()

                if self._is_running:
                    Logger.info("8083: 3秒后将尝试重连...")
                    time.sleep(3)

        Logger.info("8083: 主循环已退出。")

    def _receive_data_loop(self):
        """专门用于接收数据的内部循环。"""
        while self._is_running:
            data = self.socket.recv(1024)
            if not data:
                Logger.warning("8083: 远端关闭了连接。")
                break  # 连接已断开，跳出接收循环，外层循环将处理重连

            self.buffer += data
            self._process_buffer()  # 数据包解析逻辑

    def _process_buffer(self):
        """处理缓冲区中的数据，解析完整的数据包 """
        while True:
            # 只处理协议格式（帧头0x5A5A）
            header_pos = self.buffer.find(b'\x5a\x5a')
            if header_pos != -1:
                if self._process_new_protocol(header_pos):
                    continue
                    # 数据不足，等待更多数据
            break

    def _process_new_protocol(self, header_pos):
        """处理协议格式数据包"""
        # 确保缓冲区至少有协议头（5字节）
        if len(self.buffer) < header_pos + 5:
            self.buffer = self.buffer[header_pos:]
            return False

        # 解析帧头
        header = struct.unpack('H', self.buffer[header_pos:header_pos + 2])[0]
        if header != 0x5A5A:
            self.buffer = self.buffer[header_pos + 1:]
            return True

        # 解析数据长度
        data_length = struct.unpack('H', self.buffer[header_pos + 3:header_pos + 5])[0]
        total_packet_length = 5 + data_length + 2

        # 判断是否有完整的数据包
        if len(self.buffer) >= header_pos + total_packet_length:
            data_content = self.buffer[header_pos + 5:header_pos + 5 + data_length]
            self.buffer = self.buffer[header_pos + total_packet_length:]

            parsed_data = self.parse_protocol(data_content)
            if parsed_data:
                self.data_signal.emit(parsed_data)
            return True
        else:
            self.buffer = self.buffer[header_pos:]
            return False

    def parse_protocol(self, data_content):
        """解析协议数据"""
        parsed_data = {}
        offset = 0

        # 程序运行状态
        program_state = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['program_state'] = program_state
        offset += 1

        # 故障码
        error_code = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['error_code'] = error_code
        offset += 1

        # 机器人模式
        robot_mode = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['robot_mode'] = robot_mode
        offset += 1

        # 1 - 6 轴当前关节位置
        for i in range(6):
            joint_position = struct.unpack('d', data_content[offset:offset + 8])[0]
            parsed_data[f'jt_cur_pos{i + 1}'] = round(joint_position, 3)
            offset += 8

        # 工具当前位置和姿态
        for i in range(6):
            tool_position = struct.unpack('d', data_content[offset:offset + 8])[0]
            parsed_data[f'tl_cur_pos{i + 1}'] = round(tool_position, 3)
            offset += 8

        # 工具号
        tool_num = struct.unpack('i', data_content[offset:offset + 4])[0]
        parsed_data['toolNum'] = tool_num
        offset += 4

        # 1 - 6 轴当前扭矩
        for i in range(6):
            joint_torque = struct.unpack('d', data_content[offset:offset + 8])[0]
            parsed_data[f'jt_cur_tor{i + 1}'] = joint_torque
            offset += 8

        # 运行程序名
        program_name = data_content[offset:offset + 20].decode('utf-8').rstrip('\x00')
        parsed_data['program_name'] = program_name
        offset += 20

        # 运行程序总行数
        prog_total_line = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['prog_total_line'] = prog_total_line
        offset += 1

        # 运行程序当前行
        prog_cur_line = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['prog_cur_line'] = prog_cur_line
        offset += 1

        # 控制箱数字量 IO 输出 15 - 8
        cl_dgt_output_h = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['cl_dgt_output_h'] = cl_dgt_output_h
        offset += 1

        # 控制箱数字量 IO 输出 7 - 0
        cl_dgt_output_l = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['cl_dgt_output_l'] = cl_dgt_output_l
        offset += 1

        # 解析控制箱DO状态 (DO0-DO15)
        do_states = self.parse_control_box_do_states(cl_dgt_output_l, cl_dgt_output_h)
        parsed_data.update(do_states)

        # 工具数字量 IO 输出 7 - 0
        tl_dgt_output_l = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['tl_dgt_output_l'] = tl_dgt_output_l
        # 解析工具DO状态
        tool_do_states = self.parse_tool_do_states(tl_dgt_output_l)
        parsed_data.update(tool_do_states)
        offset += 1

        # 控制箱数字量 IO 输入 15 - 8
        cl_dgt_input_h = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['cl_dgt_input_h'] = cl_dgt_input_h
        offset += 1

        # 控制箱数字量 IO 输入 7 - 0
        cl_dgt_input_l = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['cl_dgt_input_l'] = cl_dgt_input_l

        # 解析控制箱DI状态 (DI0-DI15)
        di_states = self.parse_control_box_di_states(cl_dgt_input_l, cl_dgt_input_h)
        parsed_data.update(di_states)
        offset += 1

        # 工具数字量 IO 输入 7 - 0
        tl_dgt_input_l = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['tl_dgt_input_l'] = tl_dgt_input_l
        # 解析工具DI状态
        tool_di_states = self.parse_tool_di_states(tl_dgt_input_l)
        parsed_data.update(tool_di_states)
        offset += 1

        # 更新全局IO状态
        self.update_global_io_status(parsed_data)

        # 力/扭矩传感器数据
        for i in range(6):
            ft_data = struct.unpack('d', data_content[offset:offset + 8])[0]
            parsed_data[f'FT_data{i}'] = ft_data
            offset += 8

        # 力/扭矩传感器激活状态
        ft_act_status = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['FT_ActStatus'] = ft_act_status
        offset += 1

        # 急停标志
        emergency_stop = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['EmergencyStop'] = emergency_stop
        offset += 1

        # 机器人运动到位信号
        robot_motion_done = struct.unpack('i', data_content[offset:offset + 4])[0]
        parsed_data['robot_motion_done'] = robot_motion_done
        offset += 4

        # 夹爪运动到位信号
        gripper_motion_done = struct.unpack('B', data_content[offset:offset + 1])[0]
        parsed_data['gripper_motion_done'] = gripper_motion_done
        offset += 1

        return parsed_data

    def parse_control_box_di_states(self, input_l, input_h):
        """解析控制箱数字量输入状态 DI0-DI15"""
        states = {}

        # 解析DI0-DI7 (来自input_l)
        for i in range(8):
            bit = (input_l >> i) & 0x01
            # 根据实际硬件确定是否需要取反，这里假设高电平有效
            states[f'DI{i}'] = bit

        # 解析DI8-DI15 (来自input_h)
        for i in range(8):
            bit = (input_h >> i) & 0x01
            states[f'DI{i + 8}'] = bit

        return states

    def parse_control_box_do_states(self, output_l, output_h=0):
        """解析控制箱数字量输出状态 DO0-DO15"""
        states = {}

        # DO0-DO7 来自output_l
        for i in range(8):
            bit = (output_l >> i) & 0x01
            states[f'DO{i}'] = bit

        # DO8-DO15 来自output_h（如果提供）
        for i in range(8):
            bit = (output_h >> i) & 0x01
            states[f'DO{i + 8}'] = bit

        return states

    def parse_tool_di_states(self, tl_input):
        """解析工具数字量输入状态 (仅bit0和bit1有效)"""
        # 工具IO通常是低电平有效
        tdi0 = ((tl_input >> 0) & 0x01) ^ 0x01  # bit0取反
        tdi1 = ((tl_input >> 1) & 0x01) ^ 0x01  # bit1取反

        return {
            'TDI0': tdi0,
            'TDI1': tdi1
        }

    def parse_tool_do_states(self, tl_output):
        """解析工具数字量输出状态 (仅bit0和bit1有效)"""
        tdo0 = (tl_output >> 0) & 0x01  # bit0
        tdo1 = (tl_output >> 1) & 0x01  # bit1

        return {
            'TDO0': tdo0,
            'TDO1': tdo1
        }

    def update_global_io_status(self, parsed_data):
        """更新全局IO状态"""
        try:
            from ..common import global_vars as gv

            # 更新控制箱DI状态
            for i in range(16):
                di_key = f'DI{i}'
                if di_key in parsed_data:
                    gv.IO_STATUS['DI'][di_key] = parsed_data[di_key]
            # 输出DI状态
            # print("控制箱DI状态:", gv.IO_STATUS['DI'])

            # 更新控制箱DO状态
            for i in range(16):
                do_key = f'DO{i}'
                if do_key in parsed_data:
                    gv.IO_STATUS['DO'][do_key] = parsed_data[do_key]
            # 输出DO状态
            # print("控制箱DO状态:", gv.IO_STATUS['DO'])

        except Exception as e:
            Logger.error(f"更新全局IO状态失败: {e}")

    def reconfigure(self, new_ip, new_port):
        """公开方法:用于重新配置IP和端口。"""
        with self._lock:
            if self.ip == new_ip and self.port == new_port:
                Logger.info(f"8083: IP地址 {new_ip} 未改变，无需重新配置。")
                return

            Logger.info(f"8083: 正在重新配置IP -> {new_ip}:{new_port}")

            # 1. 停止当前的主循环
            self.close()

            # 2. 更新配置
            self.ip = new_ip
            self.port = new_port

            # 3. 重启主循环
            self.start()

    def close(self):
        """优雅地关闭socket和控制线程。"""
        Logger.info("8083: 正在关闭...")
        self._is_running = False
        if self.socket:
            try:
                # shutdown可以打断阻塞的recv调用
                self.socket.shutdown(socket.SHUT_RDWR)
            except OSError:
                pass
            self.socket.close()

        if self.control_thread and self.control_thread.is_alive():
            self.control_thread.join(timeout=1.5)
        Logger.info("8083: 已关闭。")
