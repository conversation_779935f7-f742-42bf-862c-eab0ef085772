from PySide6.QtCore import QObject, Signal, QTimer, QEventLoop
from typing import Optional, Dict, Any
from .robot_tcp_controller import RobotTcpController
from .cmd_tool import RobotCommand
from ..common.log import Logger
from ..common import global_vars as gv


class CommandManager(QObject):
    """
    统一的机器人指令发送管理器

    职责说明：
    1. 直接发送 (send_command): 用于实时控制指令，如停止、暂停等
    2. 同步发送 (send_and_wait): 用于需要等待响应的设置指令，如速度、碰撞等级
    3. 异步发送 (send_async): 用于不需要等待响应的普通指令
    4. 批量发送 (send_batch): 用于发送指令序列
    5. 指令响应处理: 处理机器人返回的响应数据  (此功能内置在同步发送中，不需单独调用)
    6. 超时和错误处理: 处理指令发送过程中的各种异常情况 (此功能 自动集成在所有方法中)
    """

    # 统一的指令发送信号
    command_sent = Signal(str)  # 指令发送成功信号
    command_failed = Signal(str, str)  # 指令发送失败信号 (指令, 错误信息)

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        super().__init__()
        self._initialized = True
        self.robot_controller = None
        self.logger = Logger
        self.send_command_signal = None

    def set_robot_controller(self, controller: RobotTcpController):
        """设置机器人控制器"""
        self.robot_controller = controller

    def set_send_command_signal(self, signal):
        """用于设置发送指令的信号"""
        self.send_command_signal = signal

    @staticmethod
    def extract_request_id(command: str) -> str:
        """提取请求ID"""
        try:
            if not command or not isinstance(command, str):
                print(f"错误: 命令为空或不是字符串: {command}")
                return None

            # 查找第一个III
            start = command.find("III")
            if start == -1:
                print(f"错误: 命令中未找到第一个III分隔符: {command}")
                return None

            # 查找第二个III
            second_start = command.find("III", start + 3)
            if second_start == -1:
                print(f"错误: 命令中未找到第二个III分隔符: {command}")
                return None

            # 查找第三个III
            end = command.find("III", second_start + 3)
            if end == -1:
                # 如果没有第三个III，返回从第二个III之后到字符串结尾的部分
                request_id = command[second_start + 3:]
            else:
                # 如果有第三个III，返回第二个和第三个III之间的部分
                request_id = command[second_start + 3: end]

            print(f"提取到请求ID: {request_id} 从命令: {command[:50]}...")
            return request_id

        except Exception as e:
            print(f"提取请求ID时发生异常: {e}, 命令: {command}")
            return None

    def send_command(self, command: str, command_type: str = "direct") -> bool:
        """
        直接发送指令 - 用于实时控制

        Args:
            command: 要发送的指令
            command_type: 指令类型，用于日志记录

        Returns:
            bool: 发送是否成功
        """
        if not self._check_connection():
            return False

        try:
            self.robot_controller.send_command(command)
            self.logger.info(f"[{command_type}] 指令发送成功: {command}")
            self.command_sent.emit(command)
            return True
        except Exception as e:
            error_msg = f"指令发送失败: {str(e)}"
            self.logger.error(f"[{command_type}] {error_msg}")
            self.command_failed.emit(command, error_msg)
            return False

    def send_and_wait(self, command: str, timeout: int = 6000, command_type: str = "sync") -> Dict[str, Any]:
        """
        同步发送指令并等待响应 - 用于设置类指令

        Args:
            command: 要发送的指令
            timeout: 超时时间(毫秒)
            command_type: 指令类型，用于日志记录

        Returns:
            Dict: 包含success和response的结果字典
        """
        if not self._check_connection():
            return {"success": False, "error": "机器人未连接"}

        try:
            result = self._execute_sync_command(command, timeout)
            if result.get("success"):
                self.logger.info(f"[{command_type}] 同步指令执行成功: {command}")
                self.command_sent.emit(command)
            else:
                error_msg = result.get("error", "未知错误")
                self.logger.error(f"[{command_type}] 同步指令执行失败: {error_msg}")
                self.command_failed.emit(command, error_msg)
            return result
        except Exception as e:
            error_msg = f"同步指令执行异常: {str(e)}"
            self.logger.error(f"[{command_type}] {error_msg}")
            self.command_failed.emit(command, error_msg)
            return {"success": False, "error": error_msg}

    def _execute_sync_command(self, command: str, timeout: int = 6000) -> dict:
        """执行同步指令并等待响应（原RobotCommandExecutor的核心功能）"""
        if not self.robot_controller:
            return {"success": False, "error": "未设置 robot_controller"}

        # 添加命令验证
        if not command or command.strip() == "":
            return {"success": False, "error": "命令为空"}

        print(f"准备执行同步命令: {command[:100]}...")

        request_id = self.extract_request_id(command)
        if not request_id:
            error_msg = f"无法提取 requestid，命令格式可能有误: {command[:100]}..."
            print(error_msg)
            return {"success": False, "error": "无法提取 requestid"}

        result = {"success": False, "response": None}
        loop = QEventLoop()

        def handler(resp):
            print(f"Received response: {resp}")
            resp_id = self.extract_request_id(resp)
            if resp_id == '102':  # 停止指令 直接返回退出
                result["success"] = True
                result["response"] = resp
                loop.quit()
            if resp_id == request_id:
                result["success"] = True
                result["response"] = resp
                loop.quit()

        self.robot_controller.response_received.connect(handler)

        try:
            if self.send_command_signal is not None:
                self.send_command_signal.emit(command)
            else:
                return {"success": False, "error": "未设置 send_command_signal"}
        except Exception as e:
            self.robot_controller.response_received.disconnect(handler)
            return {"success": False, "error": f"发送失败: {str(e)}"}

        timer = QTimer()
        timer.setSingleShot(True)
        timer.timeout.connect(loop.quit)
        timer.start(timeout)

        loop.exec()

        self.robot_controller.response_received.disconnect(handler)
        timer.deleteLater()

        return result

    def send_async(self, command: str, command_type: str = "async") -> bool:
        """
        异步发送指令 - 用于普通业务指令

        Args:
            command: 要发送的指令
            command_type: 指令类型，用于日志记录

        Returns:
            bool: 发送是否成功
        """
        return self.send_command(command, command_type)

    def send_batch(self, commands: list, command_type: str = "batch") -> Dict[str, Any]:
        """
        批量发送指令序列

        Args:
            commands: 指令列表
            command_type: 指令类型，用于日志记录

        Returns:
            Dict: 包含成功和失败指令统计的结果
        """
        if not self._check_connection():
            return {"success": False, "error": "机器人未连接", "sent": 0, "failed": len(commands)}

        sent_count = 0
        failed_count = 0
        failed_commands = []

        for i, command in enumerate(commands):
            try:
                if self.send_command(command, f"{command_type}[{i + 1}/{len(commands)}]"):
                    sent_count += 1
                else:
                    failed_count += 1
                    failed_commands.append(command)
            except Exception as e:
                failed_count += 1
                failed_commands.append(command)
                self.logger.error(f"[{command_type}] 批量指令第{i + 1}条发送异常: {str(e)}")

        result = {
            "success": failed_count == 0,
            "sent": sent_count,
            "failed": failed_count,
            "failed_commands": failed_commands
        }

        self.logger.info(f"[{command_type}] 批量指令发送完成: 成功{sent_count}条, 失败{failed_count}条")
        return result

    def _check_connection(self) -> bool:
        """检查机器人连接状态"""
        if not self.robot_controller:
            self.logger.error("机器人控制器未设置")
            return False

        if not (gv.is_8083_connected and gv.is_8080_connected):
            self.logger.warning("机器人未完全连接 (8080或8083端口未连接)")
            return False

        return True

    # 便捷方法 - 常用指令的封装
    def send_stop_command(self) -> bool:
        """发送停止指令"""
        stop_cmd = RobotCommand.get_command_str('STOP', None, 102)
        return self.send_command(stop_cmd, "停止")

    def send_pause_command(self) -> bool:
        """发送暂停指令"""
        pause_cmd = RobotCommand.get_command_str('PAUSE', None, 103)
        return self.send_command(pause_cmd, "暂停")

    def send_resume_command(self) -> bool:
        """发送恢复指令"""
        resume_cmd = RobotCommand.get_command_str('RESUME', None, 104)
        return self.send_command(resume_cmd, "恢复")

    def send_speed_command(self, speed: float) -> Dict[str, Any]:
        """发送速度设置指令"""
        speed_cmd = RobotCommand.get_command_str('SetSpeed', {'speed': str(speed)}, 206)
        return self.send_and_wait(speed_cmd, command_type="速度设置")

    def send_collision_command(self, collision_level: float) -> bool:
        """发送碰撞等级设置指令"""
        params = {
            "type": 1,
            "J1": str(collision_level),
            "J2": str(collision_level),
            "J3": str(collision_level),
            "J4": str(collision_level),
            "J5": str(collision_level),
            "J6": str(collision_level)
        }
        collision_cmd = RobotCommand.get_command_str('SetAnticollision', params, 305)
        return self.send_async(collision_cmd, "碰撞等级设置")


# 全局单例实例
command_manager = CommandManager()