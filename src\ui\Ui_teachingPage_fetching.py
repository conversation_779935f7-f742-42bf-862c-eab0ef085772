# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'teachingPage_fetching.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QB<PERSON>, QColor, QC<PERSON>alGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QGridLayout, QGroupBox, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QSizePolicy,
    QVBoxLayout, QWidget)

from ..resources.fetching import resources_fetching_rc

class Ui_Form_1(object):
    def setupUi(self, Form_1):
        if not Form_1.objectName():
            Form_1.setObjectName(u"Form_1")
        Form_1.resize(839, 430)
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(Form_1.sizePolicy().hasHeightForWidth())
        Form_1.setSizePolicy(sizePolicy)
        Form_1.setMinimumSize(QSize(600, 300))
        Form_1.setStyleSheet(u"QPushButton {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"                               stop:0 #4F94CD, stop:1 #63B8FF);\n"
"    color: white;\n"
"    border-radius: 6px;\n"
"    padding: 6px;\n"
"    font-weight: bold;\n"
"    min-height: 25px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"                              stop:0 #63B8FF, stop:1 #97FFFF);\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"                              stop:0 #3A5FCD, stop:1 #4F94CD);\n"
"}")
        self.horizontalLayout = QHBoxLayout(Form_1)
        self.horizontalLayout.setSpacing(4)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(6, 6, 6, 6)
        self.groupBox_5 = QGroupBox(Form_1)
        self.groupBox_5.setObjectName(u"groupBox_5")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.groupBox_5.sizePolicy().hasHeightForWidth())
        self.groupBox_5.setSizePolicy(sizePolicy1)
        self.groupBox_5.setMinimumSize(QSize(160, 0))
        self.groupBox_5.setStyleSheet(u"background-image: url(:/picture/\u4f8b\u56fe.png);\n"
"background-repeat: no-repeat;\n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")
        self.verticalLayout_3 = QVBoxLayout(self.groupBox_5)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")

        self.horizontalLayout.addWidget(self.groupBox_5)

        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setSpacing(4)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.groupBox = QGroupBox(Form_1)
        self.groupBox.setObjectName(u"groupBox")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(1)
        sizePolicy2.setHeightForWidth(self.groupBox.sizePolicy().hasHeightForWidth())
        self.groupBox.setSizePolicy(sizePolicy2)
        self.groupBox.setStyleSheet(u"QGroupBox { border: none; }")
        self.gridLayout = QGridLayout(self.groupBox)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setHorizontalSpacing(10)
        self.gridLayout.setVerticalSpacing(6)
        self.label = QLabel(self.groupBox)
        self.label.setObjectName(u"label")

        self.gridLayout.addWidget(self.label, 0, 0, 1, 1)

        self.lineEdit = QLineEdit(self.groupBox)
        self.lineEdit.setObjectName(u"lineEdit")
        sizePolicy3 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.lineEdit.sizePolicy().hasHeightForWidth())
        self.lineEdit.setSizePolicy(sizePolicy3)
        self.lineEdit.setMinimumSize(QSize(80, 25))

        self.gridLayout.addWidget(self.lineEdit, 0, 1, 1, 1)

        self.label_2 = QLabel(self.groupBox)
        self.label_2.setObjectName(u"label_2")

        self.gridLayout.addWidget(self.label_2, 0, 2, 1, 1)

        self.lineEdit_2 = QLineEdit(self.groupBox)
        self.lineEdit_2.setObjectName(u"lineEdit_2")
        sizePolicy3.setHeightForWidth(self.lineEdit_2.sizePolicy().hasHeightForWidth())
        self.lineEdit_2.setSizePolicy(sizePolicy3)
        self.lineEdit_2.setMinimumSize(QSize(80, 25))

        self.gridLayout.addWidget(self.lineEdit_2, 0, 3, 1, 1)

        self.label_3 = QLabel(self.groupBox)
        self.label_3.setObjectName(u"label_3")

        self.gridLayout.addWidget(self.label_3, 1, 0, 1, 1)

        self.lineEdit_3 = QLineEdit(self.groupBox)
        self.lineEdit_3.setObjectName(u"lineEdit_3")
        sizePolicy3.setHeightForWidth(self.lineEdit_3.sizePolicy().hasHeightForWidth())
        self.lineEdit_3.setSizePolicy(sizePolicy3)
        self.lineEdit_3.setMinimumSize(QSize(80, 25))

        self.gridLayout.addWidget(self.lineEdit_3, 1, 1, 1, 1)

        self.label_4 = QLabel(self.groupBox)
        self.label_4.setObjectName(u"label_4")

        self.gridLayout.addWidget(self.label_4, 1, 2, 1, 1)

        self.lineEdit_4 = QLineEdit(self.groupBox)
        self.lineEdit_4.setObjectName(u"lineEdit_4")
        sizePolicy3.setHeightForWidth(self.lineEdit_4.sizePolicy().hasHeightForWidth())
        self.lineEdit_4.setSizePolicy(sizePolicy3)
        self.lineEdit_4.setMinimumSize(QSize(80, 25))

        self.gridLayout.addWidget(self.lineEdit_4, 1, 3, 1, 1)

        self.label_17 = QLabel(self.groupBox)
        self.label_17.setObjectName(u"label_17")

        self.gridLayout.addWidget(self.label_17, 2, 0, 1, 1)

        self.lineEdit_11 = QLineEdit(self.groupBox)
        self.lineEdit_11.setObjectName(u"lineEdit_11")
        sizePolicy3.setHeightForWidth(self.lineEdit_11.sizePolicy().hasHeightForWidth())
        self.lineEdit_11.setSizePolicy(sizePolicy3)
        self.lineEdit_11.setMinimumSize(QSize(200, 25))

        self.gridLayout.addWidget(self.lineEdit_11, 2, 1, 1, 3)

        self.label_7 = QLabel(self.groupBox)
        self.label_7.setObjectName(u"label_7")

        self.gridLayout.addWidget(self.label_7, 3, 0, 1, 4)

        self.label_8 = QLabel(self.groupBox)
        self.label_8.setObjectName(u"label_8")

        self.gridLayout.addWidget(self.label_8, 4, 0, 1, 4)

        self.pushButton = QPushButton(self.groupBox)
        self.pushButton.setObjectName(u"pushButton")
        self.pushButton.setStyleSheet(u"background: #0FFFFF;")

        self.gridLayout.addWidget(self.pushButton, 5, 0, 1, 2)

        self.pushButton_2 = QPushButton(self.groupBox)
        self.pushButton_2.setObjectName(u"pushButton_2")
        self.pushButton_2.setStyleSheet(u"background: #1FFFFF;\n"
"")

        self.gridLayout.addWidget(self.pushButton_2, 5, 2, 1, 2)


        self.verticalLayout.addWidget(self.groupBox)

        self.groupBox_2 = QGroupBox(Form_1)
        self.groupBox_2.setObjectName(u"groupBox_2")
        sizePolicy2.setHeightForWidth(self.groupBox_2.sizePolicy().hasHeightForWidth())
        self.groupBox_2.setSizePolicy(sizePolicy2)
        self.groupBox_2.setMinimumSize(QSize(0, 0))
        self.groupBox_2.setStyleSheet(u"QGroupBox { border: none; }")
        self.gridLayout_2 = QGridLayout(self.groupBox_2)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setHorizontalSpacing(10)
        self.gridLayout_2.setVerticalSpacing(6)
        self.label_27 = QLabel(self.groupBox_2)
        self.label_27.setObjectName(u"label_27")

        self.gridLayout_2.addWidget(self.label_27, 0, 0, 1, 1)

        self.lineEdit_21 = QLineEdit(self.groupBox_2)
        self.lineEdit_21.setObjectName(u"lineEdit_21")
        sizePolicy3.setHeightForWidth(self.lineEdit_21.sizePolicy().hasHeightForWidth())
        self.lineEdit_21.setSizePolicy(sizePolicy3)
        self.lineEdit_21.setMinimumSize(QSize(120, 25))

        self.gridLayout_2.addWidget(self.lineEdit_21, 0, 1, 1, 1)

        self.label_28 = QLabel(self.groupBox_2)
        self.label_28.setObjectName(u"label_28")

        self.gridLayout_2.addWidget(self.label_28, 1, 0, 1, 1)

        self.lineEdit_22 = QLineEdit(self.groupBox_2)
        self.lineEdit_22.setObjectName(u"lineEdit_22")
        sizePolicy3.setHeightForWidth(self.lineEdit_22.sizePolicy().hasHeightForWidth())
        self.lineEdit_22.setSizePolicy(sizePolicy3)
        self.lineEdit_22.setMinimumSize(QSize(120, 25))

        self.gridLayout_2.addWidget(self.lineEdit_22, 1, 1, 1, 1)

        self.label_29 = QLabel(self.groupBox_2)
        self.label_29.setObjectName(u"label_29")

        self.gridLayout_2.addWidget(self.label_29, 2, 0, 1, 1)

        self.lineEdit_23 = QLineEdit(self.groupBox_2)
        self.lineEdit_23.setObjectName(u"lineEdit_23")
        sizePolicy3.setHeightForWidth(self.lineEdit_23.sizePolicy().hasHeightForWidth())
        self.lineEdit_23.setSizePolicy(sizePolicy3)
        self.lineEdit_23.setMinimumSize(QSize(120, 25))

        self.gridLayout_2.addWidget(self.lineEdit_23, 2, 1, 1, 1)

        self.pushButton_8 = QPushButton(self.groupBox_2)
        self.pushButton_8.setObjectName(u"pushButton_8")
        self.pushButton_8.setStyleSheet(u"background: #1FFFFF;")

        self.gridLayout_2.addWidget(self.pushButton_8, 3, 0, 1, 2)


        self.verticalLayout.addWidget(self.groupBox_2)


        self.horizontalLayout.addLayout(self.verticalLayout)

        self.verticalLayout_2 = QVBoxLayout()
        self.verticalLayout_2.setSpacing(4)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.groupBox_3 = QGroupBox(Form_1)
        self.groupBox_3.setObjectName(u"groupBox_3")
        sizePolicy2.setHeightForWidth(self.groupBox_3.sizePolicy().hasHeightForWidth())
        self.groupBox_3.setSizePolicy(sizePolicy2)
        self.groupBox_3.setMinimumSize(QSize(0, 0))
        self.groupBox_3.setStyleSheet(u"QGroupBox { border: none; }")
        self.horizontalLayout_2 = QHBoxLayout(self.groupBox_3)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.groupBox_8 = QGroupBox(self.groupBox_3)
        self.groupBox_8.setObjectName(u"groupBox_8")
        sizePolicy4 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy4.setHorizontalStretch(1)
        sizePolicy4.setVerticalStretch(0)
        sizePolicy4.setHeightForWidth(self.groupBox_8.sizePolicy().hasHeightForWidth())
        self.groupBox_8.setSizePolicy(sizePolicy4)
        self.groupBox_8.setStyleSheet(u"QGroupBox { border: none; }")
        self.gridLayout_3 = QGridLayout(self.groupBox_8)
        self.gridLayout_3.setObjectName(u"gridLayout_3")
        self.gridLayout_3.setHorizontalSpacing(8)
        self.gridLayout_3.setVerticalSpacing(6)
        self.label_5 = QLabel(self.groupBox_8)
        self.label_5.setObjectName(u"label_5")

        self.gridLayout_3.addWidget(self.label_5, 2, 0, 1, 2)

        self.lineEdit_12 = QLineEdit(self.groupBox_8)
        self.lineEdit_12.setObjectName(u"lineEdit_12")
        self.lineEdit_12.setMinimumSize(QSize(60, 25))

        self.gridLayout_3.addWidget(self.lineEdit_12, 1, 4, 1, 1)

        self.lineEdit_5 = QLineEdit(self.groupBox_8)
        self.lineEdit_5.setObjectName(u"lineEdit_5")

        self.gridLayout_3.addWidget(self.lineEdit_5, 2, 2, 1, 3)

        self.lineEdit_13 = QLineEdit(self.groupBox_8)
        self.lineEdit_13.setObjectName(u"lineEdit_13")
        self.lineEdit_13.setMinimumSize(QSize(60, 25))

        self.gridLayout_3.addWidget(self.lineEdit_13, 1, 1, 1, 1)

        self.label_18 = QLabel(self.groupBox_8)
        self.label_18.setObjectName(u"label_18")

        self.gridLayout_3.addWidget(self.label_18, 1, 3, 1, 1)

        self.pushButton_clear_11 = QPushButton(self.groupBox_8)
        self.pushButton_clear_11.setObjectName(u"pushButton_clear_11")
        self.pushButton_clear_11.setStyleSheet(u"background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"                               stop:0 #FF6B6B, stop:1 #FF8E8E);\n"
"color: white;\n"
"border-radius: 6px;\n"
"padding: 6px;\n"
"font-weight: bold;")

        self.gridLayout_3.addWidget(self.pushButton_clear_11, 3, 2, 1, 1)

        self.pushButton_5 = QPushButton(self.groupBox_8)
        self.pushButton_5.setObjectName(u"pushButton_5")
        self.pushButton_5.setStyleSheet(u"background: #1FFFFF;")

        self.gridLayout_3.addWidget(self.pushButton_5, 3, 3, 1, 2)

        self.lineEdit_15 = QLineEdit(self.groupBox_8)
        self.lineEdit_15.setObjectName(u"lineEdit_15")
        self.lineEdit_15.setMinimumSize(QSize(60, 25))

        self.gridLayout_3.addWidget(self.lineEdit_15, 0, 1, 1, 1)

        self.pushButton_6 = QPushButton(self.groupBox_8)
        self.pushButton_6.setObjectName(u"pushButton_6")
        self.pushButton_6.setStyleSheet(u"background: #1FFFFF;")

        self.gridLayout_3.addWidget(self.pushButton_6, 3, 0, 1, 2)

        self.label_19 = QLabel(self.groupBox_8)
        self.label_19.setObjectName(u"label_19")

        self.gridLayout_3.addWidget(self.label_19, 1, 0, 1, 1)

        self.lineEdit_14 = QLineEdit(self.groupBox_8)
        self.lineEdit_14.setObjectName(u"lineEdit_14")
        self.lineEdit_14.setMinimumSize(QSize(60, 25))

        self.gridLayout_3.addWidget(self.lineEdit_14, 0, 4, 1, 1)

        self.label_21 = QLabel(self.groupBox_8)
        self.label_21.setObjectName(u"label_21")

        self.gridLayout_3.addWidget(self.label_21, 0, 0, 1, 1)

        self.label_20 = QLabel(self.groupBox_8)
        self.label_20.setObjectName(u"label_20")

        self.gridLayout_3.addWidget(self.label_20, 0, 3, 1, 1)


        self.horizontalLayout_2.addWidget(self.groupBox_8)

        self.groupBox_9 = QGroupBox(self.groupBox_3)
        self.groupBox_9.setObjectName(u"groupBox_9")
        sizePolicy4.setHeightForWidth(self.groupBox_9.sizePolicy().hasHeightForWidth())
        self.groupBox_9.setSizePolicy(sizePolicy4)
        self.groupBox_9.setStyleSheet(u"QGroupBox { border: none; }")
        self.gridLayout_4 = QGridLayout(self.groupBox_9)
        self.gridLayout_4.setObjectName(u"gridLayout_4")
        self.gridLayout_4.setHorizontalSpacing(8)
        self.gridLayout_4.setVerticalSpacing(6)
        self.label_24 = QLabel(self.groupBox_9)
        self.label_24.setObjectName(u"label_24")

        self.gridLayout_4.addWidget(self.label_24, 0, 2, 1, 1)

        self.label_23 = QLabel(self.groupBox_9)
        self.label_23.setObjectName(u"label_23")

        self.gridLayout_4.addWidget(self.label_23, 1, 0, 1, 1)

        self.lineEdit_20 = QLineEdit(self.groupBox_9)
        self.lineEdit_20.setObjectName(u"lineEdit_20")
        self.lineEdit_20.setMinimumSize(QSize(60, 25))

        self.gridLayout_4.addWidget(self.lineEdit_20, 0, 1, 1, 1)

        self.label_22 = QLabel(self.groupBox_9)
        self.label_22.setObjectName(u"label_22")

        self.gridLayout_4.addWidget(self.label_22, 2, 0, 1, 2)

        self.lineEdit_16 = QLineEdit(self.groupBox_9)
        self.lineEdit_16.setObjectName(u"lineEdit_16")

        self.gridLayout_4.addWidget(self.lineEdit_16, 2, 2, 1, 2)

        self.pushButton_clear_14 = QPushButton(self.groupBox_9)
        self.pushButton_clear_14.setObjectName(u"pushButton_clear_14")
        self.pushButton_clear_14.setStyleSheet(u"background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"                               stop:0 #FF6B6B, stop:1 #FF8E8E);\n"
"color: white;\n"
"border-radius: 6px;\n"
"padding: 6px;\n"
"font-weight: bold;")

        self.gridLayout_4.addWidget(self.pushButton_clear_14, 3, 2, 1, 1)

        self.pushButton_7 = QPushButton(self.groupBox_9)
        self.pushButton_7.setObjectName(u"pushButton_7")
        self.pushButton_7.setStyleSheet(u"background: #1FFFFF;")

        self.gridLayout_4.addWidget(self.pushButton_7, 3, 3, 1, 1)

        self.pushButton_10 = QPushButton(self.groupBox_9)
        self.pushButton_10.setObjectName(u"pushButton_10")
        self.pushButton_10.setStyleSheet(u"background: #1FFFFF;")

        self.gridLayout_4.addWidget(self.pushButton_10, 3, 0, 1, 2)

        self.label_25 = QLabel(self.groupBox_9)
        self.label_25.setObjectName(u"label_25")

        self.gridLayout_4.addWidget(self.label_25, 1, 2, 1, 1)

        self.lineEdit_18 = QLineEdit(self.groupBox_9)
        self.lineEdit_18.setObjectName(u"lineEdit_18")

        self.gridLayout_4.addWidget(self.lineEdit_18, 0, 3, 1, 1)

        self.label_26 = QLabel(self.groupBox_9)
        self.label_26.setObjectName(u"label_26")

        self.gridLayout_4.addWidget(self.label_26, 0, 0, 1, 1)

        self.lineEdit_17 = QLineEdit(self.groupBox_9)
        self.lineEdit_17.setObjectName(u"lineEdit_17")
        self.lineEdit_17.setMinimumSize(QSize(60, 0))

        self.gridLayout_4.addWidget(self.lineEdit_17, 1, 1, 1, 1)

        self.lineEdit_19 = QLineEdit(self.groupBox_9)
        self.lineEdit_19.setObjectName(u"lineEdit_19")

        self.gridLayout_4.addWidget(self.lineEdit_19, 1, 3, 1, 1)


        self.horizontalLayout_2.addWidget(self.groupBox_9)


        self.verticalLayout_2.addWidget(self.groupBox_3)

        self.groupBox_4 = QGroupBox(Form_1)
        self.groupBox_4.setObjectName(u"groupBox_4")
        sizePolicy2.setHeightForWidth(self.groupBox_4.sizePolicy().hasHeightForWidth())
        self.groupBox_4.setSizePolicy(sizePolicy2)
        self.groupBox_4.setStyleSheet(u"QGroupBox { border: none; }")
        self.horizontalLayout_3 = QHBoxLayout(self.groupBox_4)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.groupBox_10 = QGroupBox(self.groupBox_4)
        self.groupBox_10.setObjectName(u"groupBox_10")
        sizePolicy4.setHeightForWidth(self.groupBox_10.sizePolicy().hasHeightForWidth())
        self.groupBox_10.setSizePolicy(sizePolicy4)
        self.groupBox_10.setMinimumSize(QSize(0, 0))
        self.groupBox_10.setStyleSheet(u"QGroupBox { border: none; }")
        self.gridLayout_5 = QGridLayout(self.groupBox_10)
        self.gridLayout_5.setObjectName(u"gridLayout_5")
        self.gridLayout_5.setHorizontalSpacing(10)
        self.gridLayout_5.setVerticalSpacing(6)
        self.label_31 = QLabel(self.groupBox_10)
        self.label_31.setObjectName(u"label_31")

        self.gridLayout_5.addWidget(self.label_31, 2, 0, 1, 1)

        self.lineEdit_24 = QLineEdit(self.groupBox_10)
        self.lineEdit_24.setObjectName(u"lineEdit_24")

        self.gridLayout_5.addWidget(self.lineEdit_24, 1, 0, 1, 1)

        self.label_30 = QLabel(self.groupBox_10)
        self.label_30.setObjectName(u"label_30")

        self.gridLayout_5.addWidget(self.label_30, 0, 0, 1, 1)

        self.lineEdit_25 = QLineEdit(self.groupBox_10)
        self.lineEdit_25.setObjectName(u"lineEdit_25")

        self.gridLayout_5.addWidget(self.lineEdit_25, 3, 0, 1, 1)

        self.pushButton_9 = QPushButton(self.groupBox_10)
        self.pushButton_9.setObjectName(u"pushButton_9")
        self.pushButton_9.setStyleSheet(u"background: #1FFFFF;")

        self.gridLayout_5.addWidget(self.pushButton_9, 4, 0, 1, 1)


        self.horizontalLayout_3.addWidget(self.groupBox_10)

        self.groupBox_12 = QGroupBox(self.groupBox_4)
        self.groupBox_12.setObjectName(u"groupBox_12")
        sizePolicy4.setHeightForWidth(self.groupBox_12.sizePolicy().hasHeightForWidth())
        self.groupBox_12.setSizePolicy(sizePolicy4)
        self.groupBox_12.setMinimumSize(QSize(0, 0))
        self.groupBox_12.setStyleSheet(u"QGroupBox { border: none; }")
        self.gridLayout_6 = QGridLayout(self.groupBox_12)
        self.gridLayout_6.setObjectName(u"gridLayout_6")
        self.gridLayout_6.setHorizontalSpacing(8)
        self.gridLayout_6.setVerticalSpacing(6)
        self.lineEdit_29 = QLineEdit(self.groupBox_12)
        self.lineEdit_29.setObjectName(u"lineEdit_29")

        self.gridLayout_6.addWidget(self.lineEdit_29, 3, 1, 1, 1)

        self.lineEdit_27 = QLineEdit(self.groupBox_12)
        self.lineEdit_27.setObjectName(u"lineEdit_27")

        self.gridLayout_6.addWidget(self.lineEdit_27, 1, 1, 1, 1)

        self.lineEdit_28 = QLineEdit(self.groupBox_12)
        self.lineEdit_28.setObjectName(u"lineEdit_28")

        self.gridLayout_6.addWidget(self.lineEdit_28, 2, 1, 1, 1)

        self.label_33 = QLabel(self.groupBox_12)
        self.label_33.setObjectName(u"label_33")

        self.gridLayout_6.addWidget(self.label_33, 1, 0, 1, 1)

        self.label_32 = QLabel(self.groupBox_12)
        self.label_32.setObjectName(u"label_32")

        self.gridLayout_6.addWidget(self.label_32, 0, 0, 1, 1)

        self.lineEdit_26 = QLineEdit(self.groupBox_12)
        self.lineEdit_26.setObjectName(u"lineEdit_26")

        self.gridLayout_6.addWidget(self.lineEdit_26, 0, 1, 1, 1)

        self.label_35 = QLabel(self.groupBox_12)
        self.label_35.setObjectName(u"label_35")

        self.gridLayout_6.addWidget(self.label_35, 3, 0, 1, 1)

        self.label_34 = QLabel(self.groupBox_12)
        self.label_34.setObjectName(u"label_34")

        self.gridLayout_6.addWidget(self.label_34, 2, 0, 1, 1)

        self.pushButton_clear_12 = QPushButton(self.groupBox_12)
        self.pushButton_clear_12.setObjectName(u"pushButton_clear_12")
        self.pushButton_clear_12.setStyleSheet(u"background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"                               stop:0 #FF6B6B, stop:1 #FF8E8E);\n"
"color: white;\n"
"border-radius: 6px;\n"
"padding: 6px;\n"
"font-weight: bold;")

        self.gridLayout_6.addWidget(self.pushButton_clear_12, 6, 0, 1, 1)

        self.pushButton_11 = QPushButton(self.groupBox_12)
        self.pushButton_11.setObjectName(u"pushButton_11")
        self.pushButton_11.setStyleSheet(u"background: #1FFFFF;")

        self.gridLayout_6.addWidget(self.pushButton_11, 6, 1, 1, 1)


        self.horizontalLayout_3.addWidget(self.groupBox_12)

        self.groupBox_13 = QGroupBox(self.groupBox_4)
        self.groupBox_13.setObjectName(u"groupBox_13")
        sizePolicy4.setHeightForWidth(self.groupBox_13.sizePolicy().hasHeightForWidth())
        self.groupBox_13.setSizePolicy(sizePolicy4)
        self.groupBox_13.setMinimumSize(QSize(0, 0))
        self.groupBox_13.setStyleSheet(u"QGroupBox { border: none; }")
        self.gridLayout_7 = QGridLayout(self.groupBox_13)
        self.gridLayout_7.setObjectName(u"gridLayout_7")
        self.gridLayout_7.setHorizontalSpacing(8)
        self.gridLayout_7.setVerticalSpacing(6)
        self.lineEdit_31 = QLineEdit(self.groupBox_13)
        self.lineEdit_31.setObjectName(u"lineEdit_31")

        self.gridLayout_7.addWidget(self.lineEdit_31, 3, 1, 1, 1)

        self.lineEdit_32 = QLineEdit(self.groupBox_13)
        self.lineEdit_32.setObjectName(u"lineEdit_32")

        self.gridLayout_7.addWidget(self.lineEdit_32, 1, 1, 1, 1)

        self.lineEdit_33 = QLineEdit(self.groupBox_13)
        self.lineEdit_33.setObjectName(u"lineEdit_33")

        self.gridLayout_7.addWidget(self.lineEdit_33, 0, 1, 1, 1)

        self.label_37 = QLabel(self.groupBox_13)
        self.label_37.setObjectName(u"label_37")

        self.gridLayout_7.addWidget(self.label_37, 0, 0, 1, 1)

        self.label_36 = QLabel(self.groupBox_13)
        self.label_36.setObjectName(u"label_36")

        self.gridLayout_7.addWidget(self.label_36, 3, 0, 1, 1)

        self.label_38 = QLabel(self.groupBox_13)
        self.label_38.setObjectName(u"label_38")

        self.gridLayout_7.addWidget(self.label_38, 1, 0, 1, 1)

        self.lineEdit_30 = QLineEdit(self.groupBox_13)
        self.lineEdit_30.setObjectName(u"lineEdit_30")

        self.gridLayout_7.addWidget(self.lineEdit_30, 2, 1, 1, 1)

        self.pushButton_clear_13 = QPushButton(self.groupBox_13)
        self.pushButton_clear_13.setObjectName(u"pushButton_clear_13")
        self.pushButton_clear_13.setStyleSheet(u"background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"                               stop:0 #FF6B6B, stop:1 #FF8E8E);\n"
"color: white;\n"
"border-radius: 6px;\n"
"padding: 6px;\n"
"font-weight: bold;")

        self.gridLayout_7.addWidget(self.pushButton_clear_13, 4, 0, 1, 1)

        self.label_39 = QLabel(self.groupBox_13)
        self.label_39.setObjectName(u"label_39")

        self.gridLayout_7.addWidget(self.label_39, 2, 0, 1, 1)

        self.pushButton_12 = QPushButton(self.groupBox_13)
        self.pushButton_12.setObjectName(u"pushButton_12")
        self.pushButton_12.setStyleSheet(u"background: #1FFFFF;")

        self.gridLayout_7.addWidget(self.pushButton_12, 4, 1, 1, 1)


        self.horizontalLayout_3.addWidget(self.groupBox_13)


        self.verticalLayout_2.addWidget(self.groupBox_4)


        self.horizontalLayout.addLayout(self.verticalLayout_2)

        self.horizontalLayout.setStretch(0, 1)
        self.horizontalLayout.setStretch(1, 5)

        self.retranslateUi(Form_1)

        QMetaObject.connectSlotsByName(Form_1)
    # setupUi

    def retranslateUi(self, Form_1):
        self.groupBox_5.setTitle("")
        self.groupBox.setTitle(QCoreApplication.translate("Form_1", u"\u6293\u53d6\u70b9\u8bbe\u7f6e", None))
        self.label.setText(QCoreApplication.translate("Form_1", u"X", None))
        self.label_2.setText(QCoreApplication.translate("Form_1", u"Y", None))
        self.label_3.setText(QCoreApplication.translate("Form_1", u"Z", None))
        self.label_4.setText(QCoreApplication.translate("Form_1", u"RZ", None))
        self.label_17.setText(QCoreApplication.translate("Form_1", u"\u57fa\u5750\u6807Z", None))
        self.label_7.setText(QCoreApplication.translate("Form_1", u"\u793a\u6559\u65f6\u4e0d\u9700\u8981\u5e26\u7bb1\u5b50\u64cd\u4f5c", None))
        self.label_8.setText(QCoreApplication.translate("Form_1", u"\u793a\u6559\u4e00\u6b21\u6539\u53d8\u7bb1\u5b50\u540e\u65e0\u9700\u6bcf\u6b21\u793a\u6559", None))
        self.pushButton.setText(QCoreApplication.translate("Form_1", u"\u8fd4\u56de\u793a\u6559\u70b9", None))
        self.pushButton_2.setText(QCoreApplication.translate("Form_1", u"\u793a\u6559\u70b9\u4f4d", None))
        self.groupBox_2.setTitle("")
        self.label_27.setText(QCoreApplication.translate("Form_1", u"\u6293\u53d6\u7b49\u5f85\u9ad8\u5ea6", None))
        self.lineEdit_21.setText("")
        self.label_28.setText(QCoreApplication.translate("Form_1", u"\u722c\u53d6\u62ac\u5347\u9ad8\u5ea6", None))
        self.label_29.setText(QCoreApplication.translate("Form_1", u"\u62ac\u5347\u540e\u7b49\u5f85\u65f6\u95f4", None))
        self.pushButton_8.setText(QCoreApplication.translate("Form_1", u"\u4fdd\u5b58", None))
        self.groupBox_3.setTitle("")
        self.groupBox_8.setTitle(QCoreApplication.translate("Form_1", u"\u6293\u53d6\u540e\u8fc7\u6e21\u70b9\u4f4d", None))
        self.label_5.setText(QCoreApplication.translate("Form_1", u"\u57fa\u5750\u6807Z", None))
        self.label_18.setText(QCoreApplication.translate("Form_1", u"RZ", None))
        self.pushButton_clear_11.setText(QCoreApplication.translate("Form_1", u"\u6e05\u9664", None))
        self.pushButton_5.setText(QCoreApplication.translate("Form_1", u"\u793a\u6559", None))
        self.pushButton_6.setText(QCoreApplication.translate("Form_1", u"\u8fd4\u56de\u793a\u6559\u70b9", None))
        self.label_19.setText(QCoreApplication.translate("Form_1", u"Z", None))
        self.label_21.setText(QCoreApplication.translate("Form_1", u"X", None))
        self.label_20.setText(QCoreApplication.translate("Form_1", u"Y", None))
        self.groupBox_9.setTitle(QCoreApplication.translate("Form_1", u"\u653e\u7f6e\u540e\u8fc7\u6e21\u70b9\u4f4d", None))
        self.label_24.setText(QCoreApplication.translate("Form_1", u"      Y", None))
        self.label_23.setText(QCoreApplication.translate("Form_1", u"Z", None))
        self.label_22.setText(QCoreApplication.translate("Form_1", u"\u57fa\u5750\u6807Z", None))
        self.pushButton_clear_14.setText(QCoreApplication.translate("Form_1", u"\u6e05\u9664", None))
        self.pushButton_7.setText(QCoreApplication.translate("Form_1", u"\u793a\u6559", None))
        self.pushButton_10.setText(QCoreApplication.translate("Form_1", u"\u8fd4\u56de\u793a\u6559\u70b9", None))
        self.label_25.setText(QCoreApplication.translate("Form_1", u"     RZ", None))
        self.label_26.setText(QCoreApplication.translate("Form_1", u"X", None))
        self.groupBox_4.setTitle("")
        self.groupBox_10.setTitle("")
        self.label_31.setText(QCoreApplication.translate("Form_1", u"\u653e\u7f6e\u540e\u62ac\u5347\u9ad8\u5ea6", None))
        self.label_30.setText(QCoreApplication.translate("Form_1", u"\u653e\u7f6e\u524d\u60ac\u505c\u9ad8\u5ea6", None))
        self.pushButton_9.setText(QCoreApplication.translate("Form_1", u"\u4fdd\u5b58", None))
        self.groupBox_12.setTitle(QCoreApplication.translate("Form_1", u"\u5de6\u6258\u76d8\u96f6\u70b9\u4f4d\u7f6e", None))
        self.label_33.setText(QCoreApplication.translate("Form_1", u"Y", None))
        self.label_32.setText(QCoreApplication.translate("Form_1", u"X", None))
        self.label_35.setText(QCoreApplication.translate("Form_1", u"RZ", None))
        self.label_34.setText(QCoreApplication.translate("Form_1", u"Z", None))
        self.pushButton_clear_12.setText(QCoreApplication.translate("Form_1", u"\u6e05\u9664", None))
        self.pushButton_11.setText(QCoreApplication.translate("Form_1", u"\u4fdd\u5b58", None))
        self.groupBox_13.setTitle(QCoreApplication.translate("Form_1", u"\u53f3\u6258\u76d8\u96f6\u70b9\u4f4d\u7f6e", None))
        self.label_37.setText(QCoreApplication.translate("Form_1", u"X", None))
        self.label_36.setText(QCoreApplication.translate("Form_1", u"RZ", None))
        self.label_38.setText(QCoreApplication.translate("Form_1", u"Y", None))
        self.pushButton_clear_13.setText(QCoreApplication.translate("Form_1", u"\u6e05\u9664", None))
        self.label_39.setText(QCoreApplication.translate("Form_1", u"Z", None))
        self.pushButton_12.setText(QCoreApplication.translate("Form_1", u"\u4fdd\u5b58", None))
        pass
    # retranslateUi

