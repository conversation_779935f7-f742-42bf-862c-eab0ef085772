# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'layerStyle.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON><PERSON>, QColor, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ursor,
    <PERSON><PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QFormLayout,
    QFrame, QGridLayout, QHBoxLayout, QLabel,
    QPushButton, QRadioButton, QScrollArea, QSizePolicy,
    QSpacerItem, QSpinBox, QStackedWidget, QTextEdit,
    QVBoxLayout, QWidget)

class Ui_formula_layer_style(object):
    def setupUi(self, formula_layer_style):
        if not formula_layer_style.objectName():
            formula_layer_style.setObjectName(u"formula_layer_style")
        formula_layer_style.resize(880, 518)
        formula_layer_style.setMinimumSize(QSize(561, 413))
        formula_layer_style.setMaximumSize(QSize(16777215, 16777215))
        font = QFont()
        font.setPointSize(6)
        formula_layer_style.setFont(font)
        self.frame = QFrame(formula_layer_style)
        self.frame.setObjectName(u"frame")
        self.frame.setGeometry(QRect(9, 30, 820, 50))
        self.frame.setMinimumSize(QSize(815, 50))
        self.frame.setMaximumSize(QSize(820, 50))
        self.frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_3 = QHBoxLayout(self.frame)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.boxpallet_btn = QPushButton(self.frame)
        self.boxpallet_btn.setObjectName(u"boxpallet_btn")
        self.boxpallet_btn.setMinimumSize(QSize(0, 30))
        self.boxpallet_btn.setMaximumSize(QSize(16777215, 50))
        font1 = QFont()
        font1.setPointSize(12)
        self.boxpallet_btn.setFont(font1)
        self.boxpallet_btn.setStyleSheet(u"")
        self.boxpallet_btn.setCheckable(True)
        self.boxpallet_btn.setChecked(True)
        self.boxpallet_btn.setAutoExclusive(True)

        self.horizontalLayout_3.addWidget(self.boxpallet_btn)

        self.laystyle_btn = QPushButton(self.frame)
        self.laystyle_btn.setObjectName(u"laystyle_btn")
        self.laystyle_btn.setMinimumSize(QSize(0, 30))
        self.laystyle_btn.setMaximumSize(QSize(16777215, 50))
        font2 = QFont()
        self.laystyle_btn.setFont(font2)
        self.laystyle_btn.setStyleSheet(u"font-size:16px")
        self.laystyle_btn.setCheckable(True)
        self.laystyle_btn.setAutoExclusive(True)

        self.horizontalLayout_3.addWidget(self.laystyle_btn)

        self.stacktype_btn = QPushButton(self.frame)
        self.stacktype_btn.setObjectName(u"stacktype_btn")
        self.stacktype_btn.setMinimumSize(QSize(0, 30))
        self.stacktype_btn.setMaximumSize(QSize(16777215, 50))
        self.stacktype_btn.setFont(font1)
        self.stacktype_btn.setCheckable(True)
        self.stacktype_btn.setAutoExclusive(True)

        self.horizontalLayout_3.addWidget(self.stacktype_btn)

        self.stackedWidget = QStackedWidget(formula_layer_style)
        self.stackedWidget.setObjectName(u"stackedWidget")
        self.stackedWidget.setEnabled(True)
        self.stackedWidget.setGeometry(QRect(9, 65, 862, 444))
        self.stackedWidget.setMinimumSize(QSize(85, 34))
        self.page_layer = QWidget()
        self.page_layer.setObjectName(u"page_layer")
        self.frame_4 = QFrame(self.page_layer)
        self.frame_4.setObjectName(u"frame_4")
        self.frame_4.setGeometry(QRect(270, 30, 551, 411))
        self.frame_4.setMinimumSize(QSize(551, 411))
        self.frame_4.setMaximumSize(QSize(551, 411))
        self.frame_4.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_4.setFrameShadow(QFrame.Shadow.Raised)
        self.PalletVisualization = QWidget(self.frame_4)
        self.PalletVisualization.setObjectName(u"PalletVisualization")
        self.PalletVisualization.setGeometry(QRect(10, 20, 338, 382))
        self.PalletVisualization.setMinimumSize(QSize(338, 382))
        self.PalletVisualization.setStyleSheet(u"background-color: rgb(240, 245, 255);")
        self.frame_7 = QFrame(self.frame_4)
        self.frame_7.setObjectName(u"frame_7")
        self.frame_7.setGeometry(QRect(350, 70, 190, 205))
        self.frame_7.setMinimumSize(QSize(190, 150))
        self.frame_7.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_7.setFrameShadow(QFrame.Shadow.Raised)
        self.down_btn = QPushButton(self.frame_7)
        self.down_btn.setObjectName(u"down_btn")
        self.down_btn.setGeometry(QRect(70, 130, 50, 50))
        self.down_btn.setMinimumSize(QSize(50, 50))
        self.down_btn.setMaximumSize(QSize(40, 40))
        self.down_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: #4A86E8;\n"
"    color: white;\n"
"    border-radius: 25px;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6A9AEA;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3A76D8;\n"
"}")
        self.up_btn = QPushButton(self.frame_7)
        self.up_btn.setObjectName(u"up_btn")
        self.up_btn.setGeometry(QRect(70, 40, 50, 50))
        self.up_btn.setMinimumSize(QSize(50, 50))
        self.up_btn.setMaximumSize(QSize(40, 40))
        self.up_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: #4A86E8;\n"
"    color: white;\n"
"    border-radius: 25px;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6A9AEA;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3A76D8;\n"
"}")
        self.rotate_btn = QPushButton(self.frame_7)
        self.rotate_btn.setObjectName(u"rotate_btn")
        self.rotate_btn.setGeometry(QRect(140, 150, 50, 50))
        self.rotate_btn.setMinimumSize(QSize(50, 50))
        self.rotate_btn.setMaximumSize(QSize(40, 40))
        self.rotate_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: #4A86E8;\n"
"    color: white;\n"
"    border-radius: 25px;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6A9AEA;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3A76D8;\n"
"}")
        self.layoutWidget_3 = QWidget(self.frame_7)
        self.layoutWidget_3.setObjectName(u"layoutWidget_3")
        self.layoutWidget_3.setGeometry(QRect(10, 10, 171, 28))
        self.horizontalLayout_2 = QHBoxLayout(self.layoutWidget_3)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.label_15 = QLabel(self.layoutWidget_3)
        self.label_15.setObjectName(u"label_15")
        self.label_15.setMinimumSize(QSize(12, 12))
        self.label_15.setMaximumSize(QSize(24, 16777215))
        self.label_15.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.horizontalLayout_2.addWidget(self.label_15)

        self.step_spin = QSpinBox(self.layoutWidget_3)
        self.step_spin.setObjectName(u"step_spin")
        self.step_spin.setMinimumSize(QSize(121, 26))
        self.step_spin.setMaximumSize(QSize(100000, 100000))
        self.step_spin.setFont(font2)
        self.step_spin.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.step_spin.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.step_spin.setMinimum(0)
        self.step_spin.setMaximum(10000)
        self.step_spin.setValue(10)

        self.horizontalLayout_2.addWidget(self.step_spin)

        self.rotate_btn_2 = QPushButton(self.frame_7)
        self.rotate_btn_2.setObjectName(u"rotate_btn_2")
        self.rotate_btn_2.setGeometry(QRect(10, 150, 50, 50))
        self.rotate_btn_2.setMinimumSize(QSize(50, 50))
        self.rotate_btn_2.setMaximumSize(QSize(40, 40))
        self.rotate_btn_2.setStyleSheet(u"QPushButton {\n"
"    background-color: #4A86E8;\n"
"    color: white;\n"
"    border-radius: 25px;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6A9AEA;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3A76D8;\n"
"}")
        self.right_btn = QPushButton(self.frame_7)
        self.right_btn.setObjectName(u"right_btn")
        self.right_btn.setGeometry(QRect(120, 90, 50, 50))
        self.right_btn.setMinimumSize(QSize(50, 50))
        self.right_btn.setMaximumSize(QSize(40, 40))
        self.right_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: #4A86E8;\n"
"    color: white;\n"
"    border-radius: 25px;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6A9AEA;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3A76D8;\n"
"}")
        self.left_btn = QPushButton(self.frame_7)
        self.left_btn.setObjectName(u"left_btn")
        self.left_btn.setGeometry(QRect(30, 90, 50, 50))
        self.left_btn.setMinimumSize(QSize(50, 50))
        self.left_btn.setMaximumSize(QSize(40, 40))
        self.left_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: #4A86E8;\n"
"    color: white;\n"
"    border-radius: 25px;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6A9AEA;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #3A76D8;\n"
"}")
        self.frame_8 = QFrame(self.frame_4)
        self.frame_8.setObjectName(u"frame_8")
        self.frame_8.setGeometry(QRect(350, 20, 191, 51))
        self.frame_8.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_8.setFrameShadow(QFrame.Shadow.Raised)
        self.add_box_btn = QPushButton(self.frame_8)
        self.add_box_btn.setObjectName(u"add_box_btn")
        self.add_box_btn.setEnabled(True)
        self.add_box_btn.setGeometry(QRect(5, 10, 85, 34))
        self.add_box_btn.setMinimumSize(QSize(85, 34))
        self.add_box_btn.setMaximumSize(QSize(85, 34))
        self.add_box_btn.setFont(font1)
        self.delete_box_btn = QPushButton(self.frame_8)
        self.delete_box_btn.setObjectName(u"delete_box_btn")
        self.delete_box_btn.setEnabled(False)
        self.delete_box_btn.setGeometry(QRect(100, 10, 85, 34))
        self.delete_box_btn.setMinimumSize(QSize(85, 34))
        self.delete_box_btn.setMaximumSize(QSize(85, 34))
        self.delete_box_btn.setFont(font1)
        self.frame_9 = QFrame(self.frame_4)
        self.frame_9.setObjectName(u"frame_9")
        self.frame_9.setGeometry(QRect(350, 280, 191, 95))
        self.frame_9.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_9.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget = QWidget(self.frame_9)
        self.layoutWidget.setObjectName(u"layoutWidget")
        self.layoutWidget.setGeometry(QRect(10, 0, 182, 92))
        self.verticalLayout = QVBoxLayout(self.layoutWidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.top_alignment = QPushButton(self.layoutWidget)
        self.top_alignment.setObjectName(u"top_alignment")
        self.top_alignment.setMinimumSize(QSize(40, 40))
        self.top_alignment.setMaximumSize(QSize(40, 40))

        self.horizontalLayout_5.addWidget(self.top_alignment)

        self.bottom_alignment = QPushButton(self.layoutWidget)
        self.bottom_alignment.setObjectName(u"bottom_alignment")
        self.bottom_alignment.setMinimumSize(QSize(40, 40))
        self.bottom_alignment.setMaximumSize(QSize(40, 40))

        self.horizontalLayout_5.addWidget(self.bottom_alignment)

        self.left_alignment = QPushButton(self.layoutWidget)
        self.left_alignment.setObjectName(u"left_alignment")
        self.left_alignment.setMinimumSize(QSize(40, 40))
        self.left_alignment.setMaximumSize(QSize(40, 40))

        self.horizontalLayout_5.addWidget(self.left_alignment)

        self.right_alignment = QPushButton(self.layoutWidget)
        self.right_alignment.setObjectName(u"right_alignment")
        self.right_alignment.setMinimumSize(QSize(40, 40))
        self.right_alignment.setMaximumSize(QSize(40, 40))

        self.horizontalLayout_5.addWidget(self.right_alignment)


        self.verticalLayout.addLayout(self.horizontalLayout_5)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontal_distribution = QPushButton(self.layoutWidget)
        self.horizontal_distribution.setObjectName(u"horizontal_distribution")
        self.horizontal_distribution.setMinimumSize(QSize(40, 40))
        self.horizontal_distribution.setMaximumSize(QSize(40, 40))

        self.horizontalLayout_6.addWidget(self.horizontal_distribution)

        self.vertical_distribution = QPushButton(self.layoutWidget)
        self.vertical_distribution.setObjectName(u"vertical_distribution")
        self.vertical_distribution.setMinimumSize(QSize(40, 40))
        self.vertical_distribution.setMaximumSize(QSize(40, 40))

        self.horizontalLayout_6.addWidget(self.vertical_distribution)

        self.horizontal_flip = QPushButton(self.layoutWidget)
        self.horizontal_flip.setObjectName(u"horizontal_flip")
        self.horizontal_flip.setMinimumSize(QSize(40, 40))
        self.horizontal_flip.setMaximumSize(QSize(40, 40))

        self.horizontalLayout_6.addWidget(self.horizontal_flip)

        self.Vertical_flip = QPushButton(self.layoutWidget)
        self.Vertical_flip.setObjectName(u"Vertical_flip")
        self.Vertical_flip.setMinimumSize(QSize(40, 40))
        self.Vertical_flip.setMaximumSize(QSize(40, 40))

        self.horizontalLayout_6.addWidget(self.Vertical_flip)


        self.verticalLayout.addLayout(self.horizontalLayout_6)

        self.layoutWidget1 = QWidget(self.frame_4)
        self.layoutWidget1.setObjectName(u"layoutWidget1")
        self.layoutWidget1.setGeometry(QRect(360, 380, 181, 31))
        self.horizontalLayout_7 = QHBoxLayout(self.layoutWidget1)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.select_multiple_box = QRadioButton(self.layoutWidget1)
        self.select_multiple_box.setObjectName(u"select_multiple_box")
        self.select_multiple_box.setFont(font2)
        self.select_multiple_box.setStyleSheet(u"font-size:12px")

        self.horizontalLayout_7.addWidget(self.select_multiple_box)

        self.save_plan = QPushButton(self.layoutWidget1)
        self.save_plan.setObjectName(u"save_plan")
        font3 = QFont()
        font3.setBold(True)
        self.save_plan.setFont(font3)
        self.save_plan.setStyleSheet(u"QPushButton {\n"
"    background-color: #4A86E8;\n"
"    color: white;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6A9AEA;\n"
"}\n"
"QPushButton:pressed {\n"
"    margin-top: 1px;\n"
"    margin-bottom: -1px;\n"
"};\n"
"font-size:12px")

        self.horizontalLayout_7.addWidget(self.save_plan)

        self.frame_10 = QFrame(self.page_layer)
        self.frame_10.setObjectName(u"frame_10")
        self.frame_10.setGeometry(QRect(0, 30, 268, 226))
        self.frame_10.setMinimumSize(QSize(268, 226))
        self.frame_10.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_10.setFrameShadow(QFrame.Shadow.Raised)
        self.plan_widget_2 = QWidget(self.frame_10)
        self.plan_widget_2.setObjectName(u"plan_widget_2")
        self.plan_widget_2.setGeometry(QRect(10, 10, 248, 171))
        self.plan_widget_2.setStyleSheet(u"background-color: #FFFFFF;")
        self.label_plan = QLabel(self.plan_widget_2)
        self.label_plan.setObjectName(u"label_plan")
        self.label_plan.setGeometry(QRect(30, 10, 180, 30))
        self.label_plan.setMinimumSize(QSize(180, 30))
        self.label_plan.setMaximumSize(QSize(180, 30))
        self.label_plan.setFont(font2)
        self.label_plan.setStyleSheet(u"background-color: #0851A0;\n"
"border-radius: 15px 15px 15px 15px;\n"
"color:#FFFFFF;\n"
"text-align:center;\n"
"font-size:16px")
        self.label_plan.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layoutWidget_2 = QWidget(self.plan_widget_2)
        self.layoutWidget_2.setObjectName(u"layoutWidget_2")
        self.layoutWidget_2.setGeometry(QRect(0, 90, 241, 22))
        self.horizontalLayout_8 = QHBoxLayout(self.layoutWidget_2)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.previous_plan = QPushButton(self.layoutWidget_2)
        self.previous_plan.setObjectName(u"previous_plan")
        self.previous_plan.setMinimumSize(QSize(20, 20))
        self.previous_plan.setMaximumSize(QSize(20, 20))
        self.previous_plan.setStyleSheet(u"background-color:#07509F;\n"
"color:#FFFFFF\n"
"")

        self.horizontalLayout_8.addWidget(self.previous_plan)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_8.addItem(self.horizontalSpacer_2)

        self.next_plan = QPushButton(self.layoutWidget_2)
        self.next_plan.setObjectName(u"next_plan")
        self.next_plan.setMinimumSize(QSize(20, 20))
        self.next_plan.setMaximumSize(QSize(20, 20))
        self.next_plan.setStyleSheet(u"background-color:#07509F;\n"
"color:#FFFFFF\n"
"")

        self.horizontalLayout_8.addWidget(self.next_plan)

        self.plan_vision = QFrame(self.plan_widget_2)
        self.plan_vision.setObjectName(u"plan_vision")
        self.plan_vision.setGeometry(QRect(20, 50, 201, 111))
        self.plan_vision.setFrameShape(QFrame.Shape.StyledPanel)
        self.plan_vision.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget2 = QWidget(self.frame_10)
        self.layoutWidget2.setObjectName(u"layoutWidget2")
        self.layoutWidget2.setGeometry(QRect(10, 180, 239, 31))
        self.horizontalLayout_4 = QHBoxLayout(self.layoutWidget2)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.new_pattern_btn = QPushButton(self.layoutWidget2)
        self.new_pattern_btn.setObjectName(u"new_pattern_btn")
        self.new_pattern_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: #F76058;\n"
"    color: white;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #FC9795;\n"
"}\n"
"QPushButton:pressed {\n"
"    margin-top: 1px;\n"
"    margin-bottom: -1px;\n"
"}")

        self.horizontalLayout_4.addWidget(self.new_pattern_btn)

        self.copy_pattern_btn = QPushButton(self.layoutWidget2)
        self.copy_pattern_btn.setObjectName(u"copy_pattern_btn")
        self.copy_pattern_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: #1FB0B0;\n"
"    color: white;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #5FD0D0;\n"
"}\n"
"QPushButton:pressed {\n"
"    margin-top: 1px;\n"
"    margin-bottom: -1px;\n"
"}")

        self.horizontalLayout_4.addWidget(self.copy_pattern_btn)

        self.delete_pattern_btn = QPushButton(self.layoutWidget2)
        self.delete_pattern_btn.setObjectName(u"delete_pattern_btn")
        self.delete_pattern_btn.setStyleSheet(u"QPushButton {\n"
"    background-color: #4A86E8;\n"
"    color: white;\n"
"    font-size: 16px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6A9AEA;\n"
"}\n"
"QPushButton:pressed {\n"
"    margin-top: 1px;\n"
"    margin-bottom: -1px;\n"
"}")

        self.horizontalLayout_4.addWidget(self.delete_pattern_btn)

        self.frame_11 = QFrame(self.page_layer)
        self.frame_11.setObjectName(u"frame_11")
        self.frame_11.setGeometry(QRect(0, 270, 268, 171))
        self.frame_11.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_11.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget3 = QWidget(self.frame_11)
        self.layoutWidget3.setObjectName(u"layoutWidget3")
        self.layoutWidget3.setGeometry(QRect(10, 10, 241, 33))
        self.horizontalLayout = QHBoxLayout(self.layoutWidget3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.comboBox = QComboBox(self.layoutWidget3)
        self.comboBox.setObjectName(u"comboBox")
        self.comboBox.setMinimumSize(QSize(67, 26))
        self.comboBox.setMaximumSize(QSize(16777215, 26))

        self.horizontalLayout.addWidget(self.comboBox)

        self.generate_btn = QPushButton(self.layoutWidget3)
        self.generate_btn.setObjectName(u"generate_btn")
        self.generate_btn.setFont(font2)
        self.generate_btn.setStyleSheet(u"font-size:12px")

        self.horizontalLayout.addWidget(self.generate_btn)

        self.widget = QWidget(self.frame_11)
        self.widget.setObjectName(u"widget")
        self.widget.setGeometry(QRect(10, 40, 250, 130))
        self.widget.setMinimumSize(QSize(250, 130))
        self.widget.setMaximumSize(QSize(250, 130))
        self.gridLayout = QGridLayout(self.widget)
        self.gridLayout.setObjectName(u"gridLayout")
        self.verticalLayout_2 = QVBoxLayout()
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.lines_number_4 = QLabel(self.widget)
        self.lines_number_4.setObjectName(u"lines_number_4")
        self.lines_number_4.setFont(font2)
        self.lines_number_4.setStyleSheet(u"font-size:12px")

        self.verticalLayout_2.addWidget(self.lines_number_4)

        self.rows_spin_3 = QSpinBox(self.widget)
        self.rows_spin_3.setObjectName(u"rows_spin_3")
        self.rows_spin_3.setMinimumSize(QSize(100, 26))
        self.rows_spin_3.setMaximumSize(QSize(100, 26))
        self.rows_spin_3.setFont(font2)
        self.rows_spin_3.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.rows_spin_3.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.rows_spin_3.setMinimum(1)
        self.rows_spin_3.setMaximum(20)
        self.rows_spin_3.setValue(3)

        self.verticalLayout_2.addWidget(self.rows_spin_3)


        self.gridLayout.addLayout(self.verticalLayout_2, 0, 0, 1, 1)

        self.verticalLayout_3 = QVBoxLayout()
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.colu_number_4 = QLabel(self.widget)
        self.colu_number_4.setObjectName(u"colu_number_4")
        self.colu_number_4.setFont(font2)
        self.colu_number_4.setStyleSheet(u"font-size:12px\n"
"")

        self.verticalLayout_3.addWidget(self.colu_number_4)

        self.cols_spin_3 = QSpinBox(self.widget)
        self.cols_spin_3.setObjectName(u"cols_spin_3")
        self.cols_spin_3.setMinimumSize(QSize(100, 26))
        self.cols_spin_3.setMaximumSize(QSize(100, 26))
        self.cols_spin_3.setFont(font2)
        self.cols_spin_3.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.cols_spin_3.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.cols_spin_3.setMinimum(1)
        self.cols_spin_3.setMaximum(20)
        self.cols_spin_3.setValue(3)

        self.verticalLayout_3.addWidget(self.cols_spin_3)


        self.gridLayout.addLayout(self.verticalLayout_3, 0, 1, 1, 1)

        self.verticalLayout_4 = QVBoxLayout()
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.line_spacing_4 = QLabel(self.widget)
        self.line_spacing_4.setObjectName(u"line_spacing_4")
        self.line_spacing_4.setFont(font2)
        self.line_spacing_4.setStyleSheet(u"font-size:12px")

        self.verticalLayout_4.addWidget(self.line_spacing_4)

        self.row_spacing_spin_3 = QSpinBox(self.widget)
        self.row_spacing_spin_3.setObjectName(u"row_spacing_spin_3")
        self.row_spacing_spin_3.setMinimumSize(QSize(100, 26))
        self.row_spacing_spin_3.setMaximumSize(QSize(100, 26))
        self.row_spacing_spin_3.setFont(font2)
        self.row_spacing_spin_3.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.row_spacing_spin_3.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.row_spacing_spin_3.setMaximum(500)
        self.row_spacing_spin_3.setValue(20)

        self.verticalLayout_4.addWidget(self.row_spacing_spin_3)


        self.gridLayout.addLayout(self.verticalLayout_4, 1, 0, 1, 1)

        self.verticalLayout_5 = QVBoxLayout()
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.column_spacing_4 = QLabel(self.widget)
        self.column_spacing_4.setObjectName(u"column_spacing_4")
        self.column_spacing_4.setFont(font2)
        self.column_spacing_4.setStyleSheet(u"font-size:12px")

        self.verticalLayout_5.addWidget(self.column_spacing_4)

        self.col_spacing_spin_3 = QSpinBox(self.widget)
        self.col_spacing_spin_3.setObjectName(u"col_spacing_spin_3")
        self.col_spacing_spin_3.setMinimumSize(QSize(100, 26))
        self.col_spacing_spin_3.setMaximumSize(QSize(100, 26))
        self.col_spacing_spin_3.setFont(font2)
        self.col_spacing_spin_3.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.col_spacing_spin_3.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.col_spacing_spin_3.setMaximum(500)
        self.col_spacing_spin_3.setValue(20)

        self.verticalLayout_5.addWidget(self.col_spacing_spin_3)


        self.gridLayout.addLayout(self.verticalLayout_5, 1, 1, 1, 1)

        self.stackedWidget.addWidget(self.page_layer)
        self.page_box = QWidget()
        self.page_box.setObjectName(u"page_box")
        self.frame_2 = QFrame(self.page_box)
        self.frame_2.setObjectName(u"frame_2")
        self.frame_2.setGeometry(QRect(0, 30, 409, 413))
        self.frame_2.setMinimumSize(QSize(409, 413))
        self.frame_2.setMaximumSize(QSize(409, 413))
        self.frame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget_7 = QWidget(self.frame_2)
        self.layoutWidget_7.setObjectName(u"layoutWidget_7")
        self.layoutWidget_7.setGeometry(QRect(10, 10, 391, 32))
        self.gridLayout_7 = QGridLayout(self.layoutWidget_7)
        self.gridLayout_7.setObjectName(u"gridLayout_7")
        self.gridLayout_7.setContentsMargins(0, 0, 0, 0)
        self.save_btn_l = QPushButton(self.layoutWidget_7)
        self.save_btn_l.setObjectName(u"save_btn_l")
        self.save_btn_l.setMinimumSize(QSize(52, 30))
        self.save_btn_l.setMaximumSize(QSize(52, 30))
        self.save_btn_l.setFont(font1)

        self.gridLayout_7.addWidget(self.save_btn_l, 0, 2, 1, 1)

        self.box_size = QLabel(self.layoutWidget_7)
        self.box_size.setObjectName(u"box_size")
        self.box_size.setMinimumSize(QSize(65, 16))
        self.box_size.setMaximumSize(QSize(65, 16))
        font4 = QFont()
        font4.setFamilies([u"MiSans-Semibold"])
        font4.setWeight(QFont.DemiBold)
        self.box_size.setFont(font4)
        self.box_size.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 16px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_7.addWidget(self.box_size, 0, 0, 1, 1)

        self.horizontalSpacer_7 = QSpacerItem(200, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.gridLayout_7.addItem(self.horizontalSpacer_7, 0, 1, 1, 1)

        self.frame_box = QFrame(self.frame_2)
        self.frame_box.setObjectName(u"frame_box")
        self.frame_box.setGeometry(QRect(60, 50, 283, 167))
        self.frame_box.setMinimumSize(QSize(283, 167))
        self.frame_box.setMaximumSize(QSize(283, 167))
        self.frame_box.setLayoutDirection(Qt.LayoutDirection.LeftToRight)
        self.frame_box.setStyleSheet(u"background-color: #185FAB")
        self.frame_box.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_box.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget4 = QWidget(self.frame_2)
        self.layoutWidget4.setObjectName(u"layoutWidget4")
        self.layoutWidget4.setGeometry(QRect(208, 289, 164, 116))
        self.verticalLayout_11 = QVBoxLayout(self.layoutWidget4)
        self.verticalLayout_11.setObjectName(u"verticalLayout_11")
        self.verticalLayout_11.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_8 = QVBoxLayout()
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.label_11 = QLabel(self.layoutWidget4)
        self.label_11.setObjectName(u"label_11")
        self.label_11.setFont(font4)
        self.label_11.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_8.addWidget(self.label_11)

        self.width_box = QSpinBox(self.layoutWidget4)
        self.width_box.setObjectName(u"width_box")
        self.width_box.setMinimumSize(QSize(160, 30))
        self.width_box.setMaximumSize(QSize(160, 30))
        self.width_box.setFont(font2)
        self.width_box.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.width_box.setMinimum(0)
        self.width_box.setMaximum(10000)
        self.width_box.setValue(300)

        self.verticalLayout_8.addWidget(self.width_box)


        self.verticalLayout_11.addLayout(self.verticalLayout_8)

        self.verticalLayout_9 = QVBoxLayout()
        self.verticalLayout_9.setObjectName(u"verticalLayout_9")
        self.label_12 = QLabel(self.layoutWidget4)
        self.label_12.setObjectName(u"label_12")
        self.label_12.setFont(font4)
        self.label_12.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_9.addWidget(self.label_12)

        self.weight_box = QSpinBox(self.layoutWidget4)
        self.weight_box.setObjectName(u"weight_box")
        self.weight_box.setMinimumSize(QSize(160, 30))
        self.weight_box.setMaximumSize(QSize(160, 30))
        self.weight_box.setFont(font2)
        self.weight_box.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.weight_box.setMinimum(0)
        self.weight_box.setMaximum(10000)
        self.weight_box.setValue(300)

        self.verticalLayout_9.addWidget(self.weight_box)


        self.verticalLayout_11.addLayout(self.verticalLayout_9)

        self.layoutWidget5 = QWidget(self.frame_2)
        self.layoutWidget5.setObjectName(u"layoutWidget5")
        self.layoutWidget5.setGeometry(QRect(20, 230, 352, 54))
        self.verticalLayout_6 = QVBoxLayout(self.layoutWidget5)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.verticalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.label = QLabel(self.layoutWidget5)
        self.label.setObjectName(u"label")
        self.label.setFont(font4)
        self.label.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_6.addWidget(self.label)

        self.box_direction = QTextEdit(self.layoutWidget5)
        self.box_direction.setObjectName(u"box_direction")
        self.box_direction.setMinimumSize(QSize(350, 30))
        self.box_direction.setMaximumSize(QSize(350, 30))
        self.box_direction.setFont(font2)
        self.box_direction.setStyleSheet(u"QTextEdit::up-button, QTextEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QTextEdit::up-arrow, QTextEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QTextEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")

        self.verticalLayout_6.addWidget(self.box_direction)

        self.layoutWidget6 = QWidget(self.frame_2)
        self.layoutWidget6.setObjectName(u"layoutWidget6")
        self.layoutWidget6.setGeometry(QRect(20, 289, 164, 116))
        self.verticalLayout_12 = QVBoxLayout(self.layoutWidget6)
        self.verticalLayout_12.setObjectName(u"verticalLayout_12")
        self.verticalLayout_12.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_7 = QVBoxLayout()
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.label_6 = QLabel(self.layoutWidget6)
        self.label_6.setObjectName(u"label_6")
        self.label_6.setFont(font4)
        self.label_6.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_7.addWidget(self.label_6)

        self.length_box = QSpinBox(self.layoutWidget6)
        self.length_box.setObjectName(u"length_box")
        self.length_box.setMinimumSize(QSize(160, 30))
        self.length_box.setMaximumSize(QSize(160, 30))
        self.length_box.setFont(font2)
        self.length_box.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.length_box.setMinimum(0)
        self.length_box.setMaximum(5000)
        self.length_box.setValue(300)

        self.verticalLayout_7.addWidget(self.length_box)


        self.verticalLayout_12.addLayout(self.verticalLayout_7)

        self.verticalLayout_10 = QVBoxLayout()
        self.verticalLayout_10.setObjectName(u"verticalLayout_10")
        self.hight_textEdit = QLabel(self.layoutWidget6)
        self.hight_textEdit.setObjectName(u"hight_textEdit")
        self.hight_textEdit.setFont(font4)
        self.hight_textEdit.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_10.addWidget(self.hight_textEdit)

        self.hight_box = QSpinBox(self.layoutWidget6)
        self.hight_box.setObjectName(u"hight_box")
        self.hight_box.setMinimumSize(QSize(160, 30))
        self.hight_box.setMaximumSize(QSize(160, 30))
        self.hight_box.setFont(font2)
        self.hight_box.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.hight_box.setMinimum(0)
        self.hight_box.setMaximum(10000)
        self.hight_box.setValue(300)

        self.verticalLayout_10.addWidget(self.hight_box)


        self.verticalLayout_12.addLayout(self.verticalLayout_10)

        self.frame_3 = QFrame(self.page_box)
        self.frame_3.setObjectName(u"frame_3")
        self.frame_3.setGeometry(QRect(410, 30, 409, 413))
        self.frame_3.setMinimumSize(QSize(409, 413))
        self.frame_3.setMaximumSize(QSize(10000, 10000))
        self.frame_3.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_3.setFrameShadow(QFrame.Shadow.Raised)
        self.frame_pallet = QFrame(self.frame_3)
        self.frame_pallet.setObjectName(u"frame_pallet")
        self.frame_pallet.setGeometry(QRect(60, 50, 283, 167))
        self.frame_pallet.setMinimumSize(QSize(283, 167))
        self.frame_pallet.setMaximumSize(QSize(283, 167))
        self.frame_pallet.setStyleSheet(u"background-color: #185FAB")
        self.frame_pallet.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_pallet.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget7 = QWidget(self.frame_3)
        self.layoutWidget7.setObjectName(u"layoutWidget7")
        self.layoutWidget7.setGeometry(QRect(29, 234, 352, 170))
        self.verticalLayout_13 = QVBoxLayout(self.layoutWidget7)
        self.verticalLayout_13.setObjectName(u"verticalLayout_13")
        self.verticalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.label_13 = QLabel(self.layoutWidget7)
        self.label_13.setObjectName(u"label_13")
        self.label_13.setFont(font4)
        self.label_13.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_13.addWidget(self.label_13)

        self.length_pallet = QSpinBox(self.layoutWidget7)
        self.length_pallet.setObjectName(u"length_pallet")
        self.length_pallet.setMinimumSize(QSize(350, 30))
        self.length_pallet.setMaximumSize(QSize(350, 30))
        self.length_pallet.setFont(font2)
        self.length_pallet.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.length_pallet.setMinimum(0)
        self.length_pallet.setMaximum(10000)
        self.length_pallet.setValue(1200)

        self.verticalLayout_13.addWidget(self.length_pallet)

        self.width = QLabel(self.layoutWidget7)
        self.width.setObjectName(u"width")
        self.width.setFont(font4)
        self.width.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_13.addWidget(self.width)

        self.width_pallet = QSpinBox(self.layoutWidget7)
        self.width_pallet.setObjectName(u"width_pallet")
        self.width_pallet.setMinimumSize(QSize(350, 30))
        self.width_pallet.setMaximumSize(QSize(350, 30))
        self.width_pallet.setFont(font2)
        self.width_pallet.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.width_pallet.setMinimum(0)
        self.width_pallet.setMaximum(5000)
        self.width_pallet.setValue(1200)

        self.verticalLayout_13.addWidget(self.width_pallet)

        self.label_18 = QLabel(self.layoutWidget7)
        self.label_18.setObjectName(u"label_18")
        self.label_18.setFont(font4)
        self.label_18.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_13.addWidget(self.label_18)

        self.hight_pallet = QSpinBox(self.layoutWidget7)
        self.hight_pallet.setObjectName(u"hight_pallet")
        self.hight_pallet.setMinimumSize(QSize(350, 30))
        self.hight_pallet.setMaximumSize(QSize(350, 30))
        self.hight_pallet.setFont(font2)
        self.hight_pallet.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.hight_pallet.setMinimum(0)
        self.hight_pallet.setMaximum(10000)
        self.hight_pallet.setValue(100)

        self.verticalLayout_13.addWidget(self.hight_pallet)

        self.layoutWidget8 = QWidget(self.frame_3)
        self.layoutWidget8.setObjectName(u"layoutWidget8")
        self.layoutWidget8.setGeometry(QRect(10, 11, 391, 32))
        self.horizontalLayout_10 = QHBoxLayout(self.layoutWidget8)
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.stack_label_r = QLabel(self.layoutWidget8)
        self.stack_label_r.setObjectName(u"stack_label_r")
        self.stack_label_r.setMinimumSize(QSize(65, 16))
        self.stack_label_r.setMaximumSize(QSize(65, 16))
        self.stack_label_r.setFont(font4)
        self.stack_label_r.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 16px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.horizontalLayout_10.addWidget(self.stack_label_r)

        self.horizontalSpacer_8 = QSpacerItem(200, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_8)

        self.save_btn_r = QPushButton(self.layoutWidget8)
        self.save_btn_r.setObjectName(u"save_btn_r")
        self.save_btn_r.setMinimumSize(QSize(52, 30))
        self.save_btn_r.setMaximumSize(QSize(52, 30))
        self.save_btn_r.setFont(font1)

        self.horizontalLayout_10.addWidget(self.save_btn_r)

        self.stackedWidget.addWidget(self.page_box)
        self.page_stype = QWidget()
        self.page_stype.setObjectName(u"page_stype")
        self.frame_5 = QFrame(self.page_stype)
        self.frame_5.setObjectName(u"frame_5")
        self.frame_5.setGeometry(QRect(0, 30, 411, 413))
        self.frame_5.setMinimumSize(QSize(410, 413))
        self.frame_5.setMaximumSize(QSize(100000, 413))
        self.frame_5.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_5.setFrameShadow(QFrame.Shadow.Raised)
        self.Repright_check_l = QCheckBox(self.frame_5)
        self.Repright_check_l.setObjectName(u"Repright_check_l")
        self.Repright_check_l.setGeometry(QRect(20, 105, 50, 20))
        font5 = QFont()
        font5.setPointSize(9)
        self.Repright_check_l.setFont(font5)
        self.layer_label_17 = QLabel(self.frame_5)
        self.layer_label_17.setObjectName(u"layer_label_17")
        self.layer_label_17.setGeometry(QRect(20, 48, 24, 12))
        self.layer_label_17.setMinimumSize(QSize(24, 12))
        self.layer_label_17.setMaximumSize(QSize(24, 12))
        self.layer_label_17.setFont(font5)
        self.stack_layer_spinBox_l = QSpinBox(self.frame_5)
        self.stack_layer_spinBox_l.setObjectName(u"stack_layer_spinBox_l")
        self.stack_layer_spinBox_l.setGeometry(QRect(20, 66, 375, 30))
        self.stack_layer_spinBox_l.setMinimumSize(QSize(375, 30))
        self.stack_layer_spinBox_l.setMaximumSize(QSize(375, 30))
        self.stack_layer_spinBox_l.setFont(font2)
        self.stack_layer_spinBox_l.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.stack_layer_spinBox_l.setMinimum(0)
        self.stack_lrl_check = QCheckBox(self.frame_5)
        self.stack_lrl_check.setObjectName(u"stack_lrl_check")
        self.stack_lrl_check.setGeometry(QRect(70, 102, 70, 26))
        self.stack_lrl_check.setMinimumSize(QSize(70, 26))
        self.stack_lrl_check.setMaximumSize(QSize(70, 26))
        self.stack_lrl_check.setFont(font5)
        self.scrollArea_l = QScrollArea(self.frame_5)
        self.scrollArea_l.setObjectName(u"scrollArea_l")
        self.scrollArea_l.setGeometry(QRect(10, 130, 390, 270))
        self.scrollArea_l.setMinimumSize(QSize(390, 270))
        self.scrollArea_l.setMaximumSize(QSize(100000, 100000))
        self.scrollArea_l.setWidgetResizable(True)
        self.scrollAreaWidgetContents_4 = QWidget()
        self.scrollAreaWidgetContents_4.setObjectName(u"scrollAreaWidgetContents_4")
        self.scrollAreaWidgetContents_4.setGeometry(QRect(0, 0, 388, 268))
        self.formLayout_3 = QFormLayout(self.scrollAreaWidgetContents_4)
        self.formLayout_3.setObjectName(u"formLayout_3")
        self.scrollArea_l.setWidget(self.scrollAreaWidgetContents_4)
        self.layoutWidget9 = QWidget(self.frame_5)
        self.layoutWidget9.setObjectName(u"layoutWidget9")
        self.layoutWidget9.setGeometry(QRect(20, 10, 381, 32))
        self.gridLayout_10 = QGridLayout(self.layoutWidget9)
        self.gridLayout_10.setObjectName(u"gridLayout_10")
        self.gridLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_10.addItem(self.horizontalSpacer_10, 0, 1, 1, 1)

        self.stack_label_r_3 = QLabel(self.layoutWidget9)
        self.stack_label_r_3.setObjectName(u"stack_label_r_3")
        self.stack_label_r_3.setMinimumSize(QSize(63, 16))
        self.stack_label_r_3.setMaximumSize(QSize(63, 16))
        self.stack_label_r_3.setFont(font4)
        self.stack_label_r_3.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 16px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_10.addWidget(self.stack_label_r_3, 0, 0, 1, 1)

        self.stack_save_btn_r = QPushButton(self.layoutWidget9)
        self.stack_save_btn_r.setObjectName(u"stack_save_btn_r")
        self.stack_save_btn_r.setMinimumSize(QSize(52, 30))
        self.stack_save_btn_r.setMaximumSize(QSize(52, 30))
        self.stack_save_btn_r.setFont(font1)

        self.gridLayout_10.addWidget(self.stack_save_btn_r, 0, 2, 1, 1)

        self.frame_6 = QFrame(self.page_stype)
        self.frame_6.setObjectName(u"frame_6")
        self.frame_6.setGeometry(QRect(410, 30, 411, 413))
        self.frame_6.setMinimumSize(QSize(410, 413))
        self.frame_6.setMaximumSize(QSize(100000, 100000))
        self.frame_6.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_6.setFrameShadow(QFrame.Shadow.Raised)
        self.stack_layer_spinBox_r = QSpinBox(self.frame_6)
        self.stack_layer_spinBox_r.setObjectName(u"stack_layer_spinBox_r")
        self.stack_layer_spinBox_r.setGeometry(QRect(20, 66, 375, 30))
        self.stack_layer_spinBox_r.setMinimumSize(QSize(375, 30))
        self.stack_layer_spinBox_r.setMaximumSize(QSize(375, 30))
        self.stack_layer_spinBox_r.setFont(font2)
        self.stack_layer_spinBox_r.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.stack_layer_spinBox_r.setMinimum(0)
        self.layer_label_18 = QLabel(self.frame_6)
        self.layer_label_18.setObjectName(u"layer_label_18")
        self.layer_label_18.setGeometry(QRect(20, 48, 24, 12))
        self.layer_label_18.setMinimumSize(QSize(24, 12))
        self.layer_label_18.setMaximumSize(QSize(24, 12))
        self.layer_label_18.setFont(font5)
        self.Repright_check_r = QCheckBox(self.frame_6)
        self.Repright_check_r.setObjectName(u"Repright_check_r")
        self.Repright_check_r.setGeometry(QRect(20, 102, 50, 20))
        self.Repright_check_r.setFont(font5)
        self.stack_lrr_check = QCheckBox(self.frame_6)
        self.stack_lrr_check.setObjectName(u"stack_lrr_check")
        self.stack_lrr_check.setGeometry(QRect(70, 102, 74, 20))
        self.stack_lrr_check.setFont(font5)
        self.scrollArea_r = QScrollArea(self.frame_6)
        self.scrollArea_r.setObjectName(u"scrollArea_r")
        self.scrollArea_r.setGeometry(QRect(10, 130, 390, 270))
        self.scrollArea_r.setMinimumSize(QSize(390, 270))
        self.scrollArea_r.setMaximumSize(QSize(100000, 100000))
        self.scrollArea_r.setWidgetResizable(True)
        self.scrollAreaWidgetContents_2 = QWidget()
        self.scrollAreaWidgetContents_2.setObjectName(u"scrollAreaWidgetContents_2")
        self.scrollAreaWidgetContents_2.setGeometry(QRect(0, 0, 388, 268))
        self.formLayout = QFormLayout(self.scrollAreaWidgetContents_2)
        self.formLayout.setObjectName(u"formLayout")
        self.scrollArea_r.setWidget(self.scrollAreaWidgetContents_2)
        self.layoutWidget10 = QWidget(self.frame_6)
        self.layoutWidget10.setObjectName(u"layoutWidget10")
        self.layoutWidget10.setGeometry(QRect(20, 10, 381, 32))
        self.gridLayout_11 = QGridLayout(self.layoutWidget10)
        self.gridLayout_11.setObjectName(u"gridLayout_11")
        self.gridLayout_11.setContentsMargins(0, 0, 0, 0)
        self.stack_label_r_4 = QLabel(self.layoutWidget10)
        self.stack_label_r_4.setObjectName(u"stack_label_r_4")
        self.stack_label_r_4.setMinimumSize(QSize(63, 16))
        self.stack_label_r_4.setMaximumSize(QSize(63, 16))
        self.stack_label_r_4.setFont(font4)
        self.stack_label_r_4.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 16px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_11.addWidget(self.stack_label_r_4, 0, 0, 1, 1)

        self.stack_save_btn_l = QPushButton(self.layoutWidget10)
        self.stack_save_btn_l.setObjectName(u"stack_save_btn_l")
        self.stack_save_btn_l.setMinimumSize(QSize(52, 30))
        self.stack_save_btn_l.setMaximumSize(QSize(52, 30))
        self.stack_save_btn_l.setFont(font1)

        self.gridLayout_11.addWidget(self.stack_save_btn_l, 0, 2, 1, 1)

        self.horizontalSpacer_11 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_11.addItem(self.horizontalSpacer_11, 0, 1, 1, 1)

        self.stackedWidget.addWidget(self.page_stype)

        self.retranslateUi(formula_layer_style)

        self.stackedWidget.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(formula_layer_style)
    # setupUi

    def retranslateUi(self, formula_layer_style):
        formula_layer_style.setWindowTitle(QCoreApplication.translate("formula_layer_style", u"\u667a\u80fd\u6258\u76d8\u89c4\u5212\u5de5\u5177", None))
        self.boxpallet_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u7bb1\u5b50/\u6258\u76d8", None))
        self.laystyle_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u5c42\u6837\u5f0f", None))
        self.stacktype_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u579b\u578b", None))
        self.down_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u2193", None))
        self.up_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u2191", None))
        self.rotate_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u21ba", None))
        self.label_15.setText(QCoreApplication.translate("formula_layer_style", u"\u6b65\u957f", None))
        self.step_spin.setSuffix(QCoreApplication.translate("formula_layer_style", u"mm", None))
        self.rotate_btn_2.setText(QCoreApplication.translate("formula_layer_style", u"\u21bb", None))
        self.right_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u2192", None))
        self.left_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u2190", None))
        self.add_box_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u65b0\u589e\u7bb1\u5b50", None))
        self.delete_box_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u5220\u9664\u7bb1\u5b50", None))
        self.top_alignment.setText(QCoreApplication.translate("formula_layer_style", u"\u9876\u5bf9\u9f50", None))
        self.bottom_alignment.setText(QCoreApplication.translate("formula_layer_style", u"\u5e95\u5bf9\u9f50", None))
        self.left_alignment.setText(QCoreApplication.translate("formula_layer_style", u"\u5de6\u5bf9\u9f50", None))
        self.right_alignment.setText(QCoreApplication.translate("formula_layer_style", u"\u53f3\u5bf9\u9f50", None))
        self.horizontal_distribution.setText(QCoreApplication.translate("formula_layer_style", u"\u6c34\u5e73\u5206\u5e03", None))
        self.vertical_distribution.setText(QCoreApplication.translate("formula_layer_style", u"\u5782\u76f4\u5206\u5e03", None))
        self.horizontal_flip.setText(QCoreApplication.translate("formula_layer_style", u"\u6c34\u5e73\u7ffb\u8f6c", None))
        self.Vertical_flip.setText(QCoreApplication.translate("formula_layer_style", u"\u5782\u76f4\u7ffb\u8f6c", None))
        self.select_multiple_box.setText(QCoreApplication.translate("formula_layer_style", u"\u591a\u9009", None))
        self.save_plan.setText(QCoreApplication.translate("formula_layer_style", u"\u4fdd\u5b58", None))
        self.label_plan.setText(QCoreApplication.translate("formula_layer_style", u"A", None))
        self.previous_plan.setText(QCoreApplication.translate("formula_layer_style", u"<", None))
        self.next_plan.setText(QCoreApplication.translate("formula_layer_style", u">", None))
        self.new_pattern_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u65b0\u589e", None))
        self.copy_pattern_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u590d\u5236", None))
        self.delete_pattern_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u5220\u9664", None))
        self.generate_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u751f\u6210", None))
        self.lines_number_4.setText(QCoreApplication.translate("formula_layer_style", u"\u884c\u6570", None))
        self.rows_spin_3.setSuffix("")
        self.colu_number_4.setText(QCoreApplication.translate("formula_layer_style", u"\u5217\u6570", None))
        self.cols_spin_3.setSuffix("")
        self.line_spacing_4.setText(QCoreApplication.translate("formula_layer_style", u"\u884c\u95f4\u8ddd:mm", None))
        self.row_spacing_spin_3.setSuffix(QCoreApplication.translate("formula_layer_style", u"mm", None))
        self.column_spacing_4.setText(QCoreApplication.translate("formula_layer_style", u"\u5217\u95f4\u8ddd:mm", None))
        self.col_spacing_spin_3.setSuffix(QCoreApplication.translate("formula_layer_style", u"mm", None))
        self.save_btn_l.setText(QCoreApplication.translate("formula_layer_style", u"\u4fdd\u5b58", None))
        self.box_size.setText(QCoreApplication.translate("formula_layer_style", u"\u7bb1\u5b50\u53c2\u6570", None))
        self.label_11.setText(QCoreApplication.translate("formula_layer_style", u"\u5bbd\u5ea6:mm", None))
        self.width_box.setSuffix("")
        self.label_12.setText(QCoreApplication.translate("formula_layer_style", u"\u91cd\u91cf:kg", None))
        self.weight_box.setSuffix("")
        self.label.setText(QCoreApplication.translate("formula_layer_style", u"\u6765\u6599\u65b9\u5411", None))
        self.label_6.setText(QCoreApplication.translate("formula_layer_style", u"\u957f\u5ea6:mm", None))
        self.length_box.setSuffix("")
        self.hight_textEdit.setText(QCoreApplication.translate("formula_layer_style", u"\u9ad8\u5ea6:mm", None))
        self.hight_box.setSuffix("")
        self.label_13.setText(QCoreApplication.translate("formula_layer_style", u"\u957f\u5ea6:mm", None))
        self.length_pallet.setSuffix("")
        self.width.setText(QCoreApplication.translate("formula_layer_style", u"\u5bbd\u5ea6:mm", None))
        self.width_pallet.setSuffix("")
        self.label_18.setText(QCoreApplication.translate("formula_layer_style", u"\u9ad8\u5ea6:mm", None))
        self.hight_pallet.setSuffix("")
        self.stack_label_r.setText(QCoreApplication.translate("formula_layer_style", u"\u6258\u76d8\u53c2\u6570", None))
        self.save_btn_r.setText(QCoreApplication.translate("formula_layer_style", u"\u4fdd\u5b58", None))
        self.Repright_check_l.setText(QCoreApplication.translate("formula_layer_style", u"\u91cd\u590d", None))
        self.layer_label_17.setText(QCoreApplication.translate("formula_layer_style", u"\u5c42\u6570", None))
        self.stack_layer_spinBox_l.setSuffix("")
        self.stack_lrl_check.setText(QCoreApplication.translate("formula_layer_style", u"\u5de6\u53f3\u4e00\u81f4", None))
        self.stack_label_r_3.setText(QCoreApplication.translate("formula_layer_style", u"\u5de6\u579b\u579b\u578b", None))
        self.stack_save_btn_r.setText(QCoreApplication.translate("formula_layer_style", u"\u4fdd\u5b58", None))
        self.stack_layer_spinBox_r.setSuffix("")
        self.layer_label_18.setText(QCoreApplication.translate("formula_layer_style", u"\u5c42\u6570", None))
        self.Repright_check_r.setText(QCoreApplication.translate("formula_layer_style", u"\u91cd\u590d", None))
        self.stack_lrr_check.setText(QCoreApplication.translate("formula_layer_style", u"\u5de6\u53f3\u4e00\u81f4", None))
        self.stack_label_r_4.setText(QCoreApplication.translate("formula_layer_style", u"\u53f3\u579b\u579b\u578b", None))
        self.stack_save_btn_l.setText(QCoreApplication.translate("formula_layer_style", u"\u4fdd\u5b58", None))
    # retranslateUi

