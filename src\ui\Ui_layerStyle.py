# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'layerStyle.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON><PERSON>, QColor, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ursor,
    <PERSON><PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QFormLayout,
    QFrame, QGridLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QRadioButton, QScrollArea,
    QSizePolicy, QSpacerItem, QSpinBox, QStackedWidget,
    QVBoxLayout, QWidget)
from ..resources.formula import formula_rc

class Ui_formula_layer_style(object):
    def setupUi(self, formula_layer_style):
        if not formula_layer_style.objectName():
            formula_layer_style.setObjectName(u"formula_layer_style")
        formula_layer_style.setEnabled(True)
        formula_layer_style.resize(871, 500)
        formula_layer_style.setMinimumSize(QSize(561, 413))
        formula_layer_style.setMaximumSize(QSize(16777215, 16777215))
        font = QFont()
        font.setPointSize(6)
        formula_layer_style.setFont(font)
        self.frame = QFrame(formula_layer_style)
        self.frame.setObjectName(u"frame")
        self.frame.setGeometry(QRect(10, 20, 839, 50))
        self.frame.setMinimumSize(QSize(839, 50))
        self.frame.setMaximumSize(QSize(839, 50))
        self.frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout_3 = QHBoxLayout(self.frame)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.boxpallet_btn = QPushButton(self.frame)
        self.boxpallet_btn.setObjectName(u"boxpallet_btn")
        self.boxpallet_btn.setMinimumSize(QSize(200, 32))
        self.boxpallet_btn.setMaximumSize(QSize(200, 32))
        font1 = QFont()
        font1.setPointSize(12)
        self.boxpallet_btn.setFont(font1)
        self.boxpallet_btn.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/button.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"color: rgb(255, 255, 255);")
        self.boxpallet_btn.setCheckable(True)
        self.boxpallet_btn.setChecked(True)
        self.boxpallet_btn.setAutoExclusive(True)

        self.horizontalLayout_3.addWidget(self.boxpallet_btn)

        self.laystyle_btn = QPushButton(self.frame)
        self.laystyle_btn.setObjectName(u"laystyle_btn")
        self.laystyle_btn.setMinimumSize(QSize(200, 32))
        self.laystyle_btn.setMaximumSize(QSize(200, 32))
        self.laystyle_btn.setFont(font1)
        self.laystyle_btn.setStyleSheet(u"")
        self.laystyle_btn.setCheckable(True)
        self.laystyle_btn.setAutoExclusive(True)

        self.horizontalLayout_3.addWidget(self.laystyle_btn)

        self.stacktype_btn = QPushButton(self.frame)
        self.stacktype_btn.setObjectName(u"stacktype_btn")
        self.stacktype_btn.setMinimumSize(QSize(200, 32))
        self.stacktype_btn.setMaximumSize(QSize(200, 32))
        self.stacktype_btn.setFont(font1)
        self.stacktype_btn.setStyleSheet(u"")
        self.stacktype_btn.setCheckable(True)
        self.stacktype_btn.setChecked(False)
        self.stacktype_btn.setAutoExclusive(True)

        self.horizontalLayout_3.addWidget(self.stacktype_btn)

        self.stackedWidget = QStackedWidget(formula_layer_style)
        self.stackedWidget.setObjectName(u"stackedWidget")
        self.stackedWidget.setEnabled(True)
        self.stackedWidget.setGeometry(QRect(10, 90, 839, 411))
        self.stackedWidget.setMinimumSize(QSize(839, 34))
        self.page_layer = QWidget()
        self.page_layer.setObjectName(u"page_layer")
        self.frame_4 = QFrame(self.page_layer)
        self.frame_4.setObjectName(u"frame_4")
        self.frame_4.setGeometry(QRect(280, -10, 561, 413))
        self.frame_4.setMinimumSize(QSize(561, 413))
        self.frame_4.setMaximumSize(QSize(561, 413))
        self.frame_4.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_4.setFrameShadow(QFrame.Shadow.Raised)
        self.PalletVisualization = QWidget(self.frame_4)
        self.PalletVisualization.setObjectName(u"PalletVisualization")
        self.PalletVisualization.setGeometry(QRect(10, 20, 338, 382))
        self.PalletVisualization.setMinimumSize(QSize(338, 382))
        self.PalletVisualization.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/pallet.png);")
        self.frame_9 = QFrame(self.frame_4)
        self.frame_9.setObjectName(u"frame_9")
        self.frame_9.setGeometry(QRect(350, 280, 201, 95))
        self.frame_9.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_9.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget = QWidget(self.frame_9)
        self.layoutWidget.setObjectName(u"layoutWidget")
        self.layoutWidget.setGeometry(QRect(10, 0, 182, 92))
        self.verticalLayout = QVBoxLayout(self.layoutWidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.top_alignment = QPushButton(self.layoutWidget)
        self.top_alignment.setObjectName(u"top_alignment")
        self.top_alignment.setMinimumSize(QSize(40, 40))
        self.top_alignment.setMaximumSize(QSize(40, 40))
        self.top_alignment.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/dingdq.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"\n"
"")

        self.horizontalLayout_5.addWidget(self.top_alignment)

        self.bottom_alignment = QPushButton(self.layoutWidget)
        self.bottom_alignment.setObjectName(u"bottom_alignment")
        self.bottom_alignment.setMinimumSize(QSize(40, 40))
        self.bottom_alignment.setMaximumSize(QSize(40, 40))
        self.bottom_alignment.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/didq.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"\n"
"")

        self.horizontalLayout_5.addWidget(self.bottom_alignment)

        self.left_alignment = QPushButton(self.layoutWidget)
        self.left_alignment.setObjectName(u"left_alignment")
        self.left_alignment.setMinimumSize(QSize(40, 40))
        self.left_alignment.setMaximumSize(QSize(40, 40))
        self.left_alignment.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/zdq.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"\n"
"")

        self.horizontalLayout_5.addWidget(self.left_alignment)

        self.right_alignment = QPushButton(self.layoutWidget)
        self.right_alignment.setObjectName(u"right_alignment")
        self.right_alignment.setMinimumSize(QSize(40, 40))
        self.right_alignment.setMaximumSize(QSize(40, 40))
        self.right_alignment.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/ydq.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"\n"
"")

        self.horizontalLayout_5.addWidget(self.right_alignment)


        self.verticalLayout.addLayout(self.horizontalLayout_5)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontal_distribution = QPushButton(self.layoutWidget)
        self.horizontal_distribution.setObjectName(u"horizontal_distribution")
        self.horizontal_distribution.setMinimumSize(QSize(40, 40))
        self.horizontal_distribution.setMaximumSize(QSize(40, 40))
        self.horizontal_distribution.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/spfb.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"\n"
"")

        self.horizontalLayout_6.addWidget(self.horizontal_distribution)

        self.vertical_distribution = QPushButton(self.layoutWidget)
        self.vertical_distribution.setObjectName(u"vertical_distribution")
        self.vertical_distribution.setMinimumSize(QSize(40, 40))
        self.vertical_distribution.setMaximumSize(QSize(40, 40))
        self.vertical_distribution.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/czfb.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"\n"
"")

        self.horizontalLayout_6.addWidget(self.vertical_distribution)

        self.horizontal_flip = QPushButton(self.layoutWidget)
        self.horizontal_flip.setObjectName(u"horizontal_flip")
        self.horizontal_flip.setMinimumSize(QSize(40, 40))
        self.horizontal_flip.setMaximumSize(QSize(40, 40))
        self.horizontal_flip.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/spfz.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"\n"
"")

        self.horizontalLayout_6.addWidget(self.horizontal_flip)

        self.Vertical_flip = QPushButton(self.layoutWidget)
        self.Vertical_flip.setObjectName(u"Vertical_flip")
        self.Vertical_flip.setMinimumSize(QSize(40, 40))
        self.Vertical_flip.setMaximumSize(QSize(40, 40))
        self.Vertical_flip.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/czfz.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"\n"
"")

        self.horizontalLayout_6.addWidget(self.Vertical_flip)


        self.verticalLayout.addLayout(self.horizontalLayout_6)

        self.layoutWidget1 = QWidget(self.frame_4)
        self.layoutWidget1.setObjectName(u"layoutWidget1")
        self.layoutWidget1.setGeometry(QRect(360, 380, 181, 31))
        self.horizontalLayout_7 = QHBoxLayout(self.layoutWidget1)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.horizontalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.select_multiple_box = QRadioButton(self.layoutWidget1)
        self.select_multiple_box.setObjectName(u"select_multiple_box")
        font2 = QFont()
        self.select_multiple_box.setFont(font2)
        self.select_multiple_box.setStyleSheet(u"font-size:12px")

        self.horizontalLayout_7.addWidget(self.select_multiple_box)

        self.save_plan = QPushButton(self.layoutWidget1)
        self.save_plan.setObjectName(u"save_plan")
        self.save_plan.setMinimumSize(QSize(48, 26))
        self.save_plan.setMaximumSize(QSize(48, 26))
        self.save_plan.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/save.png);\n"
"width: 48px;\n"
"height: 26px;\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;")

        self.horizontalLayout_7.addWidget(self.save_plan)

        self.frame_12 = QFrame(self.frame_4)
        self.frame_12.setObjectName(u"frame_12")
        self.frame_12.setGeometry(QRect(350, 70, 200, 211))
        self.frame_12.setMinimumSize(QSize(200, 150))
        self.frame_12.setStyleSheet(u"")
        self.frame_12.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_12.setFrameShadow(QFrame.Shadow.Raised)
        self.down_btn = QPushButton(self.frame_12)
        self.down_btn.setObjectName(u"down_btn")
        self.down_btn.setGeometry(QRect(80, 110, 50, 50))
        self.down_btn.setMinimumSize(QSize(50, 50))
        self.down_btn.setMaximumSize(QSize(40, 40))
        self.down_btn.setStyleSheet(u"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/down.png);")
        self.right_btn = QPushButton(self.frame_12)
        self.right_btn.setObjectName(u"right_btn")
        self.right_btn.setGeometry(QRect(120, 70, 50, 50))
        self.right_btn.setMinimumSize(QSize(50, 50))
        self.right_btn.setMaximumSize(QSize(40, 40))
        self.right_btn.setStyleSheet(u"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/right.png);")
        self.left_btn = QPushButton(self.frame_12)
        self.left_btn.setObjectName(u"left_btn")
        self.left_btn.setGeometry(QRect(40, 70, 50, 50))
        self.left_btn.setMinimumSize(QSize(50, 50))
        self.left_btn.setMaximumSize(QSize(40, 40))
        self.left_btn.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/left.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;")
        self.up_btn = QPushButton(self.frame_12)
        self.up_btn.setObjectName(u"up_btn")
        self.up_btn.setGeometry(QRect(80, 40, 50, 50))
        self.up_btn.setMinimumSize(QSize(50, 50))
        self.up_btn.setMaximumSize(QSize(40, 40))
        self.up_btn.setStyleSheet(u"background-image: url(:/layerstyle/layerstyle/up.png);\n"
"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"\n"
"")
        self.rotate_btn_2 = QPushButton(self.frame_12)
        self.rotate_btn_2.setObjectName(u"rotate_btn_2")
        self.rotate_btn_2.setGeometry(QRect(20, 150, 50, 50))
        self.rotate_btn_2.setMinimumSize(QSize(50, 50))
        self.rotate_btn_2.setMaximumSize(QSize(16777215, 40))
        self.rotate_btn_2.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/turnright.png);")
        self.rotate_btn = QPushButton(self.frame_12)
        self.rotate_btn.setObjectName(u"rotate_btn")
        self.rotate_btn.setGeometry(QRect(140, 150, 50, 50))
        self.rotate_btn.setMinimumSize(QSize(50, 50))
        self.rotate_btn.setMaximumSize(QSize(40, 40))
        self.rotate_btn.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/turnleft.png);")
        self.layoutWidget_3 = QWidget(self.frame_12)
        self.layoutWidget_3.setObjectName(u"layoutWidget_3")
        self.layoutWidget_3.setGeometry(QRect(10, 0, 189, 36))
        self.horizontalLayout_2 = QHBoxLayout(self.layoutWidget_3)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.label_15 = QLabel(self.layoutWidget_3)
        self.label_15.setObjectName(u"label_15")
        self.label_15.setMinimumSize(QSize(60, 12))
        self.label_15.setMaximumSize(QSize(60, 16777215))
        self.label_15.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.horizontalLayout_2.addWidget(self.label_15)

        self.step_spin = QLineEdit(self.layoutWidget_3)
        self.step_spin.setObjectName(u"step_spin")
        self.step_spin.setMinimumSize(QSize(121, 26))
        self.step_spin.setMaximumSize(QSize(100, 26))
        self.step_spin.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.step_spin.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_2.addWidget(self.step_spin)

        self.layoutWidget2 = QWidget(self.frame_4)
        self.layoutWidget2.setObjectName(u"layoutWidget2")
        self.layoutWidget2.setGeometry(QRect(350, 30, 201, 36))
        self.horizontalLayout_9 = QHBoxLayout(self.layoutWidget2)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.add_box_btn = QPushButton(self.layoutWidget2)
        self.add_box_btn.setObjectName(u"add_box_btn")
        self.add_box_btn.setEnabled(True)
        self.add_box_btn.setMinimumSize(QSize(85, 34))
        self.add_box_btn.setMaximumSize(QSize(85, 34))
        self.add_box_btn.setFont(font1)
        self.add_box_btn.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/add.png);")

        self.horizontalLayout_9.addWidget(self.add_box_btn)

        self.delete_box_btn = QPushButton(self.layoutWidget2)
        self.delete_box_btn.setObjectName(u"delete_box_btn")
        self.delete_box_btn.setEnabled(True)
        self.delete_box_btn.setMinimumSize(QSize(85, 34))
        self.delete_box_btn.setMaximumSize(QSize(85, 34))
        self.delete_box_btn.setFont(font1)
        self.delete_box_btn.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/remove.png);\n"
"")

        self.horizontalLayout_9.addWidget(self.delete_box_btn)

        self.frame_10 = QFrame(self.page_layer)
        self.frame_10.setObjectName(u"frame_10")
        self.frame_10.setGeometry(QRect(0, 0, 268, 226))
        self.frame_10.setMinimumSize(QSize(268, 226))
        self.frame_10.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_10.setFrameShadow(QFrame.Shadow.Raised)
        self.plan_widget_2 = QWidget(self.frame_10)
        self.plan_widget_2.setObjectName(u"plan_widget_2")
        self.plan_widget_2.setGeometry(QRect(10, 10, 248, 171))
        self.plan_widget_2.setStyleSheet(u"background-color: #FFFFFF;")
        self.label_plan = QLabel(self.plan_widget_2)
        self.label_plan.setObjectName(u"label_plan")
        self.label_plan.setGeometry(QRect(30, 10, 180, 30))
        self.label_plan.setMinimumSize(QSize(180, 30))
        self.label_plan.setMaximumSize(QSize(180, 30))
        self.label_plan.setFont(font2)
        self.label_plan.setStyleSheet(u"background-color: #0851A0;\n"
"border-radius: 15px 15px 15px 15px;\n"
"color:#FFFFFF;\n"
"text-align:center;\n"
"font-size:16px")
        self.label_plan.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layoutWidget_2 = QWidget(self.plan_widget_2)
        self.layoutWidget_2.setObjectName(u"layoutWidget_2")
        self.layoutWidget_2.setGeometry(QRect(0, 90, 241, 22))
        self.horizontalLayout_8 = QHBoxLayout(self.layoutWidget_2)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.horizontalLayout_8.setContentsMargins(0, 0, 0, 0)
        self.previous_plan = QPushButton(self.layoutWidget_2)
        self.previous_plan.setObjectName(u"previous_plan")
        self.previous_plan.setMinimumSize(QSize(20, 20))
        self.previous_plan.setMaximumSize(QSize(20, 20))
        self.previous_plan.setStyleSheet(u"background-color:#07509F;\n"
"color:#FFFFFF\n"
"")

        self.horizontalLayout_8.addWidget(self.previous_plan)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_8.addItem(self.horizontalSpacer_2)

        self.next_plan = QPushButton(self.layoutWidget_2)
        self.next_plan.setObjectName(u"next_plan")
        self.next_plan.setMinimumSize(QSize(20, 20))
        self.next_plan.setMaximumSize(QSize(20, 20))
        self.next_plan.setStyleSheet(u"background-color:#07509F;\n"
"color:#FFFFFF\n"
"")

        self.horizontalLayout_8.addWidget(self.next_plan)

        self.plan_vision = QFrame(self.plan_widget_2)
        self.plan_vision.setObjectName(u"plan_vision")
        self.plan_vision.setGeometry(QRect(20, 50, 201, 111))
        self.plan_vision.setFrameShape(QFrame.Shape.StyledPanel)
        self.plan_vision.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget3 = QWidget(self.frame_10)
        self.layoutWidget3.setObjectName(u"layoutWidget3")
        self.layoutWidget3.setGeometry(QRect(10, 180, 241, 41))
        self.horizontalLayout_4 = QHBoxLayout(self.layoutWidget3)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.new_pattern_btn = QPushButton(self.layoutWidget3)
        self.new_pattern_btn.setObjectName(u"new_pattern_btn")
        self.new_pattern_btn.setMinimumSize(QSize(65, 35))
        self.new_pattern_btn.setMaximumSize(QSize(65, 35))
        self.new_pattern_btn.setStyleSheet(u"width:65px;\n"
"height: 35px;\n"
"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/addplan.png);")

        self.horizontalLayout_4.addWidget(self.new_pattern_btn)

        self.copy_pattern_btn = QPushButton(self.layoutWidget3)
        self.copy_pattern_btn.setObjectName(u"copy_pattern_btn")
        self.copy_pattern_btn.setMinimumSize(QSize(65, 35))
        self.copy_pattern_btn.setMaximumSize(QSize(65, 35))
        self.copy_pattern_btn.setStyleSheet(u"width:65px;\n"
"height: 35px;\n"
"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/copy.png);")

        self.horizontalLayout_4.addWidget(self.copy_pattern_btn)

        self.delete_pattern_btn = QPushButton(self.layoutWidget3)
        self.delete_pattern_btn.setObjectName(u"delete_pattern_btn")
        self.delete_pattern_btn.setMinimumSize(QSize(65, 34))
        self.delete_pattern_btn.setMaximumSize(QSize(65, 34))
        self.delete_pattern_btn.setStyleSheet(u"width:65px;\n"
"height: 35px;\n"
"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/removeplan.png);")

        self.horizontalLayout_4.addWidget(self.delete_pattern_btn)

        self.frame_11 = QFrame(self.page_layer)
        self.frame_11.setObjectName(u"frame_11")
        self.frame_11.setGeometry(QRect(0, 230, 268, 171))
        self.frame_11.setMinimumSize(QSize(268, 171))
        self.frame_11.setMaximumSize(QSize(268, 171))
        self.frame_11.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_11.setFrameShadow(QFrame.Shadow.Raised)
        self.widget = QWidget(self.frame_11)
        self.widget.setObjectName(u"widget")
        self.widget.setGeometry(QRect(10, 30, 250, 130))
        self.widget.setMinimumSize(QSize(250, 130))
        self.widget.setMaximumSize(QSize(250, 130))
        self.gridLayout = QGridLayout(self.widget)
        self.gridLayout.setObjectName(u"gridLayout")
        self.lines_number_4 = QLabel(self.widget)
        self.lines_number_4.setObjectName(u"lines_number_4")
        self.lines_number_4.setFont(font2)
        self.lines_number_4.setStyleSheet(u"font-size:12px")

        self.gridLayout.addWidget(self.lines_number_4, 0, 0, 1, 1)

        self.colu_number_4 = QLabel(self.widget)
        self.colu_number_4.setObjectName(u"colu_number_4")
        self.colu_number_4.setFont(font2)
        self.colu_number_4.setStyleSheet(u"font-size:12px\n"
"")

        self.gridLayout.addWidget(self.colu_number_4, 0, 1, 1, 1)

        self.rows_spin_3 = QLineEdit(self.widget)
        self.rows_spin_3.setObjectName(u"rows_spin_3")
        self.rows_spin_3.setMinimumSize(QSize(100, 26))
        self.rows_spin_3.setMaximumSize(QSize(100, 26))
        self.rows_spin_3.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.rows_spin_3.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.rows_spin_3, 1, 0, 1, 1)

        self.cols_spin_3 = QLineEdit(self.widget)
        self.cols_spin_3.setObjectName(u"cols_spin_3")
        self.cols_spin_3.setMinimumSize(QSize(100, 26))
        self.cols_spin_3.setMaximumSize(QSize(100, 26))
        self.cols_spin_3.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.cols_spin_3.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.cols_spin_3, 1, 1, 1, 1)

        self.line_spacing_4 = QLabel(self.widget)
        self.line_spacing_4.setObjectName(u"line_spacing_4")
        self.line_spacing_4.setFont(font2)
        self.line_spacing_4.setStyleSheet(u"font-size:12px")

        self.gridLayout.addWidget(self.line_spacing_4, 2, 0, 1, 1)

        self.column_spacing_4 = QLabel(self.widget)
        self.column_spacing_4.setObjectName(u"column_spacing_4")
        self.column_spacing_4.setFont(font2)
        self.column_spacing_4.setStyleSheet(u"font-size:12px")

        self.gridLayout.addWidget(self.column_spacing_4, 2, 1, 1, 1)

        self.row_spacing_spin_3 = QLineEdit(self.widget)
        self.row_spacing_spin_3.setObjectName(u"row_spacing_spin_3")
        self.row_spacing_spin_3.setMinimumSize(QSize(100, 26))
        self.row_spacing_spin_3.setMaximumSize(QSize(100, 26))
        self.row_spacing_spin_3.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.row_spacing_spin_3.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.row_spacing_spin_3, 3, 0, 1, 1)

        self.col_spacing_spin_3 = QLineEdit(self.widget)
        self.col_spacing_spin_3.setObjectName(u"col_spacing_spin_3")
        self.col_spacing_spin_3.setMinimumSize(QSize(100, 26))
        self.col_spacing_spin_3.setMaximumSize(QSize(100, 26))
        self.col_spacing_spin_3.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.col_spacing_spin_3.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.col_spacing_spin_3, 3, 1, 1, 1)

        self.layoutWidget4 = QWidget(self.frame_11)
        self.layoutWidget4.setObjectName(u"layoutWidget4")
        self.layoutWidget4.setGeometry(QRect(20, 10, 231, 36))
        self.horizontalLayout = QHBoxLayout(self.layoutWidget4)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.comboBox = QComboBox(self.layoutWidget4)
        self.comboBox.setObjectName(u"comboBox")
        self.comboBox.setMinimumSize(QSize(67, 26))
        self.comboBox.setMaximumSize(QSize(16777215, 26))

        self.horizontalLayout.addWidget(self.comboBox)

        self.generate_btn = QPushButton(self.layoutWidget4)
        self.generate_btn.setObjectName(u"generate_btn")
        self.generate_btn.setMinimumSize(QSize(56, 34))
        self.generate_btn.setMaximumSize(QSize(56, 34))
        font3 = QFont()
        font3.setPointSize(9)
        self.generate_btn.setFont(font3)
        self.generate_btn.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/layerstyle/layerstyle/gen.png);\n"
"\n"
"")

        self.horizontalLayout.addWidget(self.generate_btn)

        self.stackedWidget.addWidget(self.page_layer)
        self.page_box = QWidget()
        self.page_box.setObjectName(u"page_box")
        self.frame_2 = QFrame(self.page_box)
        self.frame_2.setObjectName(u"frame_2")
        self.frame_2.setGeometry(QRect(0, -10, 409, 413))
        self.frame_2.setMinimumSize(QSize(409, 413))
        self.frame_2.setMaximumSize(QSize(409, 413))
        self.frame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget_7 = QWidget(self.frame_2)
        self.layoutWidget_7.setObjectName(u"layoutWidget_7")
        self.layoutWidget_7.setGeometry(QRect(10, 10, 391, 36))
        self.gridLayout_7 = QGridLayout(self.layoutWidget_7)
        self.gridLayout_7.setObjectName(u"gridLayout_7")
        self.gridLayout_7.setContentsMargins(0, 0, 0, 0)
        self.save_btn_l = QPushButton(self.layoutWidget_7)
        self.save_btn_l.setObjectName(u"save_btn_l")
        self.save_btn_l.setMinimumSize(QSize(56, 34))
        self.save_btn_l.setMaximumSize(QSize(56, 34))
        self.save_btn_l.setFont(font1)
        self.save_btn_l.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/boxpallet/boxpallet/save.png);")

        self.gridLayout_7.addWidget(self.save_btn_l, 0, 2, 1, 1)

        self.box_size = QLabel(self.layoutWidget_7)
        self.box_size.setObjectName(u"box_size")
        self.box_size.setMinimumSize(QSize(65, 16))
        self.box_size.setMaximumSize(QSize(65, 16))
        font4 = QFont()
        font4.setFamilies([u"MiSans-Semibold"])
        font4.setWeight(QFont.DemiBold)
        self.box_size.setFont(font4)
        self.box_size.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 16px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_7.addWidget(self.box_size, 0, 0, 1, 1)

        self.horizontalSpacer_7 = QSpacerItem(200, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.gridLayout_7.addItem(self.horizontalSpacer_7, 0, 1, 1, 1)

        self.frame_box = QFrame(self.frame_2)
        self.frame_box.setObjectName(u"frame_box")
        self.frame_box.setGeometry(QRect(60, 50, 283, 167))
        self.frame_box.setMinimumSize(QSize(283, 167))
        self.frame_box.setMaximumSize(QSize(283, 167))
        self.frame_box.setLayoutDirection(Qt.LayoutDirection.LeftToRight)
        self.frame_box.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/boxpallet/boxpallet/box.png);\n"
"\n"
"")
        self.frame_box.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_box.setFrameShadow(QFrame.Shadow.Raised)
        self.widget1 = QWidget(self.frame_2)
        self.widget1.setObjectName(u"widget1")
        self.widget1.setGeometry(QRect(20, 230, 354, 171))
        self.gridLayout_3 = QGridLayout(self.widget1)
        self.gridLayout_3.setObjectName(u"gridLayout_3")
        self.gridLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_6 = QVBoxLayout()
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.label = QLabel(self.widget1)
        self.label.setObjectName(u"label")
        self.label.setFont(font4)
        self.label.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_6.addWidget(self.label)

        self.box_direction = QComboBox(self.widget1)
        self.box_direction.addItem("")
        self.box_direction.addItem("")
        self.box_direction.setObjectName(u"box_direction")
        self.box_direction.setMinimumSize(QSize(350, 30))
        self.box_direction.setMaximumSize(QSize(350, 30))
        font5 = QFont()
        font5.setPointSize(10)
        self.box_direction.setFont(font5)
        self.box_direction.setStyleSheet(u"QcomboBox::up-button, QcomboBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QcomboBox::up-arrow, QcomboBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QcomboBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")

        self.verticalLayout_6.addWidget(self.box_direction)


        self.gridLayout_3.addLayout(self.verticalLayout_6, 0, 0, 1, 1)

        self.gridLayout_2 = QGridLayout()
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.label_6 = QLabel(self.widget1)
        self.label_6.setObjectName(u"label_6")
        self.label_6.setFont(font4)
        self.label_6.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_2.addWidget(self.label_6, 0, 0, 1, 1)

        self.label_11 = QLabel(self.widget1)
        self.label_11.setObjectName(u"label_11")
        self.label_11.setFont(font4)
        self.label_11.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_2.addWidget(self.label_11, 0, 1, 1, 1)

        self.length_box = QLineEdit(self.widget1)
        self.length_box.setObjectName(u"length_box")
        self.length_box.setMinimumSize(QSize(160, 30))
        self.length_box.setMaximumSize(QSize(160, 30))
        self.length_box.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.length_box.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout_2.addWidget(self.length_box, 1, 0, 1, 1)

        self.width_box = QLineEdit(self.widget1)
        self.width_box.setObjectName(u"width_box")
        self.width_box.setMinimumSize(QSize(160, 30))
        self.width_box.setMaximumSize(QSize(160, 30))
        self.width_box.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.width_box.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout_2.addWidget(self.width_box, 1, 1, 1, 1)

        self.hight_textEdit = QLabel(self.widget1)
        self.hight_textEdit.setObjectName(u"hight_textEdit")
        self.hight_textEdit.setFont(font4)
        self.hight_textEdit.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_2.addWidget(self.hight_textEdit, 2, 0, 1, 1)

        self.label_12 = QLabel(self.widget1)
        self.label_12.setObjectName(u"label_12")
        self.label_12.setFont(font4)
        self.label_12.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_2.addWidget(self.label_12, 2, 1, 1, 1)

        self.hight_box = QLineEdit(self.widget1)
        self.hight_box.setObjectName(u"hight_box")
        self.hight_box.setMinimumSize(QSize(160, 30))
        self.hight_box.setMaximumSize(QSize(160, 30))
        self.hight_box.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.hight_box.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout_2.addWidget(self.hight_box, 3, 0, 1, 1)

        self.weight_box = QLineEdit(self.widget1)
        self.weight_box.setObjectName(u"weight_box")
        self.weight_box.setMinimumSize(QSize(160, 30))
        self.weight_box.setMaximumSize(QSize(160, 30))
        self.weight_box.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.weight_box.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout_2.addWidget(self.weight_box, 3, 1, 1, 1)


        self.gridLayout_3.addLayout(self.gridLayout_2, 1, 0, 1, 1)

        self.frame_3 = QFrame(self.page_box)
        self.frame_3.setObjectName(u"frame_3")
        self.frame_3.setGeometry(QRect(430, -10, 409, 413))
        self.frame_3.setMinimumSize(QSize(409, 413))
        self.frame_3.setMaximumSize(QSize(10000, 10000))
        self.frame_3.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_3.setFrameShadow(QFrame.Shadow.Raised)
        self.frame_pallet = QFrame(self.frame_3)
        self.frame_pallet.setObjectName(u"frame_pallet")
        self.frame_pallet.setGeometry(QRect(60, 50, 283, 167))
        self.frame_pallet.setMinimumSize(QSize(283, 167))
        self.frame_pallet.setMaximumSize(QSize(283, 167))
        self.frame_pallet.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/boxpallet/boxpallet/pallet.png);\n"
"\n"
"")
        self.frame_pallet.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_pallet.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget5 = QWidget(self.frame_3)
        self.layoutWidget5.setObjectName(u"layoutWidget5")
        self.layoutWidget5.setGeometry(QRect(29, 234, 352, 170))
        self.verticalLayout_13 = QVBoxLayout(self.layoutWidget5)
        self.verticalLayout_13.setObjectName(u"verticalLayout_13")
        self.verticalLayout_13.setContentsMargins(0, 0, 0, 0)
        self.label_13 = QLabel(self.layoutWidget5)
        self.label_13.setObjectName(u"label_13")
        font6 = QFont()
        font6.setFamilies([u"MiSans-Semibold"])
        font6.setPointSize(9)
        font6.setWeight(QFont.DemiBold)
        self.label_13.setFont(font6)
        self.label_13.setStyleSheet(u"")

        self.verticalLayout_13.addWidget(self.label_13)

        self.length_pallet = QLineEdit(self.layoutWidget5)
        self.length_pallet.setObjectName(u"length_pallet")
        self.length_pallet.setMinimumSize(QSize(350, 30))
        self.length_pallet.setMaximumSize(QSize(350, 30))
        self.length_pallet.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.length_pallet.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.length_pallet)

        self.width = QLabel(self.layoutWidget5)
        self.width.setObjectName(u"width")
        self.width.setFont(font4)
        self.width.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_13.addWidget(self.width)

        self.width_pallet = QLineEdit(self.layoutWidget5)
        self.width_pallet.setObjectName(u"width_pallet")
        self.width_pallet.setMinimumSize(QSize(350, 30))
        self.width_pallet.setMaximumSize(QSize(350, 30))
        self.width_pallet.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.width_pallet.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.width_pallet)

        self.label_18 = QLabel(self.layoutWidget5)
        self.label_18.setObjectName(u"label_18")
        self.label_18.setFont(font4)
        self.label_18.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 12px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.verticalLayout_13.addWidget(self.label_18)

        self.hight_pallet = QLineEdit(self.layoutWidget5)
        self.hight_pallet.setObjectName(u"hight_pallet")
        self.hight_pallet.setMinimumSize(QSize(350, 30))
        self.hight_pallet.setMaximumSize(QSize(350, 30))
        self.hight_pallet.setStyleSheet(u"QLineEdit::up-button, QLineEdit::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QLineEdit::up-arrow, QLineEdit::down-arrow {\n"
"    image: none;\n"
"}\n"
"QLineEdit {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.hight_pallet.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.hight_pallet)

        self.layoutWidget6 = QWidget(self.frame_3)
        self.layoutWidget6.setObjectName(u"layoutWidget6")
        self.layoutWidget6.setGeometry(QRect(10, 11, 391, 36))
        self.horizontalLayout_10 = QHBoxLayout(self.layoutWidget6)
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.stack_label_r = QLabel(self.layoutWidget6)
        self.stack_label_r.setObjectName(u"stack_label_r")
        self.stack_label_r.setMinimumSize(QSize(65, 16))
        self.stack_label_r.setMaximumSize(QSize(65, 16))
        self.stack_label_r.setFont(font4)
        self.stack_label_r.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 16px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.horizontalLayout_10.addWidget(self.stack_label_r)

        self.horizontalSpacer_8 = QSpacerItem(200, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_8)

        self.save_btn_r = QPushButton(self.layoutWidget6)
        self.save_btn_r.setObjectName(u"save_btn_r")
        self.save_btn_r.setMinimumSize(QSize(56, 34))
        self.save_btn_r.setMaximumSize(QSize(56, 34))
        self.save_btn_r.setFont(font1)
        self.save_btn_r.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/boxpallet/boxpallet/save.png);")

        self.horizontalLayout_10.addWidget(self.save_btn_r)

        self.stackedWidget.addWidget(self.page_box)
        self.page_stype = QWidget()
        self.page_stype.setObjectName(u"page_stype")
        self.frame_5 = QFrame(self.page_stype)
        self.frame_5.setObjectName(u"frame_5")
        self.frame_5.setGeometry(QRect(0, -10, 411, 413))
        self.frame_5.setMinimumSize(QSize(410, 413))
        self.frame_5.setMaximumSize(QSize(100000, 413))
        self.frame_5.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_5.setFrameShadow(QFrame.Shadow.Raised)
        self.Repright_check_l = QCheckBox(self.frame_5)
        self.Repright_check_l.setObjectName(u"Repright_check_l")
        self.Repright_check_l.setGeometry(QRect(20, 105, 50, 20))
        self.Repright_check_l.setFont(font3)
        self.layer_label_17 = QLabel(self.frame_5)
        self.layer_label_17.setObjectName(u"layer_label_17")
        self.layer_label_17.setGeometry(QRect(20, 48, 24, 12))
        self.layer_label_17.setMinimumSize(QSize(24, 12))
        self.layer_label_17.setMaximumSize(QSize(24, 12))
        self.layer_label_17.setFont(font3)
        self.stack_layer_spinBox_l = QSpinBox(self.frame_5)
        self.stack_layer_spinBox_l.setObjectName(u"stack_layer_spinBox_l")
        self.stack_layer_spinBox_l.setGeometry(QRect(20, 66, 375, 30))
        self.stack_layer_spinBox_l.setMinimumSize(QSize(375, 30))
        self.stack_layer_spinBox_l.setMaximumSize(QSize(375, 30))
        self.stack_layer_spinBox_l.setFont(font2)
        self.stack_layer_spinBox_l.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.stack_layer_spinBox_l.setMinimum(0)
        self.stack_lrl_check = QCheckBox(self.frame_5)
        self.stack_lrl_check.setObjectName(u"stack_lrl_check")
        self.stack_lrl_check.setGeometry(QRect(70, 102, 70, 26))
        self.stack_lrl_check.setMinimumSize(QSize(70, 26))
        self.stack_lrl_check.setMaximumSize(QSize(70, 26))
        self.stack_lrl_check.setFont(font3)
        self.scrollArea_l = QScrollArea(self.frame_5)
        self.scrollArea_l.setObjectName(u"scrollArea_l")
        self.scrollArea_l.setGeometry(QRect(10, 130, 390, 270))
        self.scrollArea_l.setMinimumSize(QSize(390, 270))
        self.scrollArea_l.setMaximumSize(QSize(100000, 100000))
        self.scrollArea_l.setWidgetResizable(True)
        self.scrollAreaWidgetContents_4 = QWidget()
        self.scrollAreaWidgetContents_4.setObjectName(u"scrollAreaWidgetContents_4")
        self.scrollAreaWidgetContents_4.setGeometry(QRect(0, 0, 388, 268))
        self.formLayout_3 = QFormLayout(self.scrollAreaWidgetContents_4)
        self.formLayout_3.setObjectName(u"formLayout_3")
        self.scrollArea_l.setWidget(self.scrollAreaWidgetContents_4)
        self.layoutWidget7 = QWidget(self.frame_5)
        self.layoutWidget7.setObjectName(u"layoutWidget7")
        self.layoutWidget7.setGeometry(QRect(20, 10, 381, 36))
        self.gridLayout_10 = QGridLayout(self.layoutWidget7)
        self.gridLayout_10.setObjectName(u"gridLayout_10")
        self.gridLayout_10.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_10.addItem(self.horizontalSpacer_10, 0, 1, 1, 1)

        self.stack_label_r_3 = QLabel(self.layoutWidget7)
        self.stack_label_r_3.setObjectName(u"stack_label_r_3")
        self.stack_label_r_3.setMinimumSize(QSize(63, 16))
        self.stack_label_r_3.setMaximumSize(QSize(63, 16))
        self.stack_label_r_3.setFont(font4)
        self.stack_label_r_3.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 16px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_10.addWidget(self.stack_label_r_3, 0, 0, 1, 1)

        self.stack_save_btn_r = QPushButton(self.layoutWidget7)
        self.stack_save_btn_r.setObjectName(u"stack_save_btn_r")
        self.stack_save_btn_r.setMinimumSize(QSize(56, 34))
        self.stack_save_btn_r.setMaximumSize(QSize(56, 34))
        self.stack_save_btn_r.setFont(font1)
        self.stack_save_btn_r.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/boxpallet/boxpallet/save.png);")

        self.gridLayout_10.addWidget(self.stack_save_btn_r, 0, 2, 1, 1)

        self.frame_6 = QFrame(self.page_stype)
        self.frame_6.setObjectName(u"frame_6")
        self.frame_6.setGeometry(QRect(430, -10, 411, 413))
        self.frame_6.setMinimumSize(QSize(410, 413))
        self.frame_6.setMaximumSize(QSize(100000, 100000))
        self.frame_6.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_6.setFrameShadow(QFrame.Shadow.Raised)
        self.stack_layer_spinBox_r = QSpinBox(self.frame_6)
        self.stack_layer_spinBox_r.setObjectName(u"stack_layer_spinBox_r")
        self.stack_layer_spinBox_r.setGeometry(QRect(20, 66, 375, 30))
        self.stack_layer_spinBox_r.setMinimumSize(QSize(375, 30))
        self.stack_layer_spinBox_r.setMaximumSize(QSize(375, 30))
        self.stack_layer_spinBox_r.setFont(font2)
        self.stack_layer_spinBox_r.setStyleSheet(u"QSpinBox::up-button, QSpinBox::down-button {\n"
"    width: 0px;\n"
"    padding: 0px;\n"
"    border: none;\n"
"}\n"
"QSpinBox::up-arrow, QSpinBox::down-arrow {\n"
"    image: none;\n"
"}\n"
"QSpinBox {\n"
"    font-size: 12px;\n"
"    background-color: #E2EAF5;\n"
"    border-radius: 13px 13px 13px 13px;\n"
"}\n"
"")
        self.stack_layer_spinBox_r.setMinimum(0)
        self.layer_label_18 = QLabel(self.frame_6)
        self.layer_label_18.setObjectName(u"layer_label_18")
        self.layer_label_18.setGeometry(QRect(20, 48, 24, 12))
        self.layer_label_18.setMinimumSize(QSize(24, 12))
        self.layer_label_18.setMaximumSize(QSize(24, 12))
        self.layer_label_18.setFont(font3)
        self.Repright_check_r = QCheckBox(self.frame_6)
        self.Repright_check_r.setObjectName(u"Repright_check_r")
        self.Repright_check_r.setGeometry(QRect(20, 102, 50, 20))
        self.Repright_check_r.setFont(font3)
        self.stack_lrr_check = QCheckBox(self.frame_6)
        self.stack_lrr_check.setObjectName(u"stack_lrr_check")
        self.stack_lrr_check.setGeometry(QRect(70, 102, 74, 20))
        self.stack_lrr_check.setFont(font3)
        self.scrollArea_r = QScrollArea(self.frame_6)
        self.scrollArea_r.setObjectName(u"scrollArea_r")
        self.scrollArea_r.setGeometry(QRect(10, 130, 390, 270))
        self.scrollArea_r.setMinimumSize(QSize(390, 270))
        self.scrollArea_r.setMaximumSize(QSize(100000, 100000))
        self.scrollArea_r.setWidgetResizable(True)
        self.scrollAreaWidgetContents_2 = QWidget()
        self.scrollAreaWidgetContents_2.setObjectName(u"scrollAreaWidgetContents_2")
        self.scrollAreaWidgetContents_2.setGeometry(QRect(0, 0, 388, 268))
        self.formLayout = QFormLayout(self.scrollAreaWidgetContents_2)
        self.formLayout.setObjectName(u"formLayout")
        self.scrollArea_r.setWidget(self.scrollAreaWidgetContents_2)
        self.layoutWidget8 = QWidget(self.frame_6)
        self.layoutWidget8.setObjectName(u"layoutWidget8")
        self.layoutWidget8.setGeometry(QRect(20, 10, 381, 36))
        self.gridLayout_11 = QGridLayout(self.layoutWidget8)
        self.gridLayout_11.setObjectName(u"gridLayout_11")
        self.gridLayout_11.setContentsMargins(0, 0, 0, 0)
        self.stack_label_r_4 = QLabel(self.layoutWidget8)
        self.stack_label_r_4.setObjectName(u"stack_label_r_4")
        self.stack_label_r_4.setMinimumSize(QSize(63, 16))
        self.stack_label_r_4.setMaximumSize(QSize(63, 16))
        self.stack_label_r_4.setFont(font4)
        self.stack_label_r_4.setStyleSheet(u"/* QLabel \u7684\u5168\u5c40\u6837\u5f0f */\n"
"QLabel {\n"
"    font-family: \"MiSans-Semibold\"; /* \u5b57\u4f53 */\n"
"    font-weight: 600;              /* \u5b57\u91cd\uff1aSemibold \u5bf9\u5e94 600 */\n"
"    color: #000000;                /* \u6587\u672c\u989c\u8272\uff1a\u9ed1\u8272 */\n"
"    font-size: 16px;               /* \u5b57\u53f7 */\n"
"    line-height: 14px;             /* \u884c\u9ad8 */\n"
"    text-align: left;              /* \u6c34\u5e73\u5bf9\u9f50\uff1a\u5de6\u5bf9\u9f50 */\n"
"    vertical-align: middle;        /* \u5782\u76f4\u5bf9\u9f50\uff1a\u5782\u76f4\u5c45\u4e2d */\n"
"}")

        self.gridLayout_11.addWidget(self.stack_label_r_4, 0, 0, 1, 1)

        self.stack_save_btn_l = QPushButton(self.layoutWidget8)
        self.stack_save_btn_l.setObjectName(u"stack_save_btn_l")
        self.stack_save_btn_l.setMinimumSize(QSize(56, 34))
        self.stack_save_btn_l.setMaximumSize(QSize(56, 34))
        self.stack_save_btn_l.setFont(font1)
        self.stack_save_btn_l.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/boxpallet/boxpallet/save.png);")

        self.gridLayout_11.addWidget(self.stack_save_btn_l, 0, 2, 1, 1)

        self.horizontalSpacer_11 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.gridLayout_11.addItem(self.horizontalSpacer_11, 0, 1, 1, 1)

        self.stackedWidget.addWidget(self.page_stype)

        self.retranslateUi(formula_layer_style)

        self.stackedWidget.setCurrentIndex(1)


        QMetaObject.connectSlotsByName(formula_layer_style)
    # setupUi

    def retranslateUi(self, formula_layer_style):
        formula_layer_style.setWindowTitle(QCoreApplication.translate("formula_layer_style", u"\u667a\u80fd\u6258\u76d8\u89c4\u5212\u5de5\u5177", None))
        self.boxpallet_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u7bb1\u5b50/\u6258\u76d8", None))
        self.laystyle_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u5c42\u6837\u5f0f", None))
        self.stacktype_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u579b\u578b", None))
        self.top_alignment.setText("")
        self.bottom_alignment.setText("")
        self.left_alignment.setText("")
        self.right_alignment.setText("")
        self.horizontal_distribution.setText("")
        self.vertical_distribution.setText("")
        self.horizontal_flip.setText("")
        self.Vertical_flip.setText("")
        self.select_multiple_box.setText(QCoreApplication.translate("formula_layer_style", u"\u591a\u9009", None))
        self.save_plan.setText("")
        self.down_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u2193", None))
        self.right_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u2192", None))
        self.left_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u2190", None))
        self.up_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u2191", None))
        self.rotate_btn_2.setText(QCoreApplication.translate("formula_layer_style", u"\u21bb", None))
        self.rotate_btn.setText(QCoreApplication.translate("formula_layer_style", u"\u21ba", None))
        self.label_15.setText(QCoreApplication.translate("formula_layer_style", u"\u6b65\u957f:mm", None))
        self.add_box_btn.setText("")
        self.delete_box_btn.setText("")
        self.label_plan.setText(QCoreApplication.translate("formula_layer_style", u"A", None))
        self.previous_plan.setText(QCoreApplication.translate("formula_layer_style", u"<", None))
        self.next_plan.setText(QCoreApplication.translate("formula_layer_style", u">", None))
        self.new_pattern_btn.setText("")
        self.copy_pattern_btn.setText("")
        self.delete_pattern_btn.setText("")
        self.lines_number_4.setText(QCoreApplication.translate("formula_layer_style", u"\u884c\u6570", None))
        self.colu_number_4.setText(QCoreApplication.translate("formula_layer_style", u"\u5217\u6570", None))
        self.line_spacing_4.setText(QCoreApplication.translate("formula_layer_style", u"\u884c\u95f4\u8ddd:mm", None))
        self.column_spacing_4.setText(QCoreApplication.translate("formula_layer_style", u"\u5217\u95f4\u8ddd:mm", None))
        self.generate_btn.setText("")
        self.save_btn_l.setText("")
        self.box_size.setText(QCoreApplication.translate("formula_layer_style", u"\u7bb1\u5b50\u53c2\u6570", None))
        self.label.setText(QCoreApplication.translate("formula_layer_style", u"\u6765\u6599\u65b9\u5411", None))
        self.box_direction.setItemText(0, QCoreApplication.translate("formula_layer_style", u"\u6a2a\u5411", None))
        self.box_direction.setItemText(1, QCoreApplication.translate("formula_layer_style", u"\u7eb5\u5411", None))

        self.label_6.setText(QCoreApplication.translate("formula_layer_style", u"\u957f\u5ea6:mm", None))
        self.label_11.setText(QCoreApplication.translate("formula_layer_style", u"\u5bbd\u5ea6:mm", None))
        self.hight_textEdit.setText(QCoreApplication.translate("formula_layer_style", u"\u9ad8\u5ea6:mm", None))
        self.label_12.setText(QCoreApplication.translate("formula_layer_style", u"\u91cd\u91cf:kg", None))
        self.label_13.setText(QCoreApplication.translate("formula_layer_style", u"\u957f\u5ea6:mm", None))
        self.width.setText(QCoreApplication.translate("formula_layer_style", u"\u5bbd\u5ea6:mm", None))
        self.label_18.setText(QCoreApplication.translate("formula_layer_style", u"\u9ad8\u5ea6:mm", None))
        self.stack_label_r.setText(QCoreApplication.translate("formula_layer_style", u"\u6258\u76d8\u53c2\u6570", None))
        self.save_btn_r.setText("")
        self.Repright_check_l.setText(QCoreApplication.translate("formula_layer_style", u"\u91cd\u590d", None))
        self.layer_label_17.setText(QCoreApplication.translate("formula_layer_style", u"\u5c42\u6570", None))
        self.stack_layer_spinBox_l.setSuffix("")
        self.stack_lrl_check.setText(QCoreApplication.translate("formula_layer_style", u"\u5de6\u53f3\u4e00\u81f4", None))
        self.stack_label_r_3.setText(QCoreApplication.translate("formula_layer_style", u"\u5de6\u579b\u579b\u578b", None))
        self.stack_save_btn_r.setText("")
        self.stack_layer_spinBox_r.setSuffix("")
        self.layer_label_18.setText(QCoreApplication.translate("formula_layer_style", u"\u5c42\u6570", None))
        self.Repright_check_r.setText(QCoreApplication.translate("formula_layer_style", u"\u91cd\u590d", None))
        self.stack_lrr_check.setText(QCoreApplication.translate("formula_layer_style", u"\u5de6\u53f3\u4e00\u81f4", None))
        self.stack_label_r_4.setText(QCoreApplication.translate("formula_layer_style", u"\u53f3\u579b\u579b\u578b", None))
        self.stack_save_btn_l.setText("")
    # retranslateUi

