from threading import Lock

from PySide6.QtCore import QObject, Signal, Slot
from PySide6.QtNetwork import QTcpSocket

from ..common.log import Logger


class RobotTcpController(QObject):
    # 状态信号
    connected = Signal()
    disconnected = Signal()
    response_received = Signal(str)
    error_occurred = Signal(str)

    command_signal = Signal(str)

    _instance = None
    _instance_lock = Lock()
    _initialized = False  # 此变量在您的原始代码中未使用，建议用下面的方式

    @classmethod
    def get_instance(cls, host=None, port=None):
        """获取单例实例，仅首次初始化时需要传入 host 和 port"""
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    if host is None or port is None:
                        raise ValueError("必须提供 host 和 port 进行首次初始化")
                    cls._instance = cls(host, port)
        return cls._instance

    def __init__(self, host, port):
        """防止外部直接调用 instance 之外的方式创建实例"""
        # 使用 hasattr 检查一个唯一的属性，防止 __init__ 重复执行
        if hasattr(self, 'initialized'):
            return

        super().__init__()
        self.host = host
        self.port = port
        self.tcp_socket = QTcpSocket(self)  # 传入 self 作为 parent
        self.raw_buffer = bytearray()
        self.is_connecting = False
        self.init_socket()
        self.initialized = True  # 设置初始化标记
        self.tcp_socket.setSocketOption(QTcpSocket.SocketOption.LowDelayOption, 1)

        self.command_signal.connect(self.send_command)

    # ==================== 新增功能 ====================
    def reconfigure(self, new_host, new_port):
        """
        公开方法：用于重新配置IP和端口。
        这将断开现有连接，更新配置，然后发起新的连接。
        """
        if self.host == new_host and self.port == new_port:
            Logger.info(f"8080: IP地址 {new_host} 未改变，无需重新配置。")
            return

        Logger.info(f"8080: 正在重新配置IP，从 {self.host}:{self.port} -> {new_host}:{new_port}")

        # 1. 更新配置
        self.host = new_host
        self.port = new_port

        # 2. 断开现有连接（如果存在）
        # on_disconnected 信号会自动触发，将 is_connecting 设置为 False
        if self.tcp_socket.state() != QTcpSocket.SocketState.UnconnectedState:
            self.tcp_socket.abort()  # 使用 abort 立即断开

        # 3. 重新发起连接
        # 延迟一小段时间，确保 socket 状态已完全更新
        # QTimer.singleShot(100, self.connect_to_robot)
        # 或者直接调用，因为 on_disconnected 会重置状态
        self.connect_to_robot()

    # ================================================

    def init_socket(self):
        # self.tcp_socket.setParent(self) # 构造时已设置
        self.tcp_socket.connected.connect(self.on_connected)
        self.tcp_socket.disconnected.connect(self.on_disconnected)
        self.tcp_socket.readyRead.connect(self.on_ready_read)
        self.tcp_socket.errorOccurred.connect(self.on_error)

    def connect_to_robot(self):
        """尝试连接机器人，避免重复调用 connectToHost"""
        if self.tcp_socket.state() != QTcpSocket.SocketState.UnconnectedState:
            Logger.warning("8080: Socket 当前不处于未连接状态，取消本次连接请求。")
            return

        self.is_connecting = True
        Logger.info(f"8080: 尝试连接到机器人 -> {self.host}:{self.port}...")
        self.tcp_socket.connectToHost(self.host, self.port)

    @Slot()
    def on_connected(self):
        if self.tcp_socket.state() == QTcpSocket.SocketState.ConnectedState:
            Logger.info("8080已连接")
            self.connected.emit()
            self.is_connecting = True

    @Slot()
    def on_disconnected(self):
        Logger.info("8080连接断开，准备重连...")
        self.disconnected.emit()
        self.is_connecting = False

    @Slot(QTcpSocket.SocketError)
    def on_error(self, socket_error):
        Logger.error(f"8080连接错误信号: {self.tcp_socket.errorString()}")
        self.error_occurred.emit("8080连接错误")
        self.is_connecting = False

    # 发送指令方法
    def send_command(self, command):
        """按协议格式打包并发送指令"""
        if self.tcp_socket.state() != QTcpSocket.SocketState.ConnectedState:
            self.error_occurred.emit("请先连接机器人")
            return
        cmd = bytes(command, encoding='ascii')
        Logger.info(f"发送指令: {cmd}")
        bytes_written = self.tcp_socket.write(cmd)
        self.tcp_socket.flush()
        # print(f'发送数据字节数: {bytes_written}')

    @Slot()
    def on_ready_read(self):

        # 读取原始字节数据
        self.raw_buffer += self.tcp_socket.readAll().data()
        # Logger.info(f"收到原始数据: {self.raw_buffer}")

        # 消息边界标识
        start_marker = b'/f/b'
        end_marker = b'/b/f'
        start_idx = 0

        while True:
            # 查找消息起始位置
            start_pos = self.raw_buffer.find(start_marker, start_idx)
            if start_pos == -1:
                break

            # 查找消息结束位置
            end_pos = self.raw_buffer.find(end_marker, start_pos + len(start_marker))
            if end_pos == -1:
                break

            # 提取完整消息（包含结束标记）
            full_msg = self.raw_buffer[start_pos:end_pos + len(end_marker)]
            try:
                decoded_msg = full_msg.decode('utf-8', errors='replace')
                # print(f"信号发送数据: {decoded_msg}")
                self.response_received.emit(decoded_msg)
            except UnicodeDecodeError:
                print(f"解码失败: {full_msg}")

            # 移动处理指针
            start_idx = end_pos + len(end_marker)

        # 清理缓冲区
        self.raw_buffer = self.raw_buffer[start_idx:]

    def shutdown(self):
        if self.tcp_socket.state() == QTcpSocket.SocketState.ConnectedState:
            self.tcp_socket.disconnectFromHost()
        self.tcp_socket.deleteLater()