<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>480</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>IP设置</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDialog {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    border: 2px solid #dee2e6;
    border-radius: 15px;
}
QDialog::title {
    background: transparent;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>20</number>
   </property>
   <property name="leftMargin">
    <number>30</number>
   </property>
   <property name="topMargin">
    <number>25</number>
   </property>
   <property name="rightMargin">
    <number>30</number>
   </property>
   <property name="bottomMargin">
    <number>25</number>
   </property>
   <item>
    <widget class="QLabel" name="titleLabel">
     <property name="font">
      <font>
       <family>Microsoft YaHei UI</family>
       <pointsize>18</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel {
    color: #2c3e50;
    background: transparent;
    padding: 10px 0px;
    border-bottom: 2px solid #3498db;
    margin-bottom: 10px;
}</string>
     </property>
     <property name="text">
      <string>IP 地址设置</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="ipInputFrame">
     <property name="styleSheet">
      <string notr="true">QFrame {
    background: white;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    padding: 20px;
}
QFrame:hover {
    border-color: #3498db;
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <layout class="QVBoxLayout" name="ipFrameLayout">
      <property name="spacing">
       <number>15</number>
      </property>
      <item>
       <widget class="QLabel" name="ipLabel">
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
          <pointsize>12</pointsize>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel {
    color: #34495e;
    background: transparent;
    padding: 5px 0px;
}</string>
        </property>
        <property name="text">
         <string>机器人 IP 地址</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="process_electricInput_lineEdit">
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
          <pointsize>11</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QLineEdit {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #495057;
}
QLineEdit:focus {
    border-color: #3498db;
    background: white;
    outline: none;
}
QLineEdit:hover {
    border-color: #74b9ff;
}</string>
        </property>
        <property name="placeholderText">
         <string>请输入IP地址，例如：*************</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="ipHintLabel">
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
          <pointsize>9</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QLabel {
    color: #6c757d;
    background: transparent;
    padding: 5px 0px;
}</string>
        </property>
        <property name="text">
         <string>💡 提示：请确保输入正确的机器人IP地址</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <property name="spacing">
      <number>15</number>
     </property>
     <item>
      <spacer name="leftSpacer">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="process_weldingParameterApply_button">
       <property name="minimumSize">
        <size>
         <width>120</width>
         <height>45</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei UI</family>
         <pointsize>11</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #95a5a6, stop:1 #7f8c8d);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: bold;
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #a4b4b5, stop:1 #8e9b9c);
    transform: translateY(-1px);
}
QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #7f8c8d, stop:1 #6c7b7d);
    transform: translateY(1px);
}</string>
       </property>
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="process_weldingParameterApply_button_2">
       <property name="minimumSize">
        <size>
         <width>120</width>
         <height>45</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>Microsoft YaHei UI</family>
         <pointsize>11</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #27ae60, stop:1 #229954);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: bold;
}
QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #2ecc71, stop:1 #27ae60);
    transform: translateY(-1px);
}
QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #229954, stop:1 #1e8449);
    transform: translateY(1px);
}</string>
       </property>
       <property name="text">
        <string>保存</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="rightSpacer">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
