# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'io.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, Q<PERSON><PERSON>alGradient, Q<PERSON>ursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QFrame, QHBoxLayout,
    QLabel, QPushButton, QSizePolicy, QSpacerItem,
    QVBoxLayout, QWidget)
from ..resources.images_rc import *

class Ui_IOWidget(object):
    def setupUi(self, IOWidget):
        if not IOWidget.objectName():
            IOWidget.setObjectName(u"IOWidget")
        IOWidget.resize(830, 486)
        IOWidget.setStyleSheet(u"QWidget {\n"
"    background-color: #e8f0ff;\n"
"    font-family: \"Microsoft YaHei\", Arial, sans-serif;\n"
"}\n"
"\n"
"QFrame {\n"
"    background-color: white;\n"
"    border-radius: 12px;\n"
"    border: 1px solid #d0d7de;\n"
"}\n"
"\n"
"QPushButton {\n"
"    border: none;\n"
"    border-radius: 8px;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"    color: white;\n"
"}\n"
"\n"
"QLabel {\n"
"    color: #2d3748;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
"QComboBox {\n"
"    background-color: #f1f5f9;\n"
"    border: 1px solid #cbd5e0;\n"
"    border-radius: 6px;\n"
"    padding: 6px 10px;\n"
"    font-size: 11px;\n"
"    color: #374151;\n"
"    min-height: 24px;\n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"    width: 20px;\n"
"}\n"
"\n"
"QComboBox::down-arrow {\n"
"    width: 12px;\n"
"    height: 12px;\n"
"}\n"
"\n"
".status-indicator-active {\n"
"    background-color: #22c55e;\n"
"    border-radius: 12px;\n"
"    min-width: 24px;\n"
"    min-height: 24px;\n"
"    max-width: 24px;\n"
""
                        "    max-height: 24px;\n"
"}\n"
"\n"
".status-indicator-inactive {\n"
"    background-color: #9ca3af;\n"
"    border-radius: 12px;\n"
"    min-width: 24px;\n"
"    min-height: 24px;\n"
"    max-width: 24px;\n"
"    max-height: 24px;\n"
"}\n"
"\n"
".save-button {\n"
"    background-color: #22c55e;\n"
"    color: white;\n"
"    min-height: 40px;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"    border-radius: 8px;\n"
"}")
        self.mainHorizontalLayout = QHBoxLayout(IOWidget)
        self.mainHorizontalLayout.setSpacing(16)
        self.mainHorizontalLayout.setObjectName(u"mainHorizontalLayout")
        self.mainHorizontalLayout.setContentsMargins(16, 16, 16, 16)
        self.diFrame = QFrame(IOWidget)
        self.diFrame.setObjectName(u"diFrame")
        self.diFrame.setMinimumSize(QSize(390, 450))
        self.diFrame.setMaximumSize(QSize(390, 450))
        self.diMainLayout = QVBoxLayout(self.diFrame)
        self.diMainLayout.setSpacing(16)
        self.diMainLayout.setObjectName(u"diMainLayout")
        self.diMainLayout.setContentsMargins(16, 16, 16, 16)
        self.diTitleLabel = QLabel(self.diFrame)
        self.diTitleLabel.setObjectName(u"diTitleLabel")
        self.diTitleLabel.setStyleSheet(u"font-size: 20px; font-weight: bold; color: #1f2937;")
        self.diTitleLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.diMainLayout.addWidget(self.diTitleLabel)

        self.diItemsLayout = QVBoxLayout()
        self.diItemsLayout.setSpacing(16)
        self.diItemsLayout.setObjectName(u"diItemsLayout")
        self.diRow1Layout = QHBoxLayout()
        self.diRow1Layout.setSpacing(20)
        self.diRow1Layout.setObjectName(u"diRow1Layout")
        self.diStartLayout = QVBoxLayout()
        self.diStartLayout.setSpacing(6)
        self.diStartLayout.setObjectName(u"diStartLayout")
        self.diStartControlLayout = QHBoxLayout()
        self.diStartControlLayout.setObjectName(u"diStartControlLayout")
        self.diStartLabel = QLabel(self.diFrame)
        self.diStartLabel.setObjectName(u"diStartLabel")
        self.diStartLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.diStartControlLayout.addWidget(self.diStartLabel)

        self.diStartSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.diStartControlLayout.addItem(self.diStartSpacer)

        self.diStartStatusIndicator = QPushButton(self.diFrame)
        self.diStartStatusIndicator.setObjectName(u"diStartStatusIndicator")
        self.diStartStatusIndicator.setStyleSheet(u"background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.diStartControlLayout.addWidget(self.diStartStatusIndicator)


        self.diStartLayout.addLayout(self.diStartControlLayout)

        self.diStartComboBox = QComboBox(self.diFrame)
        self.diStartComboBox.addItem("")
        self.diStartComboBox.addItem("")
        self.diStartComboBox.addItem("")
        self.diStartComboBox.addItem("")
        self.diStartComboBox.addItem("")
        self.diStartComboBox.addItem("")
        self.diStartComboBox.addItem("")
        self.diStartComboBox.addItem("")
        self.diStartComboBox.addItem("")
        self.diStartComboBox.setObjectName(u"diStartComboBox")
        self.diStartComboBox.setMinimumSize(QSize(160, 38))

        self.diStartLayout.addWidget(self.diStartComboBox)


        self.diRow1Layout.addLayout(self.diStartLayout)

        self.diPauseLayout = QVBoxLayout()
        self.diPauseLayout.setSpacing(6)
        self.diPauseLayout.setObjectName(u"diPauseLayout")
        self.diPauseControlLayout = QHBoxLayout()
        self.diPauseControlLayout.setObjectName(u"diPauseControlLayout")
        self.diPauseLabel = QLabel(self.diFrame)
        self.diPauseLabel.setObjectName(u"diPauseLabel")
        self.diPauseLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.diPauseControlLayout.addWidget(self.diPauseLabel)

        self.diPauseSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.diPauseControlLayout.addItem(self.diPauseSpacer)

        self.diPauseStatusIndicator = QPushButton(self.diFrame)
        self.diPauseStatusIndicator.setObjectName(u"diPauseStatusIndicator")
        self.diPauseStatusIndicator.setStyleSheet(u"background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.diPauseControlLayout.addWidget(self.diPauseStatusIndicator)


        self.diPauseLayout.addLayout(self.diPauseControlLayout)

        self.diPauseComboBox = QComboBox(self.diFrame)
        self.diPauseComboBox.addItem("")
        self.diPauseComboBox.addItem("")
        self.diPauseComboBox.addItem("")
        self.diPauseComboBox.addItem("")
        self.diPauseComboBox.addItem("")
        self.diPauseComboBox.addItem("")
        self.diPauseComboBox.addItem("")
        self.diPauseComboBox.addItem("")
        self.diPauseComboBox.addItem("")
        self.diPauseComboBox.setObjectName(u"diPauseComboBox")
        self.diPauseComboBox.setMinimumSize(QSize(160, 38))

        self.diPauseLayout.addWidget(self.diPauseComboBox)


        self.diRow1Layout.addLayout(self.diPauseLayout)


        self.diItemsLayout.addLayout(self.diRow1Layout)

        self.diRow2Layout = QHBoxLayout()
        self.diRow2Layout.setSpacing(20)
        self.diRow2Layout.setObjectName(u"diRow2Layout")
        self.diStopLayout = QVBoxLayout()
        self.diStopLayout.setSpacing(6)
        self.diStopLayout.setObjectName(u"diStopLayout")
        self.diStopControlLayout = QHBoxLayout()
        self.diStopControlLayout.setObjectName(u"diStopControlLayout")
        self.diStopLabel = QLabel(self.diFrame)
        self.diStopLabel.setObjectName(u"diStopLabel")
        self.diStopLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.diStopControlLayout.addWidget(self.diStopLabel)

        self.diStopSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.diStopControlLayout.addItem(self.diStopSpacer)

        self.diStopStatusIndicator = QPushButton(self.diFrame)
        self.diStopStatusIndicator.setObjectName(u"diStopStatusIndicator")
        self.diStopStatusIndicator.setStyleSheet(u"background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.diStopControlLayout.addWidget(self.diStopStatusIndicator)


        self.diStopLayout.addLayout(self.diStopControlLayout)

        self.diStopComboBox = QComboBox(self.diFrame)
        self.diStopComboBox.addItem("")
        self.diStopComboBox.addItem("")
        self.diStopComboBox.addItem("")
        self.diStopComboBox.addItem("")
        self.diStopComboBox.addItem("")
        self.diStopComboBox.addItem("")
        self.diStopComboBox.addItem("")
        self.diStopComboBox.addItem("")
        self.diStopComboBox.addItem("")
        self.diStopComboBox.setObjectName(u"diStopComboBox")
        self.diStopComboBox.setMinimumSize(QSize(160, 38))

        self.diStopLayout.addWidget(self.diStopComboBox)


        self.diRow2Layout.addLayout(self.diStopLayout)

        self.diGrabSignalLayout = QVBoxLayout()
        self.diGrabSignalLayout.setSpacing(6)
        self.diGrabSignalLayout.setObjectName(u"diGrabSignalLayout")
        self.diGrabSignalControlLayout = QHBoxLayout()
        self.diGrabSignalControlLayout.setObjectName(u"diGrabSignalControlLayout")
        self.diGrabSignalLabel = QLabel(self.diFrame)
        self.diGrabSignalLabel.setObjectName(u"diGrabSignalLabel")
        self.diGrabSignalLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.diGrabSignalControlLayout.addWidget(self.diGrabSignalLabel)

        self.diGrabSignalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.diGrabSignalControlLayout.addItem(self.diGrabSignalSpacer)

        self.diGrabSignalStatusIndicator = QPushButton(self.diFrame)
        self.diGrabSignalStatusIndicator.setObjectName(u"diGrabSignalStatusIndicator")
        self.diGrabSignalStatusIndicator.setStyleSheet(u"background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.diGrabSignalControlLayout.addWidget(self.diGrabSignalStatusIndicator)


        self.diGrabSignalLayout.addLayout(self.diGrabSignalControlLayout)

        self.diGrabSignalComboBox = QComboBox(self.diFrame)
        self.diGrabSignalComboBox.addItem("")
        self.diGrabSignalComboBox.addItem("")
        self.diGrabSignalComboBox.addItem("")
        self.diGrabSignalComboBox.addItem("")
        self.diGrabSignalComboBox.addItem("")
        self.diGrabSignalComboBox.addItem("")
        self.diGrabSignalComboBox.addItem("")
        self.diGrabSignalComboBox.addItem("")
        self.diGrabSignalComboBox.addItem("")
        self.diGrabSignalComboBox.setObjectName(u"diGrabSignalComboBox")
        self.diGrabSignalComboBox.setMinimumSize(QSize(160, 38))

        self.diGrabSignalLayout.addWidget(self.diGrabSignalComboBox)


        self.diRow2Layout.addLayout(self.diGrabSignalLayout)


        self.diItemsLayout.addLayout(self.diRow2Layout)

        self.diRow3Layout = QHBoxLayout()
        self.diRow3Layout.setSpacing(20)
        self.diRow3Layout.setObjectName(u"diRow3Layout")
        self.diPressureDetectLayout = QVBoxLayout()
        self.diPressureDetectLayout.setSpacing(6)
        self.diPressureDetectLayout.setObjectName(u"diPressureDetectLayout")
        self.diPressureDetectControlLayout = QHBoxLayout()
        self.diPressureDetectControlLayout.setObjectName(u"diPressureDetectControlLayout")
        self.diPressureDetectLabel = QLabel(self.diFrame)
        self.diPressureDetectLabel.setObjectName(u"diPressureDetectLabel")
        self.diPressureDetectLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.diPressureDetectControlLayout.addWidget(self.diPressureDetectLabel)

        self.diPressureDetectSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.diPressureDetectControlLayout.addItem(self.diPressureDetectSpacer)

        self.diPressureDetectStatusIndicator = QPushButton(self.diFrame)
        self.diPressureDetectStatusIndicator.setObjectName(u"diPressureDetectStatusIndicator")
        self.diPressureDetectStatusIndicator.setStyleSheet(u"background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.diPressureDetectControlLayout.addWidget(self.diPressureDetectStatusIndicator)


        self.diPressureDetectLayout.addLayout(self.diPressureDetectControlLayout)

        self.diPressureDetectComboBox = QComboBox(self.diFrame)
        self.diPressureDetectComboBox.addItem("")
        self.diPressureDetectComboBox.addItem("")
        self.diPressureDetectComboBox.addItem("")
        self.diPressureDetectComboBox.addItem("")
        self.diPressureDetectComboBox.addItem("")
        self.diPressureDetectComboBox.addItem("")
        self.diPressureDetectComboBox.addItem("")
        self.diPressureDetectComboBox.addItem("")
        self.diPressureDetectComboBox.addItem("")
        self.diPressureDetectComboBox.setObjectName(u"diPressureDetectComboBox")
        self.diPressureDetectComboBox.setMinimumSize(QSize(160, 38))

        self.diPressureDetectLayout.addWidget(self.diPressureDetectComboBox)


        self.diRow3Layout.addLayout(self.diPressureDetectLayout)

        self.diVacuumDetectLayout = QVBoxLayout()
        self.diVacuumDetectLayout.setSpacing(6)
        self.diVacuumDetectLayout.setObjectName(u"diVacuumDetectLayout")
        self.diVacuumDetectControlLayout = QHBoxLayout()
        self.diVacuumDetectControlLayout.setObjectName(u"diVacuumDetectControlLayout")
        self.diVacuumDetectLabel = QLabel(self.diFrame)
        self.diVacuumDetectLabel.setObjectName(u"diVacuumDetectLabel")
        self.diVacuumDetectLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.diVacuumDetectControlLayout.addWidget(self.diVacuumDetectLabel)

        self.diVacuumDetectSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.diVacuumDetectControlLayout.addItem(self.diVacuumDetectSpacer)

        self.diVacuumDetectStatusIndicator = QPushButton(self.diFrame)
        self.diVacuumDetectStatusIndicator.setObjectName(u"diVacuumDetectStatusIndicator")
        self.diVacuumDetectStatusIndicator.setStyleSheet(u"background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.diVacuumDetectControlLayout.addWidget(self.diVacuumDetectStatusIndicator)


        self.diVacuumDetectLayout.addLayout(self.diVacuumDetectControlLayout)

        self.diVacuumDetectComboBox = QComboBox(self.diFrame)
        self.diVacuumDetectComboBox.addItem("")
        self.diVacuumDetectComboBox.addItem("")
        self.diVacuumDetectComboBox.addItem("")
        self.diVacuumDetectComboBox.addItem("")
        self.diVacuumDetectComboBox.addItem("")
        self.diVacuumDetectComboBox.addItem("")
        self.diVacuumDetectComboBox.addItem("")
        self.diVacuumDetectComboBox.addItem("")
        self.diVacuumDetectComboBox.addItem("")
        self.diVacuumDetectComboBox.setObjectName(u"diVacuumDetectComboBox")
        self.diVacuumDetectComboBox.setMinimumSize(QSize(160, 38))

        self.diVacuumDetectLayout.addWidget(self.diVacuumDetectComboBox)


        self.diRow3Layout.addLayout(self.diVacuumDetectLayout)


        self.diItemsLayout.addLayout(self.diRow3Layout)

        self.diRow4Layout = QHBoxLayout()
        self.diRow4Layout.setSpacing(20)
        self.diRow4Layout.setObjectName(u"diRow4Layout")
        self.diLeftTrayDetectLayout = QVBoxLayout()
        self.diLeftTrayDetectLayout.setSpacing(6)
        self.diLeftTrayDetectLayout.setObjectName(u"diLeftTrayDetectLayout")
        self.diLeftTrayDetectControlLayout = QHBoxLayout()
        self.diLeftTrayDetectControlLayout.setObjectName(u"diLeftTrayDetectControlLayout")
        self.diLeftTrayDetectLabel = QLabel(self.diFrame)
        self.diLeftTrayDetectLabel.setObjectName(u"diLeftTrayDetectLabel")
        self.diLeftTrayDetectLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.diLeftTrayDetectControlLayout.addWidget(self.diLeftTrayDetectLabel)

        self.diLeftTrayDetectSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.diLeftTrayDetectControlLayout.addItem(self.diLeftTrayDetectSpacer)

        self.diLeftTrayDetectStatusIndicator = QPushButton(self.diFrame)
        self.diLeftTrayDetectStatusIndicator.setObjectName(u"diLeftTrayDetectStatusIndicator")
        self.diLeftTrayDetectStatusIndicator.setStyleSheet(u"background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.diLeftTrayDetectControlLayout.addWidget(self.diLeftTrayDetectStatusIndicator)


        self.diLeftTrayDetectLayout.addLayout(self.diLeftTrayDetectControlLayout)

        self.diLeftTrayDetectComboBox = QComboBox(self.diFrame)
        self.diLeftTrayDetectComboBox.addItem("")
        self.diLeftTrayDetectComboBox.addItem("")
        self.diLeftTrayDetectComboBox.addItem("")
        self.diLeftTrayDetectComboBox.addItem("")
        self.diLeftTrayDetectComboBox.addItem("")
        self.diLeftTrayDetectComboBox.addItem("")
        self.diLeftTrayDetectComboBox.addItem("")
        self.diLeftTrayDetectComboBox.addItem("")
        self.diLeftTrayDetectComboBox.addItem("")
        self.diLeftTrayDetectComboBox.setObjectName(u"diLeftTrayDetectComboBox")
        self.diLeftTrayDetectComboBox.setMinimumSize(QSize(160, 38))

        self.diLeftTrayDetectLayout.addWidget(self.diLeftTrayDetectComboBox)


        self.diRow4Layout.addLayout(self.diLeftTrayDetectLayout)

        self.diRightTrayDetectLayout = QVBoxLayout()
        self.diRightTrayDetectLayout.setSpacing(6)
        self.diRightTrayDetectLayout.setObjectName(u"diRightTrayDetectLayout")
        self.diRightTrayDetectControlLayout = QHBoxLayout()
        self.diRightTrayDetectControlLayout.setObjectName(u"diRightTrayDetectControlLayout")
        self.diRightTrayDetectLabel = QLabel(self.diFrame)
        self.diRightTrayDetectLabel.setObjectName(u"diRightTrayDetectLabel")
        self.diRightTrayDetectLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.diRightTrayDetectControlLayout.addWidget(self.diRightTrayDetectLabel)

        self.diRightTrayDetectSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.diRightTrayDetectControlLayout.addItem(self.diRightTrayDetectSpacer)

        self.diRightTrayDetectStatusIndicator = QPushButton(self.diFrame)
        self.diRightTrayDetectStatusIndicator.setObjectName(u"diRightTrayDetectStatusIndicator")
        self.diRightTrayDetectStatusIndicator.setStyleSheet(u"background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.diRightTrayDetectControlLayout.addWidget(self.diRightTrayDetectStatusIndicator)


        self.diRightTrayDetectLayout.addLayout(self.diRightTrayDetectControlLayout)

        self.diRightTrayDetectComboBox = QComboBox(self.diFrame)
        self.diRightTrayDetectComboBox.addItem("")
        self.diRightTrayDetectComboBox.addItem("")
        self.diRightTrayDetectComboBox.addItem("")
        self.diRightTrayDetectComboBox.addItem("")
        self.diRightTrayDetectComboBox.addItem("")
        self.diRightTrayDetectComboBox.addItem("")
        self.diRightTrayDetectComboBox.addItem("")
        self.diRightTrayDetectComboBox.addItem("")
        self.diRightTrayDetectComboBox.addItem("")
        self.diRightTrayDetectComboBox.setObjectName(u"diRightTrayDetectComboBox")
        self.diRightTrayDetectComboBox.setMinimumSize(QSize(160, 38))

        self.diRightTrayDetectLayout.addWidget(self.diRightTrayDetectComboBox)


        self.diRow4Layout.addLayout(self.diRightTrayDetectLayout)


        self.diItemsLayout.addLayout(self.diRow4Layout)


        self.diMainLayout.addLayout(self.diItemsLayout)

        self.diVerticalSpacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.diMainLayout.addItem(self.diVerticalSpacer)

        self.diSaveButton = QPushButton(self.diFrame)
        self.diSaveButton.setObjectName(u"diSaveButton")
        self.diSaveButton.setStyleSheet(u"background-color: #22c55e; color: white; min-height: 40px; font-size: 14px; font-weight: bold; border-radius: 8px;\n"
"background:url(:/io/io/IO\u4fdd\u5b58.png);\n"
"background-repeat: no-repeat;\n"
"background-position: center;\n"
"background-attachment: fixed;")

        self.diMainLayout.addWidget(self.diSaveButton)


        self.mainHorizontalLayout.addWidget(self.diFrame)

        self.doFrame = QFrame(IOWidget)
        self.doFrame.setObjectName(u"doFrame")
        self.doFrame.setMinimumSize(QSize(390, 450))
        self.doFrame.setMaximumSize(QSize(390, 450))
        self.doMainLayout = QVBoxLayout(self.doFrame)
        self.doMainLayout.setSpacing(16)
        self.doMainLayout.setObjectName(u"doMainLayout")
        self.doMainLayout.setContentsMargins(16, 16, 16, 16)
        self.doTitleLabel = QLabel(self.doFrame)
        self.doTitleLabel.setObjectName(u"doTitleLabel")
        self.doTitleLabel.setStyleSheet(u"font-size: 20px; font-weight: bold; color: #1f2937;")
        self.doTitleLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.doMainLayout.addWidget(self.doTitleLabel)

        self.doItemsLayout = QVBoxLayout()
        self.doItemsLayout.setSpacing(17)
        self.doItemsLayout.setObjectName(u"doItemsLayout")
        self.doRow1Layout = QHBoxLayout()
        self.doRow1Layout.setSpacing(20)
        self.doRow1Layout.setObjectName(u"doRow1Layout")
        self.doStartLightLayout = QVBoxLayout()
        self.doStartLightLayout.setSpacing(6)
        self.doStartLightLayout.setObjectName(u"doStartLightLayout")
        self.doStartLightControlLayout = QHBoxLayout()
        self.doStartLightControlLayout.setObjectName(u"doStartLightControlLayout")
        self.doStartLightLabel = QLabel(self.doFrame)
        self.doStartLightLabel.setObjectName(u"doStartLightLabel")
        self.doStartLightLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.doStartLightControlLayout.addWidget(self.doStartLightLabel)

        self.doStartLightSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doStartLightControlLayout.addItem(self.doStartLightSpacer)

        self.doStartLightStatusIndicator = QPushButton(self.doFrame)
        self.doStartLightStatusIndicator.setObjectName(u"doStartLightStatusIndicator")
        self.doStartLightStatusIndicator.setStyleSheet(u"background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.doStartLightControlLayout.addWidget(self.doStartLightStatusIndicator)


        self.doStartLightLayout.addLayout(self.doStartLightControlLayout)

        self.doStartLightComboBox = QComboBox(self.doFrame)
        self.doStartLightComboBox.addItem("")
        self.doStartLightComboBox.addItem("")
        self.doStartLightComboBox.addItem("")
        self.doStartLightComboBox.addItem("")
        self.doStartLightComboBox.addItem("")
        self.doStartLightComboBox.addItem("")
        self.doStartLightComboBox.addItem("")
        self.doStartLightComboBox.addItem("")
        self.doStartLightComboBox.addItem("")
        self.doStartLightComboBox.setObjectName(u"doStartLightComboBox")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doStartLightComboBox.sizePolicy().hasHeightForWidth())
        self.doStartLightComboBox.setSizePolicy(sizePolicy)
        self.doStartLightComboBox.setMinimumSize(QSize(160, 38))
        self.doStartLightComboBox.setMaximumSize(QSize(16777215, 16777215))

        self.doStartLightLayout.addWidget(self.doStartLightComboBox)


        self.doRow1Layout.addLayout(self.doStartLightLayout)

        self.doPauseLightLayout = QVBoxLayout()
        self.doPauseLightLayout.setSpacing(6)
        self.doPauseLightLayout.setObjectName(u"doPauseLightLayout")
        self.doPauseLightControlLayout = QHBoxLayout()
        self.doPauseLightControlLayout.setObjectName(u"doPauseLightControlLayout")
        self.doPauseLightLabel = QLabel(self.doFrame)
        self.doPauseLightLabel.setObjectName(u"doPauseLightLabel")
        self.doPauseLightLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.doPauseLightControlLayout.addWidget(self.doPauseLightLabel)

        self.doPauseLightSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doPauseLightControlLayout.addItem(self.doPauseLightSpacer)

        self.doPauseLightStatusIndicator = QPushButton(self.doFrame)
        self.doPauseLightStatusIndicator.setObjectName(u"doPauseLightStatusIndicator")
        self.doPauseLightStatusIndicator.setStyleSheet(u"background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.doPauseLightControlLayout.addWidget(self.doPauseLightStatusIndicator)


        self.doPauseLightLayout.addLayout(self.doPauseLightControlLayout)

        self.doPauseLightComboBox = QComboBox(self.doFrame)
        self.doPauseLightComboBox.addItem("")
        self.doPauseLightComboBox.addItem("")
        self.doPauseLightComboBox.addItem("")
        self.doPauseLightComboBox.addItem("")
        self.doPauseLightComboBox.addItem("")
        self.doPauseLightComboBox.addItem("")
        self.doPauseLightComboBox.addItem("")
        self.doPauseLightComboBox.addItem("")
        self.doPauseLightComboBox.addItem("")
        self.doPauseLightComboBox.setObjectName(u"doPauseLightComboBox")
        sizePolicy.setHeightForWidth(self.doPauseLightComboBox.sizePolicy().hasHeightForWidth())
        self.doPauseLightComboBox.setSizePolicy(sizePolicy)
        self.doPauseLightComboBox.setMinimumSize(QSize(160, 38))
        self.doPauseLightComboBox.setMaximumSize(QSize(16777215, 16777215))

        self.doPauseLightLayout.addWidget(self.doPauseLightComboBox)


        self.doRow1Layout.addLayout(self.doPauseLightLayout)


        self.doItemsLayout.addLayout(self.doRow1Layout)

        self.doRow2Layout = QHBoxLayout()
        self.doRow2Layout.setSpacing(20)
        self.doRow2Layout.setObjectName(u"doRow2Layout")
        self.doStopLightLayout = QVBoxLayout()
        self.doStopLightLayout.setSpacing(6)
        self.doStopLightLayout.setObjectName(u"doStopLightLayout")
        self.doStopLightControlLayout = QHBoxLayout()
        self.doStopLightControlLayout.setObjectName(u"doStopLightControlLayout")
        self.doStopLightLabel = QLabel(self.doFrame)
        self.doStopLightLabel.setObjectName(u"doStopLightLabel")
        self.doStopLightLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.doStopLightControlLayout.addWidget(self.doStopLightLabel)

        self.doStopLightSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doStopLightControlLayout.addItem(self.doStopLightSpacer)

        self.doStopLightStatusIndicator = QPushButton(self.doFrame)
        self.doStopLightStatusIndicator.setObjectName(u"doStopLightStatusIndicator")
        self.doStopLightStatusIndicator.setStyleSheet(u"background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.doStopLightControlLayout.addWidget(self.doStopLightStatusIndicator)


        self.doStopLightLayout.addLayout(self.doStopLightControlLayout)

        self.doStopLightComboBox = QComboBox(self.doFrame)
        self.doStopLightComboBox.addItem("")
        self.doStopLightComboBox.addItem("")
        self.doStopLightComboBox.addItem("")
        self.doStopLightComboBox.addItem("")
        self.doStopLightComboBox.addItem("")
        self.doStopLightComboBox.addItem("")
        self.doStopLightComboBox.addItem("")
        self.doStopLightComboBox.addItem("")
        self.doStopLightComboBox.addItem("")
        self.doStopLightComboBox.setObjectName(u"doStopLightComboBox")
        self.doStopLightComboBox.setMinimumSize(QSize(160, 38))

        self.doStopLightLayout.addWidget(self.doStopLightComboBox)


        self.doRow2Layout.addLayout(self.doStopLightLayout)

        self.doGrabLayout = QVBoxLayout()
        self.doGrabLayout.setSpacing(6)
        self.doGrabLayout.setObjectName(u"doGrabLayout")
        self.doGrabControlLayout = QHBoxLayout()
        self.doGrabControlLayout.setObjectName(u"doGrabControlLayout")
        self.doGrabLabel = QLabel(self.doFrame)
        self.doGrabLabel.setObjectName(u"doGrabLabel")
        self.doGrabLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.doGrabControlLayout.addWidget(self.doGrabLabel)

        self.doGrabSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doGrabControlLayout.addItem(self.doGrabSpacer)

        self.doGrabStatusIndicator = QPushButton(self.doFrame)
        self.doGrabStatusIndicator.setObjectName(u"doGrabStatusIndicator")
        self.doGrabStatusIndicator.setStyleSheet(u"background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.doGrabControlLayout.addWidget(self.doGrabStatusIndicator)


        self.doGrabLayout.addLayout(self.doGrabControlLayout)

        self.doGrabComboBox = QComboBox(self.doFrame)
        self.doGrabComboBox.addItem("")
        self.doGrabComboBox.addItem("")
        self.doGrabComboBox.addItem("")
        self.doGrabComboBox.addItem("")
        self.doGrabComboBox.addItem("")
        self.doGrabComboBox.addItem("")
        self.doGrabComboBox.addItem("")
        self.doGrabComboBox.addItem("")
        self.doGrabComboBox.addItem("")
        self.doGrabComboBox.setObjectName(u"doGrabComboBox")
        self.doGrabComboBox.setMinimumSize(QSize(160, 38))

        self.doGrabLayout.addWidget(self.doGrabComboBox)


        self.doRow2Layout.addLayout(self.doGrabLayout)


        self.doItemsLayout.addLayout(self.doRow2Layout)

        self.doRow3Layout = QHBoxLayout()
        self.doRow3Layout.setSpacing(20)
        self.doRow3Layout.setObjectName(u"doRow3Layout")
        self.doReleaseLayout = QVBoxLayout()
        self.doReleaseLayout.setSpacing(6)
        self.doReleaseLayout.setObjectName(u"doReleaseLayout")
        self.doReleaseControlLayout = QHBoxLayout()
        self.doReleaseControlLayout.setObjectName(u"doReleaseControlLayout")
        self.doReleaseLabel = QLabel(self.doFrame)
        self.doReleaseLabel.setObjectName(u"doReleaseLabel")
        self.doReleaseLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.doReleaseControlLayout.addWidget(self.doReleaseLabel)

        self.doReleaseSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doReleaseControlLayout.addItem(self.doReleaseSpacer)

        self.doReleaseStatusIndicator = QPushButton(self.doFrame)
        self.doReleaseStatusIndicator.setObjectName(u"doReleaseStatusIndicator")
        self.doReleaseStatusIndicator.setStyleSheet(u"background-color: #22c55e; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.doReleaseControlLayout.addWidget(self.doReleaseStatusIndicator)


        self.doReleaseLayout.addLayout(self.doReleaseControlLayout)

        self.doReleaseComboBox = QComboBox(self.doFrame)
        self.doReleaseComboBox.addItem("")
        self.doReleaseComboBox.addItem("")
        self.doReleaseComboBox.addItem("")
        self.doReleaseComboBox.addItem("")
        self.doReleaseComboBox.addItem("")
        self.doReleaseComboBox.addItem("")
        self.doReleaseComboBox.addItem("")
        self.doReleaseComboBox.addItem("")
        self.doReleaseComboBox.addItem("")
        self.doReleaseComboBox.setObjectName(u"doReleaseComboBox")
        self.doReleaseComboBox.setMinimumSize(QSize(160, 38))

        self.doReleaseLayout.addWidget(self.doReleaseComboBox)


        self.doRow3Layout.addLayout(self.doReleaseLayout)

        self.doLeftRunningLayout = QVBoxLayout()
        self.doLeftRunningLayout.setSpacing(6)
        self.doLeftRunningLayout.setObjectName(u"doLeftRunningLayout")
        self.doLeftRunningControlLayout = QHBoxLayout()
        self.doLeftRunningControlLayout.setObjectName(u"doLeftRunningControlLayout")
        self.doLeftRunningLabel = QLabel(self.doFrame)
        self.doLeftRunningLabel.setObjectName(u"doLeftRunningLabel")
        self.doLeftRunningLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.doLeftRunningControlLayout.addWidget(self.doLeftRunningLabel)

        self.doLeftRunningSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doLeftRunningControlLayout.addItem(self.doLeftRunningSpacer)

        self.doLeftRunningStatusIndicator = QPushButton(self.doFrame)
        self.doLeftRunningStatusIndicator.setObjectName(u"doLeftRunningStatusIndicator")
        self.doLeftRunningStatusIndicator.setStyleSheet(u"background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.doLeftRunningControlLayout.addWidget(self.doLeftRunningStatusIndicator)


        self.doLeftRunningLayout.addLayout(self.doLeftRunningControlLayout)

        self.doLeftRunningComboBox = QComboBox(self.doFrame)
        self.doLeftRunningComboBox.addItem("")
        self.doLeftRunningComboBox.addItem("")
        self.doLeftRunningComboBox.addItem("")
        self.doLeftRunningComboBox.addItem("")
        self.doLeftRunningComboBox.addItem("")
        self.doLeftRunningComboBox.addItem("")
        self.doLeftRunningComboBox.addItem("")
        self.doLeftRunningComboBox.addItem("")
        self.doLeftRunningComboBox.addItem("")
        self.doLeftRunningComboBox.setObjectName(u"doLeftRunningComboBox")
        self.doLeftRunningComboBox.setMinimumSize(QSize(160, 38))

        self.doLeftRunningLayout.addWidget(self.doLeftRunningComboBox)


        self.doRow3Layout.addLayout(self.doLeftRunningLayout)


        self.doItemsLayout.addLayout(self.doRow3Layout)

        self.doRow4Layout = QHBoxLayout()
        self.doRow4Layout.setSpacing(20)
        self.doRow4Layout.setObjectName(u"doRow4Layout")
        self.doLeftTrayCleanLayout = QVBoxLayout()
        self.doLeftTrayCleanLayout.setSpacing(6)
        self.doLeftTrayCleanLayout.setObjectName(u"doLeftTrayCleanLayout")
        self.doLeftTrayCleanControlLayout = QHBoxLayout()
        self.doLeftTrayCleanControlLayout.setObjectName(u"doLeftTrayCleanControlLayout")
        self.doLeftTrayCleanLabel = QLabel(self.doFrame)
        self.doLeftTrayCleanLabel.setObjectName(u"doLeftTrayCleanLabel")
        self.doLeftTrayCleanLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.doLeftTrayCleanControlLayout.addWidget(self.doLeftTrayCleanLabel)

        self.doLeftTrayCleanSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doLeftTrayCleanControlLayout.addItem(self.doLeftTrayCleanSpacer)

        self.doLeftTrayCleanStatusIndicator = QPushButton(self.doFrame)
        self.doLeftTrayCleanStatusIndicator.setObjectName(u"doLeftTrayCleanStatusIndicator")
        self.doLeftTrayCleanStatusIndicator.setStyleSheet(u"background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.doLeftTrayCleanControlLayout.addWidget(self.doLeftTrayCleanStatusIndicator)


        self.doLeftTrayCleanLayout.addLayout(self.doLeftTrayCleanControlLayout)

        self.doLeftTrayCleanComboBox = QComboBox(self.doFrame)
        self.doLeftTrayCleanComboBox.addItem("")
        self.doLeftTrayCleanComboBox.addItem("")
        self.doLeftTrayCleanComboBox.addItem("")
        self.doLeftTrayCleanComboBox.addItem("")
        self.doLeftTrayCleanComboBox.addItem("")
        self.doLeftTrayCleanComboBox.addItem("")
        self.doLeftTrayCleanComboBox.addItem("")
        self.doLeftTrayCleanComboBox.addItem("")
        self.doLeftTrayCleanComboBox.addItem("")
        self.doLeftTrayCleanComboBox.setObjectName(u"doLeftTrayCleanComboBox")
        self.doLeftTrayCleanComboBox.setMinimumSize(QSize(160, 38))

        self.doLeftTrayCleanLayout.addWidget(self.doLeftTrayCleanComboBox)


        self.doRow4Layout.addLayout(self.doLeftTrayCleanLayout)

        self.doRightRunningLayout = QVBoxLayout()
        self.doRightRunningLayout.setSpacing(6)
        self.doRightRunningLayout.setObjectName(u"doRightRunningLayout")
        self.doRightRunningControlLayout = QHBoxLayout()
        self.doRightRunningControlLayout.setObjectName(u"doRightRunningControlLayout")
        self.doRightRunningLabel = QLabel(self.doFrame)
        self.doRightRunningLabel.setObjectName(u"doRightRunningLabel")
        self.doRightRunningLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.doRightRunningControlLayout.addWidget(self.doRightRunningLabel)

        self.doRightRunningSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doRightRunningControlLayout.addItem(self.doRightRunningSpacer)

        self.doRightRunningStatusIndicator = QPushButton(self.doFrame)
        self.doRightRunningStatusIndicator.setObjectName(u"doRightRunningStatusIndicator")
        self.doRightRunningStatusIndicator.setStyleSheet(u"background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.doRightRunningControlLayout.addWidget(self.doRightRunningStatusIndicator)


        self.doRightRunningLayout.addLayout(self.doRightRunningControlLayout)

        self.doRightRunningComboBox = QComboBox(self.doFrame)
        self.doRightRunningComboBox.addItem("")
        self.doRightRunningComboBox.addItem("")
        self.doRightRunningComboBox.addItem("")
        self.doRightRunningComboBox.addItem("")
        self.doRightRunningComboBox.addItem("")
        self.doRightRunningComboBox.addItem("")
        self.doRightRunningComboBox.addItem("")
        self.doRightRunningComboBox.addItem("")
        self.doRightRunningComboBox.addItem("")
        self.doRightRunningComboBox.setObjectName(u"doRightRunningComboBox")
        self.doRightRunningComboBox.setMinimumSize(QSize(160, 38))

        self.doRightRunningLayout.addWidget(self.doRightRunningComboBox)


        self.doRow4Layout.addLayout(self.doRightRunningLayout)


        self.doItemsLayout.addLayout(self.doRow4Layout)

        self.doRow5Layout = QHBoxLayout()
        self.doRow5Layout.setSpacing(20)
        self.doRow5Layout.setObjectName(u"doRow5Layout")
        self.doRightTrayCleanLayout = QVBoxLayout()
        self.doRightTrayCleanLayout.setSpacing(6)
        self.doRightTrayCleanLayout.setObjectName(u"doRightTrayCleanLayout")
        self.doRightTrayCleanControlLayout = QHBoxLayout()
        self.doRightTrayCleanControlLayout.setObjectName(u"doRightTrayCleanControlLayout")
        self.doRightTrayCleanLabel = QLabel(self.doFrame)
        self.doRightTrayCleanLabel.setObjectName(u"doRightTrayCleanLabel")
        self.doRightTrayCleanLabel.setStyleSheet(u"font-weight: bold; font-size: 12px;\n"
"border:0px;")

        self.doRightTrayCleanControlLayout.addWidget(self.doRightTrayCleanLabel)

        self.doRightTrayCleanSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doRightTrayCleanControlLayout.addItem(self.doRightTrayCleanSpacer)

        self.doRightTrayCleanStatusIndicator = QPushButton(self.doFrame)
        self.doRightTrayCleanStatusIndicator.setObjectName(u"doRightTrayCleanStatusIndicator")
        self.doRightTrayCleanStatusIndicator.setStyleSheet(u"background-color: #9ca3af; border-radius: 12px; min-width: 24px; min-height: 24px; max-width: 24px; max-height: 24px;")

        self.doRightTrayCleanControlLayout.addWidget(self.doRightTrayCleanStatusIndicator)


        self.doRightTrayCleanLayout.addLayout(self.doRightTrayCleanControlLayout)

        self.doRightTrayCleanComboBox = QComboBox(self.doFrame)
        self.doRightTrayCleanComboBox.addItem("")
        self.doRightTrayCleanComboBox.addItem("")
        self.doRightTrayCleanComboBox.addItem("")
        self.doRightTrayCleanComboBox.addItem("")
        self.doRightTrayCleanComboBox.addItem("")
        self.doRightTrayCleanComboBox.addItem("")
        self.doRightTrayCleanComboBox.addItem("")
        self.doRightTrayCleanComboBox.addItem("")
        self.doRightTrayCleanComboBox.addItem("")
        self.doRightTrayCleanComboBox.setObjectName(u"doRightTrayCleanComboBox")
        self.doRightTrayCleanComboBox.setMinimumSize(QSize(160, 38))

        self.doRightTrayCleanLayout.addWidget(self.doRightTrayCleanComboBox)


        self.doRow5Layout.addLayout(self.doRightTrayCleanLayout)

        self.doRow5Spacer = QSpacerItem(187, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.doRow5Layout.addItem(self.doRow5Spacer)


        self.doItemsLayout.addLayout(self.doRow5Layout)


        self.doMainLayout.addLayout(self.doItemsLayout)

        self.doVerticalSpacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.doMainLayout.addItem(self.doVerticalSpacer)

        self.doSaveButton = QPushButton(self.doFrame)
        self.doSaveButton.setObjectName(u"doSaveButton")
        self.doSaveButton.setStyleSheet(u"background-color: #22c55e; color: white; min-height: 40px; font-size: 14px; font-weight: bold; border-radius: 8px;\n"
"\n"
"background:url(:/io/io/IO\u4fdd\u5b58.png);\n"
"background-repeat: no-repeat;\n"
"background-position: center;\n"
"background-attachment: fixed;")

        self.doMainLayout.addWidget(self.doSaveButton)


        self.mainHorizontalLayout.addWidget(self.doFrame)


        self.retranslateUi(IOWidget)

        QMetaObject.connectSlotsByName(IOWidget)
    # setupUi

    def retranslateUi(self, IOWidget):
        IOWidget.setWindowTitle(QCoreApplication.translate("IOWidget", u"\u667a\u80fd\u5206\u62e3\u7cfb\u7edf - IO\u63a7\u5236", None))
        self.diTitleLabel.setText(QCoreApplication.translate("IOWidget", u"DI", None))
        self.diStartLabel.setText(QCoreApplication.translate("IOWidget", u"\u542f\u52a8", None))
        self.diStartStatusIndicator.setText("")
        self.diStartComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.diStartComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.diStartComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.diStartComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.diStartComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.diStartComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.diStartComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.diStartComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.diStartComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.diPauseLabel.setText(QCoreApplication.translate("IOWidget", u"\u6682\u505c", None))
        self.diPauseStatusIndicator.setText("")
        self.diPauseComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"2", None))
        self.diPauseComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"0", None))
        self.diPauseComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"1", None))
        self.diPauseComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.diPauseComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.diPauseComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.diPauseComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.diPauseComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.diPauseComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.diStopLabel.setText(QCoreApplication.translate("IOWidget", u"\u505c\u6b62", None))
        self.diStopStatusIndicator.setText("")
        self.diStopComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"3", None))
        self.diStopComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"0", None))
        self.diStopComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"1", None))
        self.diStopComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"2", None))
        self.diStopComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.diStopComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.diStopComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.diStopComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.diStopComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.diGrabSignalLabel.setText(QCoreApplication.translate("IOWidget", u"\u6293\u53d6\u4fe1\u53f7", None))
        self.diGrabSignalStatusIndicator.setText("")
        self.diGrabSignalComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"4", None))
        self.diGrabSignalComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"0", None))
        self.diGrabSignalComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"1", None))
        self.diGrabSignalComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"2", None))
        self.diGrabSignalComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"3", None))
        self.diGrabSignalComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.diGrabSignalComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.diGrabSignalComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.diGrabSignalComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.diPressureDetectLabel.setText(QCoreApplication.translate("IOWidget", u"\u6c14\u538b\u68c0\u6d4b", None))
        self.diPressureDetectStatusIndicator.setText("")
        self.diPressureDetectComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"5", None))
        self.diPressureDetectComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"0", None))
        self.diPressureDetectComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"1", None))
        self.diPressureDetectComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"2", None))
        self.diPressureDetectComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"3", None))
        self.diPressureDetectComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"4", None))
        self.diPressureDetectComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.diPressureDetectComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.diPressureDetectComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.diVacuumDetectLabel.setText(QCoreApplication.translate("IOWidget", u"\u771f\u7a7a\u68c0\u6d4b", None))
        self.diVacuumDetectStatusIndicator.setText("")
        self.diVacuumDetectComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"6", None))
        self.diVacuumDetectComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"0", None))
        self.diVacuumDetectComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"1", None))
        self.diVacuumDetectComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"2", None))
        self.diVacuumDetectComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"3", None))
        self.diVacuumDetectComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"4", None))
        self.diVacuumDetectComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"5", None))
        self.diVacuumDetectComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.diVacuumDetectComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.diLeftTrayDetectLabel.setText(QCoreApplication.translate("IOWidget", u"\u5de6\u579b\u76d8\u68c0\u6d4b", None))
        self.diLeftTrayDetectStatusIndicator.setText("")
        self.diLeftTrayDetectComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"7", None))
        self.diLeftTrayDetectComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"0", None))
        self.diLeftTrayDetectComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"1", None))
        self.diLeftTrayDetectComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"2", None))
        self.diLeftTrayDetectComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"3", None))
        self.diLeftTrayDetectComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"4", None))
        self.diLeftTrayDetectComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"5", None))
        self.diLeftTrayDetectComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"6", None))
        self.diLeftTrayDetectComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.diRightTrayDetectLabel.setText(QCoreApplication.translate("IOWidget", u"\u53f3\u579b\u76d8\u68c0\u6d4b", None))
        self.diRightTrayDetectStatusIndicator.setText("")
        self.diRightTrayDetectComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"7", None))
        self.diRightTrayDetectComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"0", None))
        self.diRightTrayDetectComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"1", None))
        self.diRightTrayDetectComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"2", None))
        self.diRightTrayDetectComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"3", None))
        self.diRightTrayDetectComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"4", None))
        self.diRightTrayDetectComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"5", None))
        self.diRightTrayDetectComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"6", None))
        self.diRightTrayDetectComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.diSaveButton.setText(QCoreApplication.translate("IOWidget", u"\u4fdd\u5b58", None))
        self.doTitleLabel.setText(QCoreApplication.translate("IOWidget", u"DO", None))
        self.doStartLightLabel.setText(QCoreApplication.translate("IOWidget", u"\u542f\u52a8\u706f", None))
        self.doStartLightStatusIndicator.setText("")
        self.doStartLightComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.doStartLightComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.doStartLightComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.doStartLightComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.doStartLightComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.doStartLightComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.doStartLightComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.doStartLightComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.doStartLightComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.doPauseLightLabel.setText(QCoreApplication.translate("IOWidget", u"\u6682\u505c\u706f", None))
        self.doPauseLightStatusIndicator.setText("")
        self.doPauseLightComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.doPauseLightComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.doPauseLightComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.doPauseLightComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.doPauseLightComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.doPauseLightComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.doPauseLightComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.doPauseLightComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.doPauseLightComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.doStopLightLabel.setText(QCoreApplication.translate("IOWidget", u"\u505c\u6b62\u706f", None))
        self.doStopLightStatusIndicator.setText("")
        self.doStopLightComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.doStopLightComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.doStopLightComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.doStopLightComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.doStopLightComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.doStopLightComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.doStopLightComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.doStopLightComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.doStopLightComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.doGrabLabel.setText(QCoreApplication.translate("IOWidget", u"\u6293\u53d6", None))
        self.doGrabStatusIndicator.setText("")
        self.doGrabComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.doGrabComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.doGrabComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.doGrabComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.doGrabComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.doGrabComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.doGrabComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.doGrabComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.doGrabComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.doReleaseLabel.setText(QCoreApplication.translate("IOWidget", u"\u91ca\u653e", None))
        self.doReleaseStatusIndicator.setText("")
        self.doReleaseComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.doReleaseComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.doReleaseComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.doReleaseComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.doReleaseComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.doReleaseComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.doReleaseComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.doReleaseComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.doReleaseComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.doLeftRunningLabel.setText(QCoreApplication.translate("IOWidget", u"\u5de6\u579b\u8fdb\u884c\u4e2d", None))
        self.doLeftRunningStatusIndicator.setText("")
        self.doLeftRunningComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.doLeftRunningComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.doLeftRunningComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.doLeftRunningComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.doLeftRunningComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.doLeftRunningComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.doLeftRunningComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.doLeftRunningComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.doLeftRunningComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.doLeftTrayCleanLabel.setText(QCoreApplication.translate("IOWidget", u"\u5de6\u579b\u76d8\u6e05\u9664", None))
        self.doLeftTrayCleanStatusIndicator.setText("")
        self.doLeftTrayCleanComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.doLeftTrayCleanComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.doLeftTrayCleanComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.doLeftTrayCleanComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.doLeftTrayCleanComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.doLeftTrayCleanComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.doLeftTrayCleanComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.doLeftTrayCleanComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.doLeftTrayCleanComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.doRightRunningLabel.setText(QCoreApplication.translate("IOWidget", u"\u53f3\u579b\u8fd0\u884c\u4e2d", None))
        self.doRightRunningStatusIndicator.setText("")
        self.doRightRunningComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.doRightRunningComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.doRightRunningComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.doRightRunningComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.doRightRunningComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.doRightRunningComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.doRightRunningComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.doRightRunningComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.doRightRunningComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.doRightTrayCleanLabel.setText(QCoreApplication.translate("IOWidget", u"\u53f3\u579b\u76d8\u6e05\u9664", None))
        self.doRightTrayCleanStatusIndicator.setText("")
        self.doRightTrayCleanComboBox.setItemText(0, QCoreApplication.translate("IOWidget", u"0", None))
        self.doRightTrayCleanComboBox.setItemText(1, QCoreApplication.translate("IOWidget", u"1", None))
        self.doRightTrayCleanComboBox.setItemText(2, QCoreApplication.translate("IOWidget", u"2", None))
        self.doRightTrayCleanComboBox.setItemText(3, QCoreApplication.translate("IOWidget", u"3", None))
        self.doRightTrayCleanComboBox.setItemText(4, QCoreApplication.translate("IOWidget", u"4", None))
        self.doRightTrayCleanComboBox.setItemText(5, QCoreApplication.translate("IOWidget", u"5", None))
        self.doRightTrayCleanComboBox.setItemText(6, QCoreApplication.translate("IOWidget", u"6", None))
        self.doRightTrayCleanComboBox.setItemText(7, QCoreApplication.translate("IOWidget", u"7", None))
        self.doRightTrayCleanComboBox.setItemText(8, QCoreApplication.translate("IOWidget", u"\u7a7a", None))

        self.doSaveButton.setText(QCoreApplication.translate("IOWidget", u"\u4fdd\u5b58", None))
    # retranslateUi

