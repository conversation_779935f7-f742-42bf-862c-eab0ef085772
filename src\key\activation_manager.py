import sys
import os
import time
import hashlib
from datetime import timedelta

from PySide6.QtWidgets import (QApplication, QMessageBox, QInputDialog, QDialog,
                               QVBoxLayout, QLabel, QLineEdit, QHBoxLayout, QPushButton)
from PySide6.QtCore import QTimer, Signal
from PySide6.QtGui import Qt

from .Locking_key import get_cpu_id_fingerprint, get_short_machine_code
from .app_activation import (validate_activation_code,
                            encrypt_time_data,
                            decrypt_time_data)
from ..common.sql_lite_tool import SqliteTool
from ..common import global_vars as gv
from ..core.solve_keyboard import FloatingDisplay, MonitoredLineEdit


class ActivationDialog(QDialog):
    """一个非模态的激活码输入对话框，用于Linux以避免UI阻塞。"""
    submitted = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("软件激活")
        self.setModal(False)

        self.layout = QVBoxLayout(self)
        self.prompt_label = QLabel("请输入您的激活码：", self)
        self.code_input = QLineEdit(self)
        self.code_input.setPlaceholderText("在此处粘贴或输入激活码")

        self.button_layout = QHBoxLayout()
        self.ok_button = QPushButton("确定", self)
        self.cancel_button = QPushButton("取消", self)
        self.button_layout.addStretch()
        self.button_layout.addWidget(self.ok_button)
        self.button_layout.addWidget(self.cancel_button)

        self.layout.addWidget(self.prompt_label)
        self.layout.addWidget(self.code_input)
        self.layout.addLayout(self.button_layout)
        self.setLayout(self.layout)

        self.ok_button.clicked.connect(self.on_ok_clicked)
        self.cancel_button.clicked.connect(self.reject)
        self.code_input.returnPressed.connect(self.on_ok_clicked)

        if gv.system_type == "Linux":
            self.floating_window = FloatingDisplay()
            self.monitor = MonitoredLineEdit(self.code_input, '激活码', self.floating_window)
            self.finished.connect(self.floating_window.close)

    def on_ok_clicked(self):
        code = self.code_input.text().strip()
        if code:
            self.submitted.emit(code)
            self.accept()

    _instance = None

    @staticmethod
    def get_instance(parent=None):
        if ActivationDialog._instance is None or not ActivationDialog._instance.isVisible():
            ActivationDialog._instance = ActivationDialog(parent)
        return ActivationDialog._instance


class ActivationManager:
    """管理激活、防篡改计时和重复使用检查。"""

    def __init__(self, app, on_success_callback):
        self.app = app
        self.db_tool = SqliteTool.get_instance()
        self.on_success_callback = on_success_callback
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_and_update_status)
        self.last_db_update_time = 0
        self.time_data = {}

    def start(self):
        """启动激活检查流程。"""
        self._create_activation_tables()
        
        record = self.db_tool.select("activation_status", condition="id = 1")
        if not record:
            print("未找到激活记录，启动激活流程。")
            self.run_activation_flow()
        else:
            encrypted_data = record[0]['encrypted_data']
            time_data = decrypt_time_data(encrypted_data)
            if time_data is None:
                QMessageBox.critical(None, "激活错误", "激活数据已损坏或被篡改！软件需要重新激活。")
                self.db_tool.execute_sql("DELETE FROM activation_status")
                self.run_activation_flow()
            else:
                self.time_data = time_data
                self.check_and_update_status()

    def _create_activation_tables(self):
        """创建激活相关的数据库表"""
        self.db_tool.execute_sql("""
            CREATE TABLE IF NOT EXISTS activation_status
            (
                id INTEGER PRIMARY KEY,
                encrypted_data TEXT NOT NULL
            );""")
        self.db_tool.execute_sql("""
            CREATE TABLE IF NOT EXISTS used_activation_codes
            (
                code_hash TEXT PRIMARY KEY,
                activated_at INTEGER NOT NULL
            );""")

    def _get_flag_file_path(self, code_hash):
        # Windows系统使用系统目录
        if gv.system_type != "Linux":
            # 使用系统临时目录的隐藏子目录
            import tempfile
            system_temp = tempfile.gettempdir()
            app_data_dir = os.path.join(system_temp, '.sys_cache', 'python')
        else:
            # Linux系统使用系统级目录
            app_data_dir = '/var/tmp/._sys_data'
        
        os.makedirs(app_data_dir, exist_ok=True)
        # 使用更复杂的文件名混淆
        obfuscated_name = hashlib.md5(f"_{code_hash}_sys".encode()).hexdigest()
        return os.path.join(app_data_dir, f'.{obfuscated_name}.tmp')

    def _check_flag_file(self, code_hash):
        """多重检查：文件 + 注册表/系统属性"""
        # 检查隐藏文件
        file_exists = os.path.exists(self._get_flag_file_path(code_hash))
        
        # 检查注册表（Windows）或系统属性（Linux）
        registry_exists = self._check_flag_registry(code_hash)
        
        # 检查数据库中的额外验证字段
        db_record = self.db_tool.select("used_activation_codes", 
                                       condition="code_hash = ?", 
                                       params=(code_hash,))
        
        return file_exists or registry_exists or bool(db_record)

    def _write_flag_file(self, code_hash):
        """多重写入验证信息"""
        try:
            # 写入隐藏文件
            with open(self._get_flag_file_path(code_hash), 'w') as f:
                f.write(str(int(time.time())))
            
            # 写入注册表（Windows）
            self._write_flag_registry(code_hash)
            
        except Exception as e:
            print(f"写入验证信息失败: {e}")

    def _check_flag_registry(self, code_hash):
        """Windows下使用注册表存储验证信息"""
        if gv.system_type != "Linux":
            try:
                import winreg
                key_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced"
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_READ) as key:
                    value_name = f"Cache_{hashlib.md5(code_hash.encode()).hexdigest()[:16]}"
                    winreg.QueryValueEx(key, value_name)
                    return True
            except (ImportError, FileNotFoundError, OSError):
                return False
        return False

    def _write_flag_registry(self, code_hash):
        """Windows下写入注册表"""
        if gv.system_type != "Linux":
            try:
                import winreg
                key_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Advanced"
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_WRITE) as key:
                    value_name = f"Cache_{hashlib.md5(code_hash.encode()).hexdigest()[:16]}"
                    winreg.SetValueEx(key, value_name, 0, winreg.REG_DWORD, int(time.time()))
            except (ImportError, OSError) as e:
                print(f"注册表写入失败: {e}")

    def run_activation_flow(self):
        """引导用户完成激活，根据系统选择不同对话框。"""
        hw_fp = get_cpu_id_fingerprint()
        if not hw_fp:
            QMessageBox.critical(None, "严重错误", "无法获取硬件指纹。")
            sys.exit(1)

        short_code = get_short_machine_code(hw_fp)
        QApplication.clipboard().setText(short_code)
        QMessageBox.information(None, "软件激活", f"您的用户机器码为：\n\n{short_code}\n\n(已自动复制到剪贴板)")

        if gv.system_type == "Linux":
            dialog = ActivationDialog.get_instance()
            dialog.submitted.connect(lambda code: self.process_code(code, dialog))
            dialog.rejected.connect(sys.exit)
            dialog.show()
        else:  # Windows/Other systems
            while True:
                code, ok = QInputDialog.getText(None, "软件激活", "请输入您的激活码：")
                if not ok:
                    sys.exit(0)
                if self.process_code(code):  # 如果处理成功，则跳出循环
                    break

    def process_code(self, code, dialog=None):
        """处理输入的激活码，返回True表示成功，False表示失败。"""
        code = code.strip()

        if not code:
            return False

        # 修复：统一转换为小写来计算哈希，防止大小写绕过重复检查
        code_hash = hashlib.sha256(code.lower().encode()).hexdigest()
        if self.db_tool.select("used_activation_codes", condition="code_hash = ?",
                               params=(code_hash,)) or self._check_flag_file(code_hash):
            self.show_error("该激活码已被使用，无法重复激活。", dialog)
            return False

        is_valid, payload = validate_activation_code(code)
        if not is_valid:
            self.show_error(payload.get('error', '未知错误'), dialog)
            return False

        # --- 激活成功，处理数据库事务 ---
        try:
            self.db_tool.begin_transaction()
            current_ts = int(time.time())
            validity_hours = payload['validity_hours']
            self.time_data = {'start_ts': current_ts, 'validity_sec': validity_hours * 3600}
            if gv.system_type != "Linux":  # 只有非Linux系统才记录last_seen_ts
                self.time_data['last_seen_ts'] = current_ts
            encrypted_data = encrypt_time_data(self.time_data)

            self.db_tool.execute_sql("DELETE FROM activation_status")
            self.db_tool.execute_sql("INSERT INTO activation_status (id, encrypted_data) VALUES (?, ?)",
                                     params=(1, encrypted_data))
            self.db_tool.execute_sql("INSERT INTO used_activation_codes (code_hash, activated_at) VALUES (?, ?)",
                                     params=(code_hash, current_ts))

            self.db_tool.commit_transaction()
            self._write_flag_file(code_hash)

            QMessageBox.information(None, "成功", f"软件激活成功！有效期为 {validity_hours} 小时。")
            if dialog: dialog.accept()  # 关闭非模态对话框
            self.check_and_update_status()  # 立即检查并启动计时器
            return True
        except Exception as e:
            self.db_tool.rollback_transaction()
            self.show_error(f"激活过程中发生数据库错误：{e}", dialog)
            return False

    def show_error(self, message, dialog=None):
        """统一的错误显示函数。"""
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setText(f"激活失败: {message}")
        msg_box.setWindowTitle("激活失败")
        if dialog:  # 如果是Linux非模态对话框，则错误框关闭后重新显示它
            msg_box.finished.connect(dialog.show)
        msg_box.exec()

    def check_and_update_status(self):
        """核心的防篡改检查和状态更新函数。"""
        current_ts = int(time.time())

        if gv.system_type != "Linux":
            last_seen_ts = self.time_data.get('last_seen_ts', current_ts)
            if current_ts < last_seen_ts - 60:  # 允许60秒的误差
                QMessageBox.critical(None, "激活失效", "检测到系统时间被向后修改，激活已失效！")
                self.db_tool.execute_sql("DELETE FROM activation_status")
                self.timer.stop()
                self.run_activation_flow()
                return

        # 总时长检查（对所有系统都有效）
        start_ts = self.time_data.get('start_ts', current_ts)
        validity_sec = self.time_data.get('validity_sec', 0)

        elapsed_sec = current_ts - start_ts

        if elapsed_sec >= validity_sec:
            QMessageBox.warning(None, "激活已过期", "您的软件使用时间已到期，请重新激活。")
            self.db_tool.execute_sql("DELETE FROM activation_status")
            self.timer.stop()
            self.run_activation_flow()
            return

        if gv.system_type != "Linux":
            # 更新时间锚
            self.time_data['last_seen_ts'] = current_ts
            if current_ts - self.last_db_update_time >= 300:  # 每5分钟更新一次数据库
                print("正在更新数据库中的时间锚...")
                encrypted_data = encrypt_time_data(self.time_data)
                self.db_tool.execute_sql("REPLACE INTO activation_status (id, encrypted_data) VALUES (?, ?)",
                                         params=(1, encrypted_data))
                self.last_db_update_time = current_ts

        if not self.timer.isActive():
            remaining_sec = validity_sec - elapsed_sec
            print(f"激活状态正常。剩余时间: {timedelta(seconds=int(remaining_sec))}")
            self.on_success_callback()  # 首次验证成功，启动主程序
            # 计时器可以继续运行，以在到期时触发重新激活流程
            self.timer.start(60 * 1000)  # 每分钟检查一次