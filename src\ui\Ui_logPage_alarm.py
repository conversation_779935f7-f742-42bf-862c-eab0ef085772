# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'logPage_alarm.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON>rush, QColor, QC<PERSON>alGradient, QCursor,
    QF<PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QHeaderView, QSizePolicy,
    QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget)

class Ui_Form_2(object):
    def setupUi(self, Form_2):
        if not Form_2.objectName():
            Form_2.setObjectName(u"Form_2")
        Form_2.resize(839, 430)
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(Form_2.sizePolicy().hasHeightForWidth())
        Form_2.setSizePolicy(sizePolicy)
        Form_2.setMinimumSize(QSize(400, 300))
        self.verticalLayout = QVBoxLayout(Form_2)
        self.verticalLayout.setSpacing(5)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(5, 5, 5, 5)
        self.alarmTableWidget = QTableWidget(Form_2)
        if (self.alarmTableWidget.columnCount() < 3):
            self.alarmTableWidget.setColumnCount(3)
        __qtablewidgetitem = QTableWidgetItem()
        self.alarmTableWidget.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.alarmTableWidget.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.alarmTableWidget.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        if (self.alarmTableWidget.rowCount() < 6):
            self.alarmTableWidget.setRowCount(6)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.alarmTableWidget.setVerticalHeaderItem(0, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.alarmTableWidget.setVerticalHeaderItem(1, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.alarmTableWidget.setVerticalHeaderItem(2, __qtablewidgetitem5)
        __qtablewidgetitem6 = QTableWidgetItem()
        self.alarmTableWidget.setVerticalHeaderItem(3, __qtablewidgetitem6)
        __qtablewidgetitem7 = QTableWidgetItem()
        self.alarmTableWidget.setVerticalHeaderItem(4, __qtablewidgetitem7)
        __qtablewidgetitem8 = QTableWidgetItem()
        self.alarmTableWidget.setVerticalHeaderItem(5, __qtablewidgetitem8)
        self.alarmTableWidget.setObjectName(u"alarmTableWidget")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy1.setHorizontalStretch(1)
        sizePolicy1.setVerticalStretch(1)
        sizePolicy1.setHeightForWidth(self.alarmTableWidget.sizePolicy().hasHeightForWidth())
        self.alarmTableWidget.setSizePolicy(sizePolicy1)
        self.alarmTableWidget.setMinimumSize(QSize(350, 250))
        self.alarmTableWidget.setStyleSheet(u"\n"
"/* \u8868\u683c\u5916\u8fb9\u6846 */\n"
"QTableWidget {\n"
"    border: 2px solid #808080;\n"
"    font-size: 15px;\n"
"    background-color: white;\n"
"}\n"
"\n"
"/* \u5355\u5143\u683c\u8fb9\u6846 */\n"
"QTableView::item {\n"
"    border: 1px solid #d0d0d0;\n"
"    padding: 5px;\n"
"}\n"
"\n"
"/* \u8868\u5934\u8fb9\u6846 */\n"
"QHeaderView::section {\n"
"    font-size: 16px;\n"
"    padding: 10px;\n"
"    background-color: #f8f9fa;\n"
"    border-top: 2px solid #808080;\n"
"    border-bottom: 2px solid #808080;\n"
"    border-left: 1px solid #d0d0d0;\n"
"    border-right: 1px solid #d0d0d0;\n"
"}\n"
"\n"
"/* \u89d2\u6309\u94ae\u533a\u57df\u8fb9\u6846 */\n"
"QTableCornerButton::section {\n"
"    border: 2px solid #808080;\n"
"    background-color: #f8f9fa;\n"
"}\n"
"\n"
"/* \u7f51\u683c\u7ebf\u63a7\u5236 */\n"
"QTableView {\n"
"    show-decoration-selected: 1;\n"
"    gridline-color: #d0d0d0;\n"
"    selection-background-color: #e3f2fd;\n"
"}\n"
"\n"
"/* \u9009\u4e2d\u884c\u6837\u5f0f */\n"
"QTableView::i"
                        "tem:selected {\n"
"    background-color: #e3f2fd;\n"
"    color: #000;\n"
"}\n"
"      ")
        self.alarmTableWidget.setAlternatingRowColors(True)
        self.alarmTableWidget.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.alarmTableWidget.setVerticalScrollMode(QAbstractItemView.ScrollMode.ScrollPerPixel)
        self.alarmTableWidget.setHorizontalScrollMode(QAbstractItemView.ScrollMode.ScrollPerPixel)
        self.alarmTableWidget.horizontalHeader().setMinimumSectionSize(100)
        self.alarmTableWidget.horizontalHeader().setDefaultSectionSize(200)
        self.alarmTableWidget.horizontalHeader().setStretchLastSection(True)
        self.alarmTableWidget.verticalHeader().setVisible(False)
        self.alarmTableWidget.verticalHeader().setDefaultSectionSize(60)

        self.verticalLayout.addWidget(self.alarmTableWidget)


        self.retranslateUi(Form_2)

        QMetaObject.connectSlotsByName(Form_2)
    # setupUi

    def retranslateUi(self, Form_2):
        ___qtablewidgetitem = self.alarmTableWidget.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("Form_2", u"\u5e8f\u53f7", None));
        ___qtablewidgetitem1 = self.alarmTableWidget.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("Form_2", u"\u62a5\u8b66", None));
        ___qtablewidgetitem2 = self.alarmTableWidget.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("Form_2", u"\u65f6\u95f4", None));
        pass
    # retranslateUi

