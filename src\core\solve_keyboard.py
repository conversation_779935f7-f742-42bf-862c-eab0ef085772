import psutil
from PySide6.QtWidgets import QLineEdit, QWidget, QLabel, QVBoxLayout, QApplication
from PySide6.QtCore import Qt, QPoint
from ..common import global_vars as gv

try:
    import win32gui

    PYWIN32_AVAILABLE = True
except ImportError:
    PYWIN32_AVAILABLE = False
    print("警告: pywin32 库未安装, 键盘检测功能将受限。请运行 'pip install pywin32' 来解决。")

def check_virtual_keyboard():
    """
    检测 Windows 系统中是否有电子键盘(osk.exe 或 TabTip.exe)正在运行。
    返回值：
        True:检测到电子键盘
        False:未检测到电子键盘
    """
    return True

class FloatingDisplay(QWidget):
    """
    浮动窗口
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        if gv.language_ == "English":
            self.setWindowTitle("Real-time display of content")
        else:
            self.setWindowTitle("内容实时显示")
        # 设置窗口标志：保持在顶部，并且是工具窗口（通常没有任务栏图标）
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.Tool)
        self.resize(200, 60)

        self.layout = QVBoxLayout()

        self.label = QLabel("正在编辑的内容将在此显示")
        font = self.label.font()
        font.setPointSize(14)  # 放大字体，使其更清晰
        self.label.setFont(font)
        self.label.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.label)
        self.setLayout(self.layout)

        # 将窗口移动到屏幕顶部中央
        if QApplication.primaryScreen():
            screen_geometry = QApplication.primaryScreen().geometry()
            x = 120
            y = 90  # 离屏幕顶部一点距离
            self.move(QPoint(x, y))

    def update_text(self, display_name, text):
        self.label.setText(f"{display_name}: {text}")


class MonitoredLineEdit:
    """
    监视器类，使用我们新的 check_virtual_keyboard 函数。
    """
    def __init__(self, target_line_edit, display_name, floating_window):
        # 确认目标是QLineEdit
        if not isinstance(target_line_edit, QLineEdit):
            raise TypeError("MonitoredLineEdit 只能监视 QLineEdit 实例。")

        self.target = target_line_edit
        self.display_name = display_name
        self.floating_window = floating_window

        self.original_focusInEvent = self.target.focusInEvent
        self.original_focusOutEvent = self.target.focusOutEvent

        self.target.focusInEvent = self.focusInEvent
        self.target.focusOutEvent = self.focusOutEvent

        self.target.textChanged.connect(self._on_text_changed)
    def focusInEvent(self, event):
        # 当输入框获得焦点时，检查系统是否已将键盘变为可见状态
        if self.floating_window and check_virtual_keyboard():
            self.floating_window.update_text(self.display_name, self.target.text())
            self.floating_window.show()

        # 必须调用原始的事件处理器，以保证QLineEdit的正常功能
        self.original_focusInEvent(event)

    def focusOutEvent(self, event):
        # 当失去焦点时，隐藏浮动窗口，然后调用原始的事件处理
        if self.floating_window:
            self.floating_window.hide()

        self.original_focusOutEvent(event)

    def _on_text_changed(self, text):
        # 只有当输入框有焦点且浮动窗口已显示时，才更新内容
        if self.target.hasFocus() and self.floating_window and self.floating_window.isVisible():
            self.floating_window.update_text(self.display_name, text)