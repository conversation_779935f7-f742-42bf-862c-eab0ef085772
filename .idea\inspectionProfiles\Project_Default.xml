<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="10">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="PyInstaller" />
            <item index="2" class="java.lang.String" itemvalue="PySide6" />
            <item index="3" class="java.lang.String" itemvalue="numpy" />
            <item index="4" class="java.lang.String" itemvalue="pycryptodome" />
            <item index="5" class="java.lang.String" itemvalue="loguru" />
            <item index="6" class="java.lang.String" itemvalue="openpyxl" />
            <item index="7" class="java.lang.String" itemvalue="psutil" />
            <item index="8" class="java.lang.String" itemvalue="rectpack" />
            <item index="9" class="java.lang.String" itemvalue="cython" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>