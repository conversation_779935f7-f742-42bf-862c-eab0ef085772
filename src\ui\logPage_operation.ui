<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form_1</class>
 <widget class="QWidget" name="Form_1">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>839</width>
    <height>430</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>400</width>
    <height>300</height>
   </size>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>5</number>
   </property>
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>5</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <item>
    <widget class="QTableWidget" name="tableWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>1</horstretch>
       <verstretch>1</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>350</width>
       <height>250</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">
/* 表格外边框 */
QTableWidget {
    border: 2px solid #808080;
    font-size: 15px;
    background-color: white;
}

/* 单元格边框 */
QTableView::item {
    border: 1px solid #d0d0d0;
    padding: 5px;
}

/* 表头边框 */
QHeaderView::section {
    font-size: 16px;
    padding: 10px;
    background-color: #f8f9fa;
    border-top: 2px solid #808080;
    border-bottom: 2px solid #808080;
    border-left: 1px solid #d0d0d0;
    border-right: 1px solid #d0d0d0;
}

/* 角按钮区域边框 */
QTableCornerButton::section {
    border: 2px solid #808080;
    background-color: #f8f9fa;
}

/* 网格线控制 */
QTableView {
    show-decoration-selected: 1;
    gridline-color: #d0d0d0;
    selection-background-color: #e3f2fd;
}

/* 选中行样式 */
QTableView::item:selected {
    background-color: #e3f2fd;
    color: #000;
}
      </string>
     </property>
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
     </property>
     <property name="verticalScrollMode">
      <enum>QAbstractItemView::ScrollMode::ScrollPerPixel</enum>
     </property>
     <property name="horizontalScrollMode">
      <enum>QAbstractItemView::ScrollMode::ScrollPerPixel</enum>
     </property>
     <attribute name="horizontalHeaderMinimumSectionSize">
      <number>100</number>
     </attribute>
     <attribute name="horizontalHeaderDefaultSectionSize">
      <number>200</number>
     </attribute>
     <attribute name="horizontalHeaderStretchLastSection">
      <bool>true</bool>
     </attribute>
     <attribute name="verticalHeaderVisible">
      <bool>false</bool>
     </attribute>
     <attribute name="verticalHeaderDefaultSectionSize">
      <number>60</number>
     </attribute>
     <row>
      <property name="text">
       <string/>
      </property>
     </row>
     <row>
      <property name="text">
       <string/>
      </property>
     </row>
     <row>
      <property name="text">
       <string/>
      </property>
     </row>
     <row>
      <property name="text">
       <string/>
      </property>
     </row>
     <row>
      <property name="text">
       <string/>
      </property>
     </row>
     <row>
      <property name="text">
       <string/>
      </property>
     </row>
     <column>
      <property name="text">
       <string>序号</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>操作内容</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>时间</string>
      </property>
     </column>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
