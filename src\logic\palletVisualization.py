from PySide6.QtWidgets import QWidget
from PySide6.QtGui import QPainter, QColor, QBrush, QPen, QLinearGradient, QFont, QPainterPath
from PySide6.QtCore import Signal, Qt, QRectF

class PalletVisualization(QWidget):
    """托盘可视化组件"""
    
    boxSelected = Signal(str)  # 箱子选中信号，传递箱子ID
    boxesSelected = Signal(list)  # 多个箱子选中信号，传递箱子ID列表
    boxMoved = Signal(str, float, float)  # 箱子移动信号，传递箱子ID和新位置
    boxNumberChanged = Signal(str, str)  # 箱子编号变更信号，传递旧ID和新ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        # 初始化数据
        self.pattern = None
        self.selected_box_id = None
        self.selected_box_ids = []  # 存储多选的箱子ID
        self.remove_box_ids = []
        self.remove_box_id = None
        self.dragging = False
        self.drag_start_pos = None
        self.drag_box_start_pos = None
        self.multi_select_mode = False  # 多选模式标志
        
        # 设置最小大小
        self.setMinimumSize(391, 431)
        
        self.box_color_top = QColor(100, 150, 220)      # 浅蓝色顶部
        self.box_color_bottom = QColor(70, 120, 200)    # 浅蓝色底部
        self.selected_box_color_top = QColor(40, 80, 150)  # 深蓝色顶部
        self.selected_box_color_bottom = QColor(20, 60, 120)  # 深蓝色底部
        self.multi_selected_box_color_top = QColor(220, 100, 100)  # 红色顶部
        self.multi_selected_box_color_bottom = QColor(180, 60, 60)  # 红色底部

        # 样式设置
        self.show_grid = False
        self.show_rotation_mark = False  # 不显示对角线标记
        self.show_watermark = True
        self.box_corner_radius = 5
        
        # 设置焦点策略，使组件可以接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 设置鼠标追踪，以便能够实时获取鼠标位置
        self.setMouseTracking(True)
    
    def set_pattern(self, pattern):
        """设置托盘模式"""
        self.pattern = pattern
        self.selected_box_id = None
        self.selected_box_ids = []
        self.update()

    def calculate_display_params(self):
        """计算显示参数，使用整个控件区域但留出安全边距"""
        width = self.width()
        height = self.height()

        # 留出小的安全边距，确保箱子不会绘制到控件边界外
        margin = 0
        display_x = margin
        display_y = margin
        display_width = width - 2 * margin
        display_height = height - 2 * margin

        # 确保显示区域为正数
        display_width = max(10, display_width)
        display_height = max(10, display_height)

        # 确保托盘尺寸有效，用于计算缩放比例
        pallet_width = max(1, self.pattern.pallet_width) if self.pattern.pallet_width > 0 else 1200
        pallet_length = max(1, self.pattern.pallet_length) if self.pattern.pallet_length > 0 else 1200

        # 计算缩放比例，使托盘内容适应显示区域
        scale_x = display_width / pallet_width
        scale_y = display_height / pallet_length

        # 使用较小的缩放比例，保持比例
        scale = min(scale_x, scale_y)

        # 限制缩放比例的范围
        scale = max(0.01, min(scale, 10.0))

        return {
            'scale': scale,
            'display_x': display_x,
            'display_y': display_y,
            'display_width': display_width,
            'display_height': display_height,
            'pallet_width': pallet_width,
            'pallet_length': pallet_length
        }
    
    def set_multi_select_mode(self, enabled):
        """设置多选模式"""
        self.multi_select_mode = enabled
        if not enabled:
            self.selected_box_ids = []
            if self.selected_box_id:
                self.selected_box_ids = [self.selected_box_id]
        self.update()
    
    def resizeEvent(self, event):
        """处理窗口大小改变事件，确保托盘和箱子按比例显示"""
        super().resizeEvent(event)
        self.update()  # 重绘界面以更新托盘和箱子显示

    def paintEvent(self, event):
        """绘制托盘可视化"""
        if not self.pattern:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 获取控件尺寸
        width = self.width()
        height = self.height()

        # 使用新的显示参数计算方法
        params = self.calculate_display_params()
        scale = params['scale']
        display_x = params['display_x']
        display_y = params['display_y']
        display_width = params['display_width']
        display_height = params['display_height']
        
        # 设置裁剪区域，确保绘制不超出控件边界
        painter.setClipRect(0, 0, width, height)

        # 绘制箱子
        if self.pattern.boxes:
            # 绘制每个箱子
            for box in self.pattern.boxes:
                # 计算箱子在视图中的实际位置
                box_x = display_x + box.x * scale
                box_y = display_y + box.y * scale
                box_width = box.width * scale
                box_height = box.length * scale

                # 严格检查箱子是否完全在显示区域内
                if (box_x < display_x or box_y < display_y or
                    box_x + box_width > display_x + display_width or
                    box_y + box_height > display_y + display_height):
                    # 箱子超出显示区域边界，不绘制
                    continue
                
                # 创建渐变色
                gradient = QLinearGradient(box_x, box_y, box_x, box_y + box_height)

                # 根据选中状态设置颜色
                if self.multi_select_mode and box.id in self.selected_box_ids:
                    # 多选模式下被选中的箱子
                    gradient.setColorAt(0, self.multi_selected_box_color_top)
                    gradient.setColorAt(1, self.multi_selected_box_color_bottom)
                    painter.setPen(Qt.NoPen)
                elif box.id == self.selected_box_id:
                    # 单选模式下被选中的箱子
                    gradient.setColorAt(0, self.selected_box_color_top)
                    gradient.setColorAt(1, self.selected_box_color_bottom)
                    painter.setPen(Qt.NoPen)
                else:
                    # 未选中的箱子
                    gradient.setColorAt(0, self.box_color_top)
                    gradient.setColorAt(1, self.box_color_bottom)
                    painter.setPen(Qt.NoPen)

                painter.setBrush(QBrush(gradient))

                # 绘制箱子（使用裁剪后的区域）
                # 使用圆角矩形，使箱子外观更加美观
                path = QPainterPath()
                path.addRoundedRect(
                    QRectF(box_x, box_y, box_width, box_height),
                    self.box_corner_radius, self.box_corner_radius
                )
                painter.drawPath(path)
                
                # 绘制箱子编号（根据旋转状态调整文字方向）
                font = QFont()
                font.setBold(True)
                font.setPointSize(10)
                painter.setFont(font)
                painter.setPen(QPen(QColor(255, 255, 255)))

                # 获取箱子编号
                box_number = ""
                if isinstance(box.id, str) and box.id.startswith("box_"):
                    box_number = box.id.split("_")[1]
                else:
                    box_number = str(box.id)

                # 保存当前变换状态
                painter.save()

                # 根据旋转角度旋转文字
                if hasattr(box, 'rotation_angle') and box.rotation_angle != 0:
                    # 计算箱子中心点
                    center_x = box_x + box_width / 2
                    center_y = box_y + box_height / 2

                    # 移动到箱子中心，旋转指定角度，再移回
                    painter.translate(center_x, center_y)
                    painter.rotate(box.rotation_angle)
                    painter.translate(-center_x, -center_y)

                # 显示箱子编号
                painter.drawText(
                    int(box_x), int(box_y),
                    int(box_width), int(box_height),
                    Qt.AlignCenter,
                    str(box_number)
                )

                # 恢复变换状态
                painter.restore()
    
    def mousePressEvent(self, event):
        """处理鼠标按下事件"""
        if not self.pattern or not self.pattern.boxes:
            return
            
        # 使用新的显示参数计算方法
        params = self.calculate_display_params()
        scale = params['scale']
        display_x = params['display_x']
        display_y = params['display_y']
        
        # 检查点击是否在箱子上
        # 从列表末尾开始遍历，优先选中最上方（最后绘制）的箱子
        clicked_box_id = None
        for box in reversed(self.pattern.boxes):
            # 计算箱子在视图中的实际位置
            box_x = display_x + box.x * scale
            box_y = display_y + box.y * scale
            box_width = box.width * scale
            box_height = box.length * scale

            # 检查点击是否在箱子上
            box_rect = QRectF(box_x, box_y, box_width, box_height)
            if box_rect.contains(event.position()):
                clicked_box_id = box.id
                break  # 找到第一个（最上方的）匹配箱子就停止
        
        # 处理箱子选择
        if clicked_box_id:
            if self.multi_select_mode:
                # 多选模式 - 只使用左键进行多选操作
                if event.button() == Qt.LeftButton:
                    if clicked_box_id in self.selected_box_ids:
                        # 如果点击的是已选中的箱子
                        if len(self.selected_box_ids) > 1:
                            # 如果有多个选中的箱子，准备拖动所有选中的箱子
                            self.dragging = True
                            self.drag_start_pos = event.position()
                            self.selected_box_id = clicked_box_id  # 设置主选中箱子

                            # 记录所有选中箱子的初始位置
                            self.drag_boxes_start_pos = {}
                            for box in self.pattern.boxes:
                                if box.id in self.selected_box_ids:
                                    self.drag_boxes_start_pos[box.id] = (box.x, box.y)
                        else:
                            # 如果只有一个选中的箱子，取消选中
                            self.selected_box_ids.remove(clicked_box_id)
                            self.selected_box_id = None
                    else:
                        # 如果点击的是未选中的箱子，添加到选中列表
                        self.selected_box_ids.append(clicked_box_id)
                        self.selected_box_id = clicked_box_id

                        # 准备拖动
                        self.dragging = True
                        self.drag_start_pos = event.position()

                        # 记录所有选中箱子的初始位置
                        self.drag_boxes_start_pos = {}
                        for box in self.pattern.boxes:
                            if box.id in self.selected_box_ids:
                                self.drag_boxes_start_pos[box.id] = (box.x, box.y)

                # 发送多选信号
                self.boxesSelected.emit(self.selected_box_ids)
            else:
                # 单选模式
                self.selected_box_id = clicked_box_id
                self.selected_box_ids = [clicked_box_id]  # 同时更新多选列表
                self.boxSelected.emit(clicked_box_id)
                
                # 如果是左键点击，准备拖动
                if event.button() == Qt.LeftButton:
                    self.dragging = True
                    self.drag_start_pos = event.position()

                    # 记录箱子初始位置
                    for box in self.pattern.boxes:
                        if box.id == clicked_box_id:
                            self.drag_box_start_pos = (box.x, box.y)
                            break
        else:
            # 点击空白区域，清除选择
            self.selected_box_id = None
            self.selected_box_ids = []
            self.boxSelected.emit("")
            self.boxesSelected.emit([])
        
        self.update()
        
    def mouseMoveEvent(self, event):
        """处理鼠标移动事件"""
        if self.dragging and self.drag_start_pos:
            # 使用新的显示参数计算方法
            params = self.calculate_display_params()
            scale = params['scale']
            display_x = params['display_x']
            display_y = params['display_y']
            display_width = params['display_width']
            display_height = params['display_height']

            # 计算当前鼠标位置
            current_mouse_x = event.position().x()
            current_mouse_y = event.position().y()

            # 检查鼠标是否在显示区域边界内，如果超出则不允许移动
            if (current_mouse_x < display_x or current_mouse_x > display_x + display_width or
                current_mouse_y < display_y or current_mouse_y > display_y + display_height):
                # 鼠标超出显示区域边界，不执行移动
                return

            # 计算拖动距离
            dx = current_mouse_x - self.drag_start_pos.x()
            dy = current_mouse_y - self.drag_start_pos.y()

            # 计算实际移动距离（考虑缩放）
            real_dx = dx / scale
            real_dy = dy / scale

            if self.multi_select_mode and self.selected_box_ids and hasattr(self, 'drag_boxes_start_pos'):
                # 多选模式：作为整体移动所有选中的箱子
                new_positions = {}
                can_move_all = True

                for box_id in self.selected_box_ids:
                    if box_id in self.drag_boxes_start_pos:
                        box = self.pattern.get_box_by_id(box_id)
                        if box:
                            start_x, start_y = self.drag_boxes_start_pos[box_id]
                            new_x = start_x + real_dx
                            new_y = start_y + real_dy

                            # 检查新位置是否会导致箱子超出控件边界
                            screen_x = display_x + new_x * scale
                            screen_y = display_y + new_y * scale
                            screen_width = box.width * scale
                            screen_height = box.length * scale

                            # 如果任何箱子会超出显示区域边界，则不允许移动
                            if (screen_x < display_x or screen_y < display_y or
                                screen_x + screen_width > display_x + display_width or
                                screen_y + screen_height > display_y + display_height):
                                can_move_all = False
                                break

                            new_positions[box_id] = (new_x, new_y)

                # 只有当所有箱子都不会超出边界时才执行移动
                if can_move_all:
                    # 执行移动
                    for box_id, (new_x, new_y) in new_positions.items():
                        box = self.pattern.get_box_by_id(box_id)
                        if box:
                            box.x = new_x
                            box.y = new_y

                    # 发送箱子移动信号（发送第一个选中箱子的ID作为代表）
                    if self.selected_box_ids:
                        first_box = self.pattern.get_box_by_id(self.selected_box_ids[0])
                        if first_box:
                            self.boxMoved.emit(self.selected_box_ids[0], first_box.x, first_box.y)
                    self.update()
            elif self.selected_box_id and hasattr(self, 'drag_box_start_pos'):
                # 单选模式：移动单个箱子
                new_x = self.drag_box_start_pos[0] + real_dx
                new_y = self.drag_box_start_pos[1] + real_dy

                # 获取箱子对象以获取尺寸信息
                box = self.pattern.get_box_by_id(self.selected_box_id)
                if box:
                    # 检查新位置是否会导致箱子超出控件边界
                    screen_x = display_x + new_x * scale
                    screen_y = display_y + new_y * scale
                    screen_width = box.width * scale
                    screen_height = box.length * scale

                    # 如果箱子会超出显示区域边界，则不允许移动
                    if (screen_x < display_x or screen_y < display_y or
                        screen_x + screen_width > display_x + display_width or
                        screen_y + screen_height > display_y + display_height):
                        # 超出显示区域边界，不执行移动
                        return

                    # 在边界内，执行移动
                    box.x = new_x
                    box.y = new_y

                    # 发送箱子移动信号
                    self.boxMoved.emit(self.selected_box_id, new_x, new_y)
                    self.update()
    
    def mouseReleaseEvent(self, event):
        """处理鼠标释放事件"""
        if self.dragging:
            if self.multi_select_mode and self.selected_box_ids:
                # 多选模式：处理所有选中箱子的堆叠
                for box_id in self.selected_box_ids:
                    self.handle_box_stacking_for_box(box_id)
            elif self.selected_box_id:
                # 单选模式：处理单个箱子的堆叠
                self.handle_box_stacking()

        self.dragging = False
        self.drag_start_pos = None
        self.drag_box_start_pos = None
        if hasattr(self, 'drag_boxes_start_pos'):
            self.drag_boxes_start_pos = None

    def handle_box_stacking(self):
        """处理箱子堆叠逻辑"""
        if not self.pattern or not self.selected_box_id:
            return

        # 获取被移动的箱子
        moved_box = None
        for box in self.pattern.boxes:
            if box.id == self.selected_box_id:
                moved_box = box
                break

        if not moved_box:
            return

        # 查找与移动箱子重叠的其他箱子
        overlapping_boxes = []
        for box in self.pattern.boxes:
            if box.id != self.selected_box_id and moved_box.intersects(box):
                overlapping_boxes.append(box)

        if overlapping_boxes:
            # 如果有重叠，将移动的箱子放到最上方（列表末尾）
            # 首先从列表中移除移动的箱子
            self.pattern.boxes = [box for box in self.pattern.boxes if box.id != self.selected_box_id]
            # 然后将其添加到列表末尾（最上方）
            self.pattern.boxes.append(moved_box)

            # 更新显示
            self.update()

    def handle_box_stacking_for_box(self, box_id):
        """处理指定箱子的堆叠逻辑"""
        if not self.pattern or not box_id:
            return

        # 获取指定的箱子
        target_box = None
        for box in self.pattern.boxes:
            if box.id == box_id:
                target_box = box
                break

        if not target_box:
            return

        # 查找与目标箱子重叠的其他箱子
        overlapping_boxes = []
        for box in self.pattern.boxes:
            if box.id != box_id and target_box.intersects(box):
                overlapping_boxes.append(box)

        if overlapping_boxes:
            # 如果有重叠，将目标箱子放到最上方（列表末尾）
            # 首先从列表中移除目标箱子
            self.pattern.boxes = [box for box in self.pattern.boxes if box.id != box_id]
            # 然后将其添加到列表末尾（最上方）
            self.pattern.boxes.append(target_box)

            # 更新显示
            self.update()


class PalletVisualization_Plan(QWidget):
    """托盘可视化组件 - 仅用于显示，不支持拖动"""
    
    boxSelected = Signal(str)  # 箱子选中信号，传递箱子ID
    boxesSelected = Signal(list)  # 多个箱子选中信号，传递箱子ID列表
    boxMoved = Signal(str, float, float)  # 箱子移动信号，传递箱子ID和新位置
    boxNumberChanged = Signal(str, str)  # 箱子编号变更信号，传递旧ID和新ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        # 初始化数据
        self.pattern = None
        self.selected_box_id = None
        self.selected_box_ids = []  # 存储多选的箱子ID
        self.multi_select_mode = False  # 多选模式标志
        
        # 设置最小大小
        self.setMinimumSize(391, 431)
        
        self.box_color_top = QColor(100, 150, 220)      # 浅蓝色顶部
        self.box_color_bottom = QColor(70, 120, 200)    # 浅蓝色底部
        self.selected_box_color_top = QColor(40, 80, 150)  # 深蓝色顶部
        self.selected_box_color_bottom = QColor(20, 60, 120)  # 深蓝色底部
        self.multi_selected_box_color_top = QColor(220, 100, 100)  # 红色顶部
        self.multi_selected_box_color_bottom = QColor(180, 60, 60)  # 红色底部

        # 样式设置
        self.show_grid = False
        self.show_rotation_mark = False  # 不显示对角线标记
        self.show_watermark = True
        self.box_corner_radius = 15
        
        # 设置焦点策略，使组件可以接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 设置鼠标追踪，以便能够实时获取鼠标位置
        self.setMouseTracking(True)
    
    def set_pattern(self, pattern):
        """设置托盘模式"""
        self.pattern = pattern
        self.selected_box_id = None
        self.selected_box_ids = []
        self.update()

    def calculate_display_params(self):
        """计算显示参数，使用整个控件区域但留出安全边距"""
        width = self.width()
        height = self.height()

        # 留出小的安全边距，确保箱子不会绘制到控件边界外
        margin = 2
        display_x = margin
        display_y = margin
        display_width = width - 2 * margin
        display_height = height - 2 * margin

        # 确保显示区域为正数
        display_width = max(10, display_width)
        display_height = max(10, display_height)

        # 确保托盘尺寸有效，用于计算缩放比例
        pallet_width = max(1, self.pattern.pallet_width) if self.pattern.pallet_width > 0 else 1200
        pallet_length = max(1, self.pattern.pallet_length) if self.pattern.pallet_length > 0 else 1200

        # 计算缩放比例，使托盘内容适应显示区域
        scale_x = display_width / pallet_width
        scale_y = display_height / pallet_length

        # 使用较小的缩放比例，保持比例
        scale = min(scale_x, scale_y)

        # 限制缩放比例的范围
        scale = max(0.01, min(scale, 10.0))

        return {
            'scale': scale,
            'display_x': display_x,
            'display_y': display_y,
            'display_width': display_width,
            'display_height': display_height,
            'pallet_width': pallet_width,
            'pallet_length': pallet_length
        }
    
    def set_multi_select_mode(self, enabled):
        """设置多选模式"""
        self.multi_select_mode = enabled
        if not enabled:
            self.selected_box_ids = []
            if self.selected_box_id:
                self.selected_box_ids = [self.selected_box_id]
        else:
            # 进入多选模式时，如果有单选的箱子，将其添加到多选列表中
            if self.selected_box_id and self.selected_box_id not in self.selected_box_ids:
                self.selected_box_ids = [self.selected_box_id]
        self.update()
    
    def paintEvent(self, event):
        """绘制托盘可视化"""
        if not self.pattern:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 获取控件尺寸
        width = self.width()
        height = self.height()

        # 绘制背景 - 浅蓝色背景
        painter.fillRect(0, 0, width, height, QColor(240, 245, 255))

        # 使用新的显示参数计算方法
        params = self.calculate_display_params()
        scale = params['scale']
        display_x = params['display_x']
        display_y = params['display_y']
        display_width = params['display_width']
        display_height = params['display_height']

        
        # 设置裁剪区域，确保绘制不超出控件边界
        painter.setClipRect(0, 0, width, height)

        # 绘制箱子
        if self.pattern.boxes:
            # 绘制每个箱子
            for box in self.pattern.boxes:
                # 计算箱子在视图中的实际位置
                box_x = display_x + box.x * scale
                box_y = display_y + box.y * scale
                box_width = box.width * scale
                box_height = box.length * scale

                # 严格检查箱子是否完全在显示区域内
                if (box_x < display_x or box_y < display_y or
                    box_x + box_width > display_x + display_width or
                    box_y + box_height > display_y + display_height):
                    # 箱子超出显示区域边界，不绘制
                    continue

                # 创建渐变色
                gradient = QLinearGradient(box_x, box_y, box_x, box_y + box_height)

                # 根据选中状态设置颜色
                if self.multi_select_mode and box.id in self.selected_box_ids:
                    # 多选模式下被选中的箱子
                    gradient.setColorAt(0, self.multi_selected_box_color_top)
                    gradient.setColorAt(1, self.multi_selected_box_color_bottom)
                    painter.setPen(Qt.NoPen)
                elif box.id == self.selected_box_id:
                    # 单选模式下被选中的箱子
                    gradient.setColorAt(0, self.selected_box_color_top)
                    gradient.setColorAt(1, self.selected_box_color_bottom)
                    painter.setPen(Qt.NoPen)
                else:
                    # 未选中的箱子
                    gradient.setColorAt(0, self.box_color_top)
                    gradient.setColorAt(1, self.box_color_bottom)
                    painter.setPen(Qt.NoPen)

                painter.setBrush(QBrush(gradient))

                # 绘制箱子
                # 使用圆角矩形，使箱子外观更加美观
                corner_radius = min(box_width, box_height) * 0.1  # 圆角半径随箱子大小缩放
                path = QPainterPath()
                path.addRoundedRect(
                    QRectF(box_x, box_y, box_width, box_height),
                    corner_radius, corner_radius
                )
                painter.drawPath(path)
                
                # 绘制箱子编号（根据旋转状态调整文字方向）
                font = QFont()
                font.setBold(True)
                # 根据箱子大小调整字体大小
                font_size = min(box_width, box_height) / 3
                font.setPointSize(max(6, int(font_size)))  # 确保字体大小至少为6
                painter.setFont(font)
                painter.setPen(QPen(QColor(255, 255, 255)))

                # 获取箱子编号
                box_number = ""
                if isinstance(box.id, str) and box.id.startswith("box_"):
                    box_number = box.id.split("_")[1]
                else:
                    box_number = str(box.id)

                # 保存当前变换状态
                painter.save()

                # 根据旋转角度旋转文字
                if hasattr(box, 'rotation_angle') and box.rotation_angle != 0:
                    # 计算箱子中心点
                    center_x = box_x + box_width / 2
                    center_y = box_y + box_height / 2

                    # 移动到箱子中心，旋转指定角度，再移回
                    painter.translate(center_x, center_y)
                    painter.rotate(box.rotation_angle)
                    painter.translate(-center_x, -center_y)

                # 显示箱子编号
                painter.drawText(
                    int(box_x), int(box_y),
                    int(box_width), int(box_height),
                    Qt.AlignCenter,
                    str(box_number)
                )

                # 恢复变换状态
                painter.restore()
    
    def mousePressEvent(self, event):
        """处理鼠标按下事件 - 仅支持选择，不支持拖动"""
        if not self.pattern or not self.pattern.boxes:
            return
            
        # 使用新的显示参数计算方法
        params = self.calculate_display_params()
        scale = params['scale']
        display_x = params['display_x']
        display_y = params['display_y']
        
        # 检查点击是否在箱子上
        # 从列表末尾开始遍历，优先选中最上方（最后绘制）的箱子
        clicked_box_id = None
        for box in reversed(self.pattern.boxes):
            # 计算箱子在视图中的实际位置
            box_x = display_x + box.x * scale
            box_y = display_y + box.y * scale
            box_width = box.width * scale
            box_height = box.length * scale

            # 检查点击是否在箱子上
            box_rect = QRectF(box_x, box_y, box_width, box_height)
            if box_rect.contains(event.position()):
                clicked_box_id = box.id
                break  # 找到第一个（最上方的）匹配箱子就停止
        
        # 处理箱子选择
        if clicked_box_id:
            if self.multi_select_mode:
                # 多选模式
                if clicked_box_id in self.selected_box_ids:
                    # 如果已经选中，则取消选中
                    self.selected_box_ids.remove(clicked_box_id)
                    # 如果这是当前选中的箱子，也清除单选状态
                    if self.selected_box_id == clicked_box_id:
                        self.selected_box_id = None
                else:
                    # 如果未选中，则添加到选中列表
                    self.selected_box_ids.append(clicked_box_id)
                    # 同时更新单选状态
                    self.selected_box_id = clicked_box_id
                
                # 发送多选信号
                self.boxesSelected.emit(self.selected_box_ids)
            else:
                # 单选模式
                self.selected_box_id = clicked_box_id
                self.selected_box_ids = [clicked_box_id]  # 同时更新多选列表
                self.boxSelected.emit(clicked_box_id)
        else:
            # 点击空白区域，清除选择
            self.selected_box_id = None
            self.selected_box_ids = []
            self.boxSelected.emit("")
            self.boxesSelected.emit([])
        
        self.update()

    def handle_box_stacking(self, box_id):
        """处理箱子堆叠逻辑（用于外部调用）"""
        if not self.pattern or not box_id:
            return

        # 获取指定的箱子
        target_box = None
        for box in self.pattern.boxes:
            if box.id == box_id:
                target_box = box
                break

        if not target_box:
            return

        # 查找与目标箱子重叠的其他箱子
        overlapping_boxes = []
        for box in self.pattern.boxes:
            if box.id != box_id and target_box.intersects(box):
                overlapping_boxes.append(box)

        if overlapping_boxes:
            # 如果有重叠，将目标箱子放到最上方（列表末尾）
            # 首先从列表中移除目标箱子
            self.pattern.boxes = [box for box in self.pattern.boxes if box.id != box_id]
            # 然后将其添加到列表末尾（最上方）
            self.pattern.boxes.append(target_box)

            # 更新显示
            self.update()
