import numpy as np
from numpy.linalg import norm
import math

def compute_circle_center_3d(p1, p2, p3):
    """计算三个点确定的空间圆的圆心和半径"""
    p1, p2, p3 = np.array(p1), np.array(p2), np.array(p3)
    v1, v2 = p2 - p1, p3 - p1
    normal = np.cross(v1, v2)

    if norm(normal) < 1e-10:
        print("三点共线，无法确定圆")
        return None, None

    # 建立方程组求解圆心
    A = np.array([
        [2 * (p2[0] - p1[0]), 2 * (p2[1] - p1[1]), 2 * (p2[2] - p1[2])],
        [2 * (p3[0] - p1[0]), 2 * (p3[1] - p1[1]), 2 * (p3[2] - p1[2])],
        normal
    ])
    B = np.array([
        norm(p2) ** 2 - norm(p1) ** 2,
        norm(p3) ** 2 - norm(p1) ** 2,
        np.dot(normal, p1)
    ])

    try:
        center = np.linalg.solve(A, B)
    except:
        print("求解失败")
        return None, None

    radius = norm(p1 - center)
    return center, radius, normal

def get_intermediate_point(C, P1, P0, P2, n, t):
    # 计算半径向量和弧长
    P_mid = slerp_interpolation(C, P1, P2, n, t)
    OA = P0 - C
    OB = P_mid - C
    OC = P2 - C
    r = np.linalg.norm(OA)
    cos_BC = np.dot(OB, OC) / (np.linalg.norm(OB) * np.linalg.norm(OC))
    cos_AC = np.dot(OA, OC) / (np.linalg.norm(OA) * np.linalg.norm(OC))
    arc_BC = r * math.acos(cos_BC)
    arc_AC = r * math.acos(cos_AC)

    return P_mid, arc_BC, arc_AC

def slerp_interpolation(C, P1, P2, n, t):
    """
    球面线性插值（Slerp）计算空间圆上的中间点。

    参数:
        C (np.array): 圆心坐标 [Cx, Cy, Cz]。
        P1 (np.array): 圆上起点 [P1x, P1y, P1z]。
        P2 (np.array): 圆上终点 [P2x, P2y, P2z]。
        n (np.array): 圆的法向量 [nx, ny, nz]（需归一化）。
        t (float): 插值参数，范围 [0, 1]。

    返回:
        np.array: 中间点坐标 [Px, Py, Pz]。
    """
    # 计算圆心到两点的向量
    v1 = P1 - C
    v2 = P2 - C
    R = np.linalg.norm(v1)  # 圆的半径
    # 归一化向量
    v1_unit = v1 / R
    v2_unit = v2 / R

    # 计算两向量的夹角
    dot = np.clip(np.dot(v1_unit, v2_unit), -1.0, 1.0)  # 避免数值误差
    theta = np.arccos(dot)

    # 处理共线情况（theta接近0或π）
    if np.sin(theta) < 1e-10:
        return C + (1 - t) * v1 + t * v2

    # Slerp公式
    w1 = np.sin((1 - t) * theta) / np.sin(theta)
    w2 = np.sin(t * theta) / np.sin(theta)
    v_interp = w1 * v1_unit + w2 * v2_unit

    # 返回插值点（保持原半径）
    return C + R * v_interp

def get_rotation_angle(center, P1, P2, P3, theta_A, theta_C):
    # 将角度从度转换为弧度
    theta_A = tuple(math.radians(theta) for theta in theta_A)
    theta_C = tuple(math.radians(theta) for theta in theta_C)
    # 计算半径向量
    OA = P1 - center
    OB = P2 - center
    OC = P3 - center
    # 计算弧长
    r = np.linalg.norm(OA)
    cos_AB = np.dot(OA, OB) / (np.linalg.norm(OA) * np.linalg.norm(OB))
    cos_AC = np.dot(OA, OC) / (np.linalg.norm(OA) * np.linalg.norm(OC))
    arc_AB = r * math.acos(cos_AB)
    arc_AC = r * math.acos(cos_AC)
    # 计算点A和点C的旋转矩阵
    R_A = rotation_matrix_z(theta_A[2]) @ rotation_matrix_y(theta_A[1]) @ rotation_matrix_x(theta_A[0])
    R_C = rotation_matrix_z(theta_C[2]) @ rotation_matrix_y(theta_C[1]) @ rotation_matrix_x(theta_C[0])
    # 计算旋转角变化量
    delta_theta_x = (arc_AB / arc_AC) * (theta_C[0] - theta_A[0])
    delta_theta_y = (arc_AB / arc_AC) * (theta_C[1] - theta_A[1])
    delta_theta_z = (arc_AB / arc_AC) * (theta_C[2] - theta_A[2])
    # 计算旋转角变化量对应的旋转矩阵
    delta_R = rotation_matrix_z(delta_theta_z) @ rotation_matrix_y(delta_theta_y) @ rotation_matrix_x(delta_theta_x)
    # 计算中间点B的旋转矩阵
    R_B = R_A @ delta_R
    # 提取中间点B的旋转角
    theta_Bx, theta_By, theta_Bz = euler_angles_from_rotation_matrix(R_B)
    # 将弧度转换为角度
    theta_Bx = math.degrees(theta_Bx)
    theta_By = math.degrees(theta_By)
    theta_Bz = math.degrees(theta_Bz)
    return theta_Bx, theta_By, theta_Bz

def rotation_matrix_z(theta):
    return np.array([
        [math.cos(theta), -math.sin(theta), 0],
        [math.sin(theta), math.cos(theta), 0],
        [0, 0, 1]
    ])

def rotation_matrix_y(theta):
    return np.array([
        [math.cos(theta), 0, math.sin(theta)],
        [0, 1, 0],
        [-math.sin(theta), 0, math.cos(theta)]
    ])

def rotation_matrix_x(theta):
    return np.array([
        [1, 0, 0],
        [0, math.cos(theta), -math.sin(theta)],
        [0, math.sin(theta), math.cos(theta)]
    ])

def euler_angles_from_rotation_matrix(R):
    # 计算旋转矩阵的奇异值判断条件
    sy = math.sqrt(R[0, 0] * R[0, 0] + R[1, 0] * R[1, 0])
    singular = sy < 1e-6
    if not singular:  # 非奇异情况下的欧拉角计算
        x = math.atan2(R[2, 1] / sy, R[2, 2] / sy)
        y = math.atan2(-R[2, 0], sy)
        z = math.atan2(R[1, 0] / sy, R[0, 0] / sy)
    else:   # 奇异情况下的欧拉角计算
        x = math.atan2(-R[1, 2], R[1, 1])
        y = math.atan2(-R[2, 0], sy)
        z = 0
    return x, y, z

def closest_point(P, C, point1, point2, point3):
    """
    通过圆上三点计算最近点

    参数:
        P (numpy.ndarray): 空间点坐标
        C (numpy.ndarray): 圆心坐标
        point1, point2, point3 (numpy.ndarray): 圆上三点

    返回:
        numpy.ndarray: 最近点坐标
    """
    # 计算半径
    point1, point2, point3 = np.array(point1), np.array(point2), np.array(point3)
    P = np.array(P)
    radius = np.linalg.norm(point1 - C)
    # 计算法向量
    v1 = point2 - point1
    v2 = point3 - point1
    normal = np.cross(v1, v2)
    normal = normal / np.linalg.norm(normal)
    point = closest_point_on_circle(P, C, normal, radius)
    return point

def closest_point_on_circle(P, C, normal, radius):
    # 归一化法向量
    normal = normal / np.linalg.norm(normal)
    # 计算点到平面的距离
    P_minus_C = P - C
    distance = np.dot(P_minus_C, normal)
    P_prime = P - distance * normal
    # 计算向量CP'
    CP_prime = P_prime - C
    CP_prime_norm = np.linalg.norm(CP_prime)
    if CP_prime_norm < 1e-10:
        if abs(normal[0]) > 0.1 or abs(normal[1]) > 0.1:
            u = np.array([-normal[1], normal[0], 0])
        else:
            u = np.array([1, 0, 0])
        u = u / np.linalg.norm(u)
        Q = C + radius * u
    else:
        Q = C + radius * (CP_prime / CP_prime_norm)
    return Q

