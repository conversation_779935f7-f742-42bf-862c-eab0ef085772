<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="zh_CN">
<context>
    <name></name>
    <message>
        <source>None</source>
        <translation>无</translation>
    </message>
</context>
<context>
    <name>Alarm</name>
    <message>
        <source>警告</source>
        <translation>警告</translation>
    </message>
</context>
<context>
    <name>AlarmInfoForm</name>
    <message>
        <source>报警信息</source>
        <translation>报警信息</translation>
    </message>
    <message>
        <source>清除错误</source>
        <translation>清除错误</translation>
    </message>
    <message>
        <source>导出日志</source>
        <translation>导出日志</translation>
    </message>
    <message>
        <source>序号</source>
        <translation>序号</translation>
    </message>
    <message>
        <source>日期</source>
        <translation>日期</translation>
    </message>
    <message>
        <source>报警值</source>
        <translation>报警值</translation>
    </message>
    <message>
        <source>报警描述</source>
        <translation>报警描述</translation>
    </message>
    <message>
        <source>状态</source>
        <translation>状态</translation>
    </message>
    <message>
        <source>通用设置</source>
        <translation>通用设置</translation>
    </message>
    <message>
        <source>机器人设置</source>
        <translation>机器人设置</translation>
    </message>
    <message>
        <source>机器人碰撞等级设置</source>
        <translation>机器人碰撞等级设置</translation>
    </message>
    <message>
        <source>应用</source>
        <translation>应用</translation>
    </message>
    <message>
        <source>碰撞等级：</source>
        <translation>碰撞等级：</translation>
    </message>
    <message>
        <source>标准等级</source>
        <translation>标准等级</translation>
    </message>
    <message>
        <source>自定义百分比</source>
        <translation>自定义百分比</translation>
    </message>
    <message>
        <source>机器人应用设置</source>
        <translation>机器人应用设置</translation>
    </message>
    <message>
        <source>       全局速度：</source>
        <translation>       全局速度：</translation>
    </message>
    <message>
        <source>末端负载重量：</source>
        <translation>末端负载重量：</translation>
    </message>
    <message>
        <source>mm/s</source>
        <translation>mm/s</translation>
    </message>
    <message>
        <source>%</source>
        <translation>%</translation>
    </message>
    <message>
        <source>&lt;--100%速度时              </source>
        <translation>&lt;--100%速度时   </translation>
    </message>
    <message>
        <source>KG</source>
        <translation>KG</translation>
    </message>
    <message>
        <source>       偏移补偿:</source>
        <translation>       偏移补偿:</translation>
    </message>
    <message>
        <source>   X:</source>
        <translation>   X:</translation>
    </message>
    <message>
        <source>    Y:</source>
        <translation>    Y:</translation>
    </message>
    <message>
        <source>    Z:</source>
        <translation>    Z:</translation>
    </message>
    <message>
        <source>(单位：mm)</source>
        <translation>(单位：mm)</translation>
    </message>
    <message>
        <source>                       RX:</source>
        <translation>                       RX:</translation>
    </message>
    <message>
        <source>  RY:</source>
        <translation>  RY:</translation>
    </message>
    <message>
        <source>  RZ:</source>
        <translation>  RZ:</translation>
    </message>
    <message>
        <source>(单位：°)</source>
        <translation>(单位：°)</translation>
    </message>
    <message>
        <source>焊机设置</source>
        <translation>焊机设置</translation>
    </message>
    <message>
        <source>点焊延迟：</source>
        <translation>点焊延迟：</translation>
    </message>
    <message>
        <source>S</source>
        <translation>S</translation>
    </message>
    <message>
        <source>电流电压控制：</source>
        <translation>电流电压控制：</translation>
    </message>
    <message>
        <source>机器人控制</source>
        <translation>机器人控制</translation>
    </message>
    <message>
        <source>焊机控制</source>
        <translation>焊机控制</translation>
    </message>
    <message>
        <source>起弧延迟：</source>
        <translation>起弧延迟:</translation>
    </message>
    <message>
        <source>收弧延迟：</source>
        <translation>收弧延迟:</translation>
    </message>
    <message>
        <source>系统设置</source>
        <translation>系统设置</translation>
    </message>
    <message>
        <source>语言设置：</source>
        <translation>语言设置：</translation>
    </message>
    <message>
        <source>中文</source>
        <translation>中文</translation>
    </message>
    <message>
        <source>English</source>
        <translation>English</translation>
    </message>
    <message>
        <source>机器人IP：</source>
        <translation>机器人IP：</translation>
    </message>
</context>
<context>
    <name>Buttons</name>
    <message>
        <location filename="../logic/home_logic.py" line="1975"/>
        <source>暂停</source>
        <translation>暂停</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1977"/>
        <source>启动</source>
        <translation>启动</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1978"/>
        <source>模拟运行</source>
        <translation>模拟运行</translation>
    </message>
</context>
<context>
    <name>CommandEditFailure</name>
    <message>
        <source>指令编辑后程序序列无效：
{reason}</source>
        <translation>指令编辑后程序序列无效：
{reason}</translation>
    </message>
</context>
<context>
    <name>CommandValidation</name>
    <message>
        <location filename="../logic/home_logic.py" line="538"/>
        <source>请为指令选择一个类型。</source>
        <translation>请为指令选择一个类型。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="555"/>
        <source>速度不可为负数</source>
        <translation>速度不可为负数</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="575"/>
        <source>电流不可为负数</source>
        <translation>电流不可为负数</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="587"/>
        <source>电压不可为负数</source>
        <translation>电压不可为负数</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="624"/>
        <source>段焊焊接、非焊接长度应大于40mm</source>
        <translation>段焊焊接、非焊接长度应大于40mm</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="642"/>
        <source>速度、电流、电压、长度必须是有效的数字。</source>
        <translation>速度、电流、电压、长度必须是有效的数字。</translation>
    </message>
</context>
<context>
    <name>ConnectionStatus</name>
    <message>
        <source>已连接...</source>
        <translation>已连接</translation>
    </message>
    <message>
        <source>连接中...</source>
        <translation>连接中</translation>
    </message>
</context>
<context>
    <name>Delet</name>
    <message>
        <location filename="../logic/home_logic.py" line="2334"/>
        <source>删除失败</source>
        <translation>删除失败</translation>
    </message>
    <message>
        <source>删除成功</source>
        <translation>删除成功</translation>
    </message>
</context>
<context>
    <name>Delete</name>
    <message>
        <location filename="../logic/home_logic.py" line="2384"/>
        <source>删除成功</source>
        <translation>删除成功</translation>
    </message>
</context>
<context>
    <name>Dialog</name>
    <message>
        <source>工艺参数编辑</source>
        <translation>工艺参数编辑</translation>
    </message>
    <message>
        <source>工艺名称</source>
        <translation>工艺名称</translation>
    </message>
    <message>
        <source>焊接参数</source>
        <translation>焊接参数</translation>
    </message>
    <message>
        <source>电流:</source>
        <translation>电流:</translation>
    </message>
    <message>
        <source>A</source>
        <translation>A</translation>
    </message>
    <message>
        <source>电压:</source>
        <translation>电压:</translation>
    </message>
    <message>
        <source>V</source>
        <translation>V</translation>
    </message>
    <message>
        <source>速度:</source>
        <translation>速度:</translation>
    </message>
    <message>
        <source>mm/s</source>
        <translation>mm/s</translation>
    </message>
    <message>
        <source>摆动参数</source>
        <translation>摆动参数</translation>
    </message>
    <message>
        <source>摆动类型:</source>
        <translation>摆动类型:</translation>
    </message>
    <message>
        <source>三角波摆动</source>
        <translation>三角波摆动</translation>
    </message>
    <message>
        <source>垂直L型三角波</source>
        <translation>垂直L型三角波</translation>
    </message>
    <message>
        <source>圆型（顺时针）</source>
        <translation>圆型（顺时针）</translation>
    </message>
    <message>
        <source>圆型（逆时针）</source>
        <translation>圆型（逆时针）</translation>
    </message>
    <message>
        <source>正弦波摆动</source>
        <translation>正弦波摆动</translation>
    </message>
    <message>
        <source>垂直L型正弦波</source>
        <translation>垂直L型正弦波</translation>
    </message>
    <message>
        <source>立焊三角形摆动</source>
        <translation>立焊三角形摆动</translation>
    </message>
    <message>
        <source>周期时间:</source>
        <translation>周期时间:</translation>
    </message>
    <message>
        <source>周期不包含等待时间</source>
        <translation>周期不包含等待时间</translation>
    </message>
    <message>
        <source>周期包含等待时间</source>
        <translation>周期包含等待时间</translation>
    </message>
    <message>
        <source>等待控制:</source>
        <translation>等待控制:</translation>
    </message>
    <message>
        <source>等待时间内继续移动</source>
        <translation>等待时间内继续移动</translation>
    </message>
    <message>
        <source>等待时间内静止</source>
        <translation>等待时间内静止</translation>
    </message>
    <message>
        <source>         频率:</source>
        <translation>         频率:</translation>
    </message>
    <message>
        <source>  Hz</source>
        <translation>  Hz</translation>
    </message>
    <message>
        <source>         幅度:</source>
        <translation>         幅度:</translation>
    </message>
    <message>
        <source>mm</source>
        <translation>mm</translation>
    </message>
    <message>
        <source>左停留时间:</source>
        <translation>左停留时间:</translation>
    </message>
    <message>
        <source>  ms</source>
        <translation>  ms</translation>
    </message>
    <message>
        <source>右停留时间:</source>
        <translation>右停留时间:</translation>
    </message>
    <message>
        <source>     s</source>
        <translation>     s</translation>
    </message>
    <message>
        <source>   起弧延迟:</source>
        <translation>   起弧延迟:</translation>
    </message>
    <message>
        <source>   收弧延迟:</source>
        <translation>   收弧延迟:</translation>
    </message>
    <message>
        <source>保存</source>
        <translation>保存</translation>
    </message>
</context>
<context>
    <name>EditValidation</name>
    <message>
        <location filename="../logic/home_logic.py" line="321"/>
        <source>请先选择要编辑的行</source>
        <translation>请先选择要编辑的行</translation>
    </message>
</context>
<context>
    <name>Error</name>
    <message>
        <source>错误</source>
        <translation>错误</translation>
    </message>
</context>
<context>
    <name>ErrorCode</name>
    <message>
        <source>无故障</source>
        <translation>无故障</translation>
    </message>
    <message>
        <source>驱动器故障</source>
        <translation>驱动器故障</translation>
    </message>
    <message>
        <source>超出软限位故障</source>
        <translation>超出软限位故障</translation>
    </message>
    <message>
        <source>碰撞故障</source>
        <translation>碰撞故障</translation>
    </message>
    <message>
        <source>奇异位姿</source>
        <translation>奇异位姿</translation>
    </message>
    <message>
        <source>从站错误</source>
        <translation>从站错误</translation>
    </message>
    <message>
        <source>指令点错误</source>
        <translation>指令点错误</translation>
    </message>
    <message>
        <source>IO错误</source>
        <translation>IO错误</translation>
    </message>
    <message>
        <source>夹爪错误</source>
        <translation>夹爪错误</translation>
    </message>
    <message>
        <source>文件错误</source>
        <translation>文件错误</translation>
    </message>
    <message>
        <source>参数错误</source>
        <translation>参数错误</translation>
    </message>
    <message>
        <source>扩展轴超出软限位错误</source>
        <translation>扩展轴超出软限位错误</translation>
    </message>
    <message>
        <source>关节配置警告</source>
        <translation>关节配置警告</translation>
    </message>
</context>
<context>
    <name>Form</name>
    <message>
        <source>点位编辑</source>
        <translation>点位编辑</translation>
    </message>
    <message>
        <source>指令类型:</source>
        <translation>指令类型:</translation>
    </message>
    <message>
        <source>起始安全点</source>
        <translation>起始安全点</translation>
    </message>
    <message>
        <source>起弧点</source>
        <translation>起弧点</translation>
    </message>
    <message>
        <source>直线</source>
        <translation>直线</translation>
    </message>
    <message>
        <source>圆弧中间点</source>
        <translation>圆弧中间点</translation>
    </message>
    <message>
        <source>圆弧末点</source>
        <translation>圆弧末点</translation>
    </message>
    <message>
        <source>终点安全点</source>
        <translation>终点安全点</translation>
    </message>
    <message>
        <source>工艺选择:</source>
        <translation>工艺选择:</translation>
    </message>
    <message>
        <source>速度:</source>
        <translation>速度:</translation>
    </message>
    <message>
        <source>mm/s</source>
        <translation>mm/s</translation>
    </message>
    <message>
        <source>电流:</source>
        <translation>电流:</translation>
    </message>
    <message>
        <source>A</source>
        <translation>A</translation>
    </message>
    <message>
        <source>电压:</source>
        <translation>电压:</translation>
    </message>
    <message>
        <source>V</source>
        <translation>V</translation>
    </message>
    <message>
        <source>摆动:</source>
        <translation>摆动:</translation>
    </message>
    <message>
        <source>是</source>
        <translation>是</translation>
    </message>
    <message>
        <source>否</source>
        <translation>否</translation>
    </message>
    <message>
        <source>段焊:</source>
        <translation>段焊</translation>
    </message>
    <message>
        <source>段焊焊接、非焊接长度应大于40mm</source>
        <translation>段焊焊接、非焊接长度应大于40mm</translation>
    </message>
    <message>
        <source>执行长度:</source>
        <translation>执行长度:</translation>
    </message>
    <message>
        <source>mm</source>
        <translation>mm</translation>
    </message>
    <message>
        <source>非执行长度:</source>
        <translation>非执行长度:</translation>
    </message>
    <message>
        <source>起弧:</source>
        <translation>起弧：</translation>
    </message>
    <message>
        <source>应用</source>
        <translation>应用</translation>
    </message>
    <message>
        <source>另存为工艺</source>
        <translation>另存为工艺</translation>
    </message>
    <message>
        <source>Form</source>
        <translation>Form</translation>
    </message>
    <message>
        <source>打开</source>
        <translation>打开</translation>
    </message>
    <message>
        <source>新建</source>
        <translation>新建</translation>
    </message>
    <message>
        <source>保存</source>
        <translation>保存</translation>
    </message>
    <message>
        <source>另存为</source>
        <translation>另存为</translation>
    </message>
    <message>
        <source>删除程序</source>
        <translation>删除程序</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;当前程序：&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;当前程序：&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>一键编程区</source>
        <translation>一键编程区</translation>
    </message>
    <message>
        <source>起始
安全点</source>
        <translation>起始安全点</translation>
    </message>
    <message>
        <source>圆弧
中间点</source>
        <translation>圆弧中间点</translation>
    </message>
    <message>
        <source>终点
安全点</source>
        <translation>终点安全点</translation>
    </message>
    <message>
        <source>行号</source>
        <translation>行号</translation>
    </message>
    <message>
        <source>指令类型</source>
        <translation>指令类型</translation>
    </message>
    <message>
        <source>点位</source>
        <translation>点位</translation>
    </message>
    <message>
        <source>速度</source>
        <translation>速度</translation>
    </message>
    <message>
        <source>编辑行</source>
        <translation>编辑行</translation>
    </message>
    <message>
        <source>删除行</source>
        <translation>删除行</translation>
    </message>
    <message>
        <source>上移</source>
        <translation>上移</translation>
    </message>
    <message>
        <source>下移</source>
        <translation>下移</translation>
    </message>
    <message>
        <source>重定位</source>
        <translation>重定位</translation>
    </message>
    <message>
        <source>移动</source>
        <translation>移动</translation>
    </message>
    <message>
        <source>实时点位</source>
        <translation>实时点位</translation>
    </message>
    <message>
        <source>Y:</source>
        <translation>Y:</translation>
    </message>
    <message>
        <source>RY:</source>
        <translation>RY:</translation>
    </message>
    <message>
        <source>Z:</source>
        <translation>Z:</translation>
    </message>
    <message>
        <source>RZ:</source>
        <translation>RZ:</translation>
    </message>
    <message>
        <source>X:</source>
        <translation>X:</translation>
    </message>
    <message>
        <source>RX:</source>
        <translation>RX:</translation>
    </message>
    <message>
        <source>模拟运行</source>
        <translation>模拟运行</translation>
    </message>
    <message>
        <source>  启  动  </source>
        <translation>  启  动  </translation>
    </message>
    <message>
        <source>停止</source>
        <translation>停止</translation>
    </message>
    <message>
        <source>暂停</source>
        <translation>暂停</translation>
    </message>
    <message>
        <source>设置</source>
        <translation>设置</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;智 能 焊 接&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;智 能 焊 接&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>主页</source>
        <translation>主页</translation>
    </message>
    <message>
        <source>工艺管理</source>
        <translation>工艺管理</translation>
    </message>
    <message>
        <source>通用设置</source>
        <translation>通用设置</translation>
    </message>
    <message>
        <source>报警管理</source>
        <translation>报警管理</translation>
    </message>
    <message>
        <source>手动控制</source>
        <translation>手动控制</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:12pt;&quot;&gt;一键编程区&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:12pt;&quot;&gt;一键编程区&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>X：</source>
        <translation>X：</translation>
    </message>
    <message>
        <source>RX：</source>
        <translation>RX：</translation>
    </message>
    <message>
        <source>RY：</source>
        <translation>RY：</translation>
    </message>
    <message>
        <source>RZ：</source>
        <translation>RZ：</translation>
    </message>
    <message>
        <source>z：</source>
        <translation>z：</translation>
    </message>
    <message>
        <source>Y：</source>
        <translation>Y：</translation>
    </message>
    <message>
        <source>111</source>
        <translation>111</translation>
    </message>
    <message>
        <source>121.02</source>
        <translation>121.02</translation>
    </message>
    <message>
        <source>23.00</source>
        <translation>23.00</translation>
    </message>
    <message>
        <source>1212</source>
        <translation>1212</translation>
    </message>
    <message>
        <source>23</source>
        <translation>23</translation>
    </message>
    <message>
        <source>56</source>
        <translation>56</translation>
    </message>
    <message>
        <source>PushButton</source>
        <translation>PushButton</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:14pt;&quot;&gt;当前程序:&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:14pt;&quot;&gt;当前程序:&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:14pt;&quot;&gt;工艺选择:&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:14pt;&quot;&gt;工艺选择:&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>工艺包1</source>
        <translation>工艺包1</translation>
    </message>
    <message>
        <source>工艺包2</source>
        <translation>工艺包2</translation>
    </message>
    <message>
        <source>工艺包3</source>
        <translation>工艺包3</translation>
    </message>
    <message>
        <source>工艺包4</source>
        <translation>工艺包4</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:18pt; font-weight:700;&quot;&gt;这是工艺管理&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:18pt; font-weight:700;&quot;&gt;这是工艺管理&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Microsoft YaHei UI&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:24pt;&quot;&gt;手动控制&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Microsoft YaHei UI&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:24pt;&quot;&gt;手动控制&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>机器人控制</source>
        <translation>机器人控制</translation>
    </message>
    <message>
        <source>30</source>
        <translation>30</translation>
    </message>
    <message>
        <source>%</source>
        <translation>%</translation>
    </message>
    <message>
        <source>单次长按阈值:</source>
        <translation>单次长按阈值:</translation>
    </message>
    <message>
        <source>J1:</source>
        <translation>J1:</translation>
    </message>
    <message>
        <source>-</source>
        <translation>-</translation>
    </message>
    <message>
        <source>+</source>
        <translation>+</translation>
    </message>
    <message>
        <source>J2:</source>
        <translation>J2:</translation>
    </message>
    <message>
        <source>J3:</source>
        <translation>J3:</translation>
    </message>
    <message>
        <source>J4:</source>
        <translation>J4:</translation>
    </message>
    <message>
        <source>J5:</source>
        <translation>J5:</translation>
    </message>
    <message>
        <source>J6:</source>
        <translation>J6:</translation>
    </message>
    <message>
        <source>焊机控制</source>
        <translation>焊机控制</translation>
    </message>
    <message>
        <source>起弧</source>
        <translation>起弧</translation>
    </message>
    <message>
        <source>收弧</source>
        <translation>收弧</translation>
    </message>
    <message>
        <source>送气</source>
        <translation>送气</translation>
    </message>
    <message>
        <source>关气</source>
        <translation>关气</translation>
    </message>
    <message>
        <source>正向送丝</source>
        <translation>正向送丝</translation>
    </message>
    <message>
        <source>反向送丝</source>
        <translation>反向送丝</translation>
    </message>
    <message>
        <source>点焊</source>
        <translation>点焊</translation>
    </message>
    <message>
        <source>安全点</source>
        <translation>安全点</translation>
    </message>
    <message>
        <source>J6：</source>
        <translation>J6：</translation>
    </message>
    <message>
        <source>J1：</source>
        <translation>J1：</translation>
    </message>
    <message>
        <source>J2：</source>
        <translation>J2：</translation>
    </message>
    <message>
        <source>J5：</source>
        <translation>J5：</translation>
    </message>
    <message>
        <source>J4：</source>
        <translation>J4：</translation>
    </message>
    <message>
        <source>J3：</source>
        <translation>J3：</translation>
    </message>
    <message>
        <source>1(25N)</source>
        <translation>1(25N)</translation>
    </message>
    <message>
        <source>2(33N)</source>
        <translation>2(33N)</translation>
    </message>
    <message>
        <source>3(41N)</source>
        <translation>3(41N)</translation>
    </message>
    <message>
        <source>4(50N)</source>
        <translation>4(50N)</translation>
    </message>
    <message>
        <source>5(58N)</source>
        <translation>5(58N)</translation>
    </message>
    <message>
        <source>6(66N)</source>
        <translation>6(66N)</translation>
    </message>
    <message>
        <source>7(75N)</source>
        <translation>7(75N)</translation>
    </message>
    <message>
        <source>8(83N)</source>
        <translation>8(83N)</translation>
    </message>
    <message>
        <source>9(91N)</source>
        <translation>9(91N)</translation>
    </message>
    <message>
        <source>10(100N)</source>
        <translation>10(100N)</translation>
    </message>
</context>
<context>
    <name>HomeLogic</name>
    <message>
        <location filename="../logic/home_logic.py" line="658"/>
        <source>编辑失败</source>
        <translation>编辑失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="659"/>
        <source>指令编辑后程序序列无效：
{reason}</source>
        <translation>指令编辑后程序序列无效：
{reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="708"/>
        <source>名称重复</source>
        <translation>名称重复</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="709"/>
        <source>工艺名称 &apos;{new_process_name}&apos; 已存在，请使用其他名称</source>
        <translation>工艺名称 &apos;{new_process_name}&apos; 已存在，请使用其他名称</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="753"/>
        <source>保存成功</source>
        <translation>保存成功</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="754"/>
        <source>工艺 &apos;{new_process_name}&apos; 已成功另存为。</source>
        <translation>工艺 &apos;{new_process_name}&apos; 已成功另存为。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="779"/>
        <location filename="../logic/home_logic.py" line="2140"/>
        <location filename="../logic/home_logic.py" line="2151"/>
        <location filename="../logic/home_logic.py" line="2213"/>
        <source>保存失败</source>
        <translation>保存失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="780"/>
        <source>另存为工艺时发生错误：{e}
操作已回滚。</source>
        <translation>另存为工艺时发生错误：{e}
操作已回滚。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="911"/>
        <location filename="../logic/home_logic.py" line="958"/>
        <location filename="../logic/home_logic.py" line="2408"/>
        <source>操作提示</source>
        <translation>操作提示</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="912"/>
        <source>请先创建或打开一个程序。</source>
        <translation>请先创建或打开一个程序。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="951"/>
        <source>已添加指令: {instruction_type}</source>
        <translation>已添加指令: {instruction_type}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="959"/>
        <source>请先创建或打开一个程序，才能自动添加指令。</source>
        <translation>请先创建或打开一个程序，才能自动添加指令。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="964"/>
        <source>尝试自动添加指令时，当前无打开的程序。</source>
        <translation>尝试自动添加指令时，当前无打开的程序。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="983"/>
        <location filename="../logic/home_logic.py" line="2420"/>
        <location filename="../logic/home_logic.py" line="2509"/>
        <source>操作受限</source>
        <translation>操作受限</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="984"/>
        <source>程序已以&apos;终点安全点&apos;结束，无法在末尾添加新指令。</source>
        <translation>程序已以&apos;终点安全点&apos;结束，无法在末尾添加新指令。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1035"/>
        <source>自动添加了指令: {msg_text} (类型: {target_instruction_type})</source>
        <translation>自动添加了指令: {msg_text} (类型: {target_instruction_type})</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1061"/>
        <location filename="../logic/home_logic.py" line="2395"/>
        <source>删除失败</source>
        <translation>删除失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1062"/>
        <source>请选择要删除的行。</source>
        <translation>请选择要删除的行。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1077"/>
        <source>Operation Restricted</source>
        <translation>操作受限</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1078"/>
        <source>Operation not allowed: {reason}</source>
        <translation>操作不被允许: {reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1104"/>
        <location filename="../logic/home_logic.py" line="1114"/>
        <location filename="../logic/home_logic.py" line="1176"/>
        <location filename="../logic/home_logic.py" line="1186"/>
        <location filename="../logic/home_logic.py" line="2497"/>
        <source>移动失败</source>
        <translation>移动失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1105"/>
        <location filename="../logic/home_logic.py" line="1177"/>
        <location filename="../logic/home_logic.py" line="2488"/>
        <source>请选择要移动的行。</source>
        <translation>请选择要移动的行。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1115"/>
        <location filename="../logic/home_logic.py" line="2498"/>
        <source>暂不支持多点移动，请选择单行。</source>
        <translation>暂不支持多点移动，请选择单行。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1165"/>
        <source>指令已从 {row_index} 上移到 {row_index - 1}</source>
        <translation>指令已从 {row_index} 上移到 {row_index - 1}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1187"/>
        <source>暂不支持多行移动，请选择单行。</source>
        <translation>暂不支持多行移动，请选择单行</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1226"/>
        <source>指令已从 {row_index} 跳跃下移到 {row_index + 2}，越过圆弧点对。</source>
        <translation>指令已从 {row_index} 跳跃下移到 {row_index + 2}，越过圆弧点对。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1236"/>
        <source>指令已从 {row_index} 下移到 {row_index + 1}</source>
        <translation>指令已从 {row_index} 下移到 {row_index + 1}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1247"/>
        <location filename="../logic/home_logic.py" line="1385"/>
        <source>程序中存在‘待指定类型’的指令，请先编辑并指定其具体类型。</source>
        <translation>程序中存在‘待指定类型’的指令，请先编辑并指定其具体类型。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1253"/>
        <source>列表为空时，第一条数据必须是起始安全点。</source>
        <translation>列表为空时，第一条数据必须是起始安全点。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1259"/>
        <source>列表不为空时，不能添加起始安全点。</source>
        <translation>列表不为空时，不能添加起始安全点。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1263"/>
        <source>上一条指令是&apos;{self.INSTR_END_SAFE}&apos;，不允许在其后添加任何指令。</source>
        <translation>上一条指令是&apos;{self.INSTR_END_SAFE}&apos;，不允许在其后添加任何指令。</translation>
    </message>
    <message>
        <source>上一条指令是 is &apos;{last_instruction_type}&apos;, 不允许在其后添加&apos;{instruction_type}允许的指令类型:{allowed_types}</source>
        <translation>上一条指令是 is &apos;{last_instruction_type}&apos;, 不允许在其后添加&apos;{instruction_type}允许的指令类型:{allowed_types}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1290"/>
        <source>行索引无效。</source>
        <translation>行索引无效。</translation>
    </message>
    <message>
        <source>删除该指令会破坏程序结构：{reason}</source>
        <translation>删除该指令会破坏程序结构：{reason}</translation>
    </message>
    <message>
        <source>删除该指令会破坏程序序列：{reason}</source>
        <translation>删除该指令会破坏程序序列：{reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1337"/>
        <source>无法移动到列表范围之外。</source>
        <translation>无法移动到列表范围之外。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1394"/>
        <source>程序的第一条指令必须是&apos;起始安全点&apos;。</source>
        <translation>程序的第一条指令必须是&apos;起始安全点&apos;。</translation>
    </message>
    <message>
        <source>No subsequent instructions allowed after &apos;%1&apos;</source>
        <translation>在“%1”之后不允许任何后续指令</translation>
    </message>
    <message>
        <source>, </source>
        <translation>, </translation>
    </message>
    <message>
        <source>Invalid instruction sequence: &apos;{current}&apos; cannot be followed by &apos;{next}&apos;. Allowed types: {allowed}</source>
        <translation>无效的指令序列：&apos;{current}&apos; 后面不能跟 &apos;{next}&apos;。允许的类型：{allowed}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1654"/>
        <source>没有可加载的程序指令。</source>
        <translation>没有可加载的程序指令</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1730"/>
        <source>解析点位数据失败：{record[&apos;point_position&apos;]} - 错误: {e}</source>
        <translation>解析点位数据失败：{record[&apos;point_position&apos;]} - 错误: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1731"/>
        <source>点位解析失败! (原始: {record[&apos;point_position&apos;]})</source>
        <translation>点位解析失败! (原始: {record[&apos;point_position&apos;]})</translation>
    </message>
    <message>
        <source>     工艺: {process_name};  </source>
        <translation>     工艺: {process_name};  </translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1299"/>
        <source>删除该指令会破坏程序结构：</source>
        <translation>删除该指令会破坏程序结构：</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1313"/>
        <source>删除该指令会破坏程序序列：{}</source>
        <translation>self.tr(&quot;删除该指令会破坏程序序列：{}&quot;).format(reason)</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1402"/>
        <source>程序中只能有一个&apos;起始安全点&apos;指令。</source>
        <translation>程序中只能有一个&apos;起始安全点&apos;指令。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1409"/>
        <source>The &apos;endpoint safety point&apos; instruction must be the last instruction in the program</source>
        <translation>“终点安全点”指令必须是程序中的最后一条指令</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1413"/>
        <source>程序中只能有一个&apos;终点安全点&apos;指令。</source>
        <translation>程序中只能有一个&apos;终点安全点&apos;指令。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1426"/>
        <source>No instructions allowed after &apos;{current_type}&apos;</source>
        <translation>&apos;{current_type}&apos; 之后不允许任何指令</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1434"/>
        <source>Invalid instruction sequence: &apos;{current_type}&apos; cannot be followed by &apos;{next_type}&apos;. Allowed types: {allowed_types}</source>
        <translation>无效的指令序列：&apos;{current_type}&apos; 后面不能跟 &apos;{next_type}&apos;。允许的类型：{allowed_types}</translation>
    </message>
    <message>
        <source>Invalid instruction sequence: {current_type} cannot be followed by {next_type}. Allowed types: {allowed_types}</source>
        <translation>无效的指令序列：{current_type} 后面不能跟 {next_type}。允许的类型：{allowed_types}</translation>
    </message>
    <message>
        <source>Invalid instruction sequence: &apos;%1&apos; cannot be followed by &apos;%2&apos;. Allowed types: %3</source>
        <translation>无效的指令序列： &apos;%1&apos; 后面不能跟 &apos;%2&apos;。允许的类型： %3</translation>
    </message>
    <message>
        <source>指令序列无效：&apos;{current_type}&apos;后不允许是&apos;{next_type}&apos;。允许的指令类型：{&apos;, &apos;.join(self.ALLOWED_NEXT_INSTRUCTIONS.get(current_type, {&apos;无&apos;}))}</source>
        <translation>指令序列无效：&apos;{current_type}&apos;后不允许是&apos;{next_type}&apos;。允许的指令类型：{&apos;, &apos;.join(self.ALLOWED_NEXT_INSTRUCTIONS.get(current_type, {&apos;无&apos;}))}</translation>
    </message>
    <message>
        <source>LANGUAGE</source>
        <translation>语言</translation>
    </message>
    <message>
        <source>Invalid sequence: &apos;%1&apos; cannot be followed by &apos;%2&apos;. Allowed types: %3</source>
        <translation>序列无效： &apos;%1&apos; 后面不能跟 &apos;%2&apos;。允许的类型： %3</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2377"/>
        <source>未命名程序</source>
        <translation>未命名程序</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1779"/>
        <source>       起弧:</source>
        <translation>       起弧:</translation>
    </message>
    <message>
        <source>起始安全点</source>
        <translation>起始安全点</translation>
    </message>
    <message>
        <source>起弧点</source>
        <translation>起弧点</translation>
    </message>
    <message>
        <source>直线</source>
        <translation>直线</translation>
    </message>
    <message>
        <source>圆弧中间点</source>
        <translation>圆弧中间点</translation>
    </message>
    <message>
        <source>圆弧末点</source>
        <translation>圆弧末点</translation>
    </message>
    <message>
        <source>终点安全点</source>
        <translation>终点安全点</translation>
    </message>
    <message>
        <source>待指定类型</source>
        <translation>待指定类型</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1268"/>
        <source>Instruction sequence violation. %1 cannot follow %2. Allowed: %3</source>
        <translation>允许</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1272"/>
        <source>none</source>
        <translation>无</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1841"/>
        <location filename="../logic/home_logic.py" line="1853"/>
        <source>程序启动失败</source>
        <translation>程序启动失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1842"/>
        <source>机器人处于【自动模式停止状态】下才可启动程序。</source>
        <translation>机器人处于【自动模式停止状态】下才可启动程序。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1854"/>
        <source>程序序列不符合规则，无法启动！
原因: {reason}</source>
        <translation>程序序列不符合规则，无法启动！
原因: {reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1876"/>
        <source>确认运行</source>
        <translation>确认运行</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1877"/>
        <source>确定要运行当前程序吗？</source>
        <translation>确定要运行当前程序吗</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1892"/>
        <location filename="../logic/home_logic.py" line="2028"/>
        <source>暂停</source>
        <translation>暂停</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1906"/>
        <location filename="../logic/home_logic.py" line="1916"/>
        <source>运行中</source>
        <translation>运行中</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1925"/>
        <source>启动</source>
        <translation>启动</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1926"/>
        <source>模拟运行</source>
        <translation>模拟运行</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2041"/>
        <source>继续</source>
        <translation>继续</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2061"/>
        <source>正在执行圆弧指令 (行 {data_index + 1})，暂停功能已禁用。</source>
        <translation>正在执行圆弧指令 (行 {data_index + 1})，暂停功能已禁用。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2065"/>
        <source>正在执行指令 (行 {data_index + 1})，暂停功能已启用。</source>
        <translation>正在执行指令 (行 {data_index + 1})，暂停功能已启用。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2070"/>
        <source>更新暂停按钮状态时出错: {e}</source>
        <translation>更新暂停按钮状态时出错: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2090"/>
        <source>创建失败</source>
        <translation>创建失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2091"/>
        <source>程序名称 &apos;{program_name}&apos; 已存在，请使用其他名称。</source>
        <translation>程序名称 &apos;{program_name}&apos; 已存在，请使用其他名称。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2113"/>
        <source>新程序创建成功，名称：{self._current_program_name}</source>
        <translation>新程序创建成功，名称：{self._current_program_name}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2117"/>
        <source>错误</source>
        <translation>错误</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2118"/>
        <source>创建程序失败，请检查数据库。</source>
        <translation>创建程序失败，请检查数据库。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2141"/>
        <source>程序序列不符合规则，无法保存！
原因: {reason}</source>
        <translation>程序序列不符合规则，无法保存！
原因: {reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2152"/>
        <source>当前没有选定的程序，请先通过 &apos;新建&apos; 按钮创建一个程序。</source>
        <translation>当前没有选定的程序，请先通过 &apos;新建&apos; 按钮创建一个程序。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2167"/>
        <source>已删除程序 &apos;{program_name_to_save}&apos; 的所有旧指令详情。</source>
        <translation>已删除程序 &apos;{program_name_to_save}&apos; 的所有旧指令详情。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2191"/>
        <source>Failed to insert instruction {instruction.get(&apos;instruction_type&apos;)}</source>
        <translation>无法插入指令：{instruction.get(&apos;instruction_type&apos;)}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2193"/>
        <source>插入指令: {instruction.get(&apos;instruction_type&apos;)} 到程序 &apos;{program_name_to_save}&apos;</source>
        <translation>插入指令: {instruction.get(&apos;instruction_type&apos;)} 到程序 &apos;{program_name_to_save}&apos;</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2207"/>
        <source>程序 &apos;{self._current_program_name}&apos; 所有指令已成功保存到数据库。</source>
        <translation>程序 &apos;{self._current_program_name}&apos; 所有指令已成功保存到数据库。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2214"/>
        <source>保存程序时发生错误：{e}
操作已回滚。</source>
        <translation>保存程序时发生错误：{e}
操作已回滚。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2219"/>
        <source>保存程序 &apos;{self._current_program_name}&apos; 失败: {e}</source>
        <translation>保存程序 &apos;{self._current_program_name}&apos; 失败: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2251"/>
        <location filename="../logic/home_logic.py" line="2263"/>
        <location filename="../logic/home_logic.py" line="2321"/>
        <source>另存为失败</source>
        <translation>另存为失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2252"/>
        <source>程序名称 &apos;{new_program_name}&apos; 已存在，请使用其他名称。</source>
        <translation>程序名称 &apos;{new_program_name}&apos; 已存在，请使用其他名称。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2264"/>
        <source>程序序列不符合规则，无法另存为！
原因: {reason}</source>
        <translation>程序序列不符合规则，无法另存为！
原因: {reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2322"/>
        <source>另存为程序时发生错误：{e}
操作已回滚。</source>
        <translation>另存为程序时发生错误：{e}
操作已回滚。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2327"/>
        <source>另存为程序 &apos;{new_program_name}&apos; 失败: {e}</source>
        <translation>另存为程序 &apos;{new_program_name}&apos; 失败: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2344"/>
        <source>删除程序</source>
        <translation>删除程序</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2345"/>
        <source>确定你想要删除程序 &apos;{0}&apos;?</source>
        <translation>确定你想要删除程序 &apos;{0}&apos;?</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2364"/>
        <source>删除program_detail表记录结果：{delete_detail_result}</source>
        <translation>删除program_detail表记录结果：{delete_detail_result}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2396"/>
        <source>删除过程中发生错误：{e}</source>
        <translation>删除过程中发生错误：{e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2401"/>
        <source>删除程序 &apos;{current_program}&apos; 失败: {e}</source>
        <translation>删除程序 &apos;{current_program}&apos; 失败: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2409"/>
        <source>请先选择要更新的行</source>
        <translation>请先选择要更新的行</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2421"/>
        <source>请先编辑“待指定类型”的指令，指定其具体类型后再进行点位重定位。</source>
        <translation>请先编辑“待指定类型”的指令，指定其具体类型后再进行点位重定位。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2435"/>
        <source>行 {} 的点位信息已更新。</source>
        <translation>行 {} 的点位信息已更新。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2438"/>
        <source>点位更新</source>
        <translation>点位更新</translation>
    </message>
    <message>
        <source>行 {row_index + 1} 的点位信息已更新。</source>
        <translation>行 {row_index + 1} 的点位信息已更新。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2447"/>
        <source>更新失败</source>
        <translation>更新失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2448"/>
        <source>选定的行索引超出程序指令范围。</source>
        <translation>选定的行索引超出程序指令范围。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2487"/>
        <source>失败</source>
        <translation>失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2510"/>
        <source>请先编辑“待指定类型”的指令，指定其具体类型后再执行移动。</source>
        <translation>请先编辑“待指定类型”的指令，指定其具体类型后再执行移动。</translation>
    </message>
</context>
<context>
    <name>InputError</name>
    <message>
        <location filename="../logic/home_logic.py" line="276"/>
        <location filename="../logic/home_logic.py" line="291"/>
        <location filename="../logic/home_logic.py" line="537"/>
        <location filename="../logic/home_logic.py" line="554"/>
        <location filename="../logic/home_logic.py" line="574"/>
        <location filename="../logic/home_logic.py" line="586"/>
        <location filename="../logic/home_logic.py" line="622"/>
        <location filename="../logic/home_logic.py" line="641"/>
        <location filename="../logic/home_logic.py" line="722"/>
        <source>输入错误</source>
        <translation>输入错误</translation>
    </message>
</context>
<context>
    <name>InputValidation</name>
    <message>
        <location filename="../logic/home_logic.py" line="277"/>
        <source>电流值必须在0到500之间</source>
        <translation>电流值必须在0-500之间</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="292"/>
        <source>电压值必须在0到50之间</source>
        <translation>电压值必须在0到50之间</translation>
    </message>
    <message>
        <source>电压值必须在0到30之间</source>
        <translation>电压值必须在0到50之间</translation>
    </message>
</context>
<context>
    <name>InstructionType</name>
    <message>
        <location filename="../logic/home_logic.py" line="1683"/>
        <source>起始安全点</source>
        <translation>起始安全点</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1684"/>
        <source>起弧点</source>
        <translation>起弧点</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1685"/>
        <source>直线</source>
        <translation>直线</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1686"/>
        <source>圆弧中间点</source>
        <translation>圆弧中间点</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1687"/>
        <source>圆弧末点</source>
        <translation>圆弧末点</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1688"/>
        <source>终点安全点</source>
        <translation>终点安全点</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1691"/>
        <source>待指定类型</source>
        <translation>待指定类型</translation>
    </message>
</context>
<context>
    <name>Logging</name>
    <message>
        <location filename="../logic/home_logic.py" line="786"/>
        <source>另存为工艺 &apos;{new_process_name}&apos; 失败: {e}</source>
        <translation>另存为工艺 &apos;{new_process_name}&apos; 失败: {e}</translation>
    </message>
</context>
<context>
    <name>MainWindow</name>
    <message>
        <source>编辑</source>
        <translation>编辑</translation>
    </message>
    <message>
        <source>删除</source>
        <translation>删除</translation>
    </message>
    <message>
        <source>自动</source>
        <translation>自动</translation>
    </message>
    <message>
        <source>手动</source>
        <translation>手动</translation>
    </message>
    <message>
        <source>拖动</source>
        <translation>拖动</translation>
    </message>
    <message>
        <source>操作确认</source>
        <translation>操作确认</translation>
    </message>
    <message>
        <source>紧急停止已触发！</source>
        <translation>紧急停止已触发！</translation>
    </message>
    <message>
        <source>紧急停止已触发!</source>
        <translation>紧急停止已触发!</translation>
    </message>
    <message>
        <source>智焊</source>
        <translation>智焊</translation>
    </message>
</context>
<context>
    <name>Move Filed</name>
    <message>
        <location filename="../logic/home_logic.py" line="1126"/>
        <location filename="../logic/home_logic.py" line="1198"/>
        <source>移动失败</source>
        <translation>移动失败</translation>
    </message>
</context>
<context>
    <name>MyDialog</name>
    <message>
        <location filename="../logic/home_logic.py" line="1127"/>
        <source>已是第一行，无法上移。</source>
        <translation>已是第一行，无法上移</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1199"/>
        <source>已是最后一行，无法下移。</source>
        <translation>已是最后一行，无法下移。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1554"/>
        <location filename="../logic/home_logic.py" line="1565"/>
        <source>打开程序</source>
        <translation>打开程序</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1555"/>
        <source>没有可用的程序。请先创建新程序。</source>
        <translation>没有可用的程序。请先创建新程序。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1566"/>
        <source>选择要打开的程序：</source>
        <translation>选择要打开的程序：</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1865"/>
        <source>程序列表为空，无法启动。</source>
        <translation>程序列表为空，无法启动。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2075"/>
        <source>新建程序</source>
        <translation>新建程序</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2076"/>
        <source>请输入程序名称：</source>
        <translation>请输入程序名称：</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2129"/>
        <source>当前程序列表为空，无需保存。</source>
        <translation>当前程序列表为空，无需保存</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2308"/>
        <source>程序已成功另存为『%1』。</source>
        <translation>程序已成功另存为『%1』。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2385"/>
        <source>程序『%s』已成功删除</source>
        <translation>程序『%s』已成功删除</translation>
    </message>
    <message>
        <source>程序已成功另存为 &apos;{new_program_name}&apos;。</source>
        <translation>程序已成功另存为 &apos;{new_program_name}&apos;。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2335"/>
        <source>当前没有打开的程序或程序未命名。</source>
        <translation>当前没有打开的程序或程序未命名。</translation>
    </message>
    <message>
        <source>程序 &apos;{current_program}&apos; 已成功删除</source>
        <translation>程序 &apos;{current_program}&apos; 已成功删除</translation>
    </message>
    <message>
        <source>行 {row_index + 1} 的点位信息已更新。</source>
        <translation>行 {row_index + 1} 的点位信息已更新。</translation>
    </message>
</context>
<context>
    <name>OperationHint</name>
    <message>
        <location filename="../logic/home_logic.py" line="320"/>
        <source>操作提示</source>
        <translation>操作提示</translation>
    </message>
</context>
<context>
    <name>ProcessController</name>
    <message>
        <location filename="../logic/home_logic.py" line="844"/>
        <source>未找到工艺包 ID 为 {process_logic.PROCESS_update} 的记录。</source>
        <translation>未找到工艺包 ID 为 {process_logic.PROCESS_update} 的记录。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="875"/>
        <source>已更新所有使用工艺包 ID 为 {process_logic.PROCESS_update} 的行。</source>
        <translation>已更新所有使用工艺包 ID 为 {process_logic.PROCESS_update} 的行。</translation>
    </message>
</context>
<context>
    <name>ProcessDialogs</name>
    <message>
        <location filename="../logic/home_logic.py" line="693"/>
        <source>另存为工艺</source>
        <translation>另存为工艺</translation>
    </message>
    <message>
        <source>名称重复</source>
        <translation>名称重复</translation>
    </message>
</context>
<context>
    <name>ProcessInfo</name>
    <message>
        <location filename="../logic/home_logic.py" line="1742"/>
        <source>      工艺: {0};</source>
        <translation>      工艺: {0};</translation>
    </message>
</context>
<context>
    <name>ProcessInfoForm</name>
    <message>
        <source>Form</source>
        <translation>Form</translation>
    </message>
    <message>
        <source>导出</source>
        <translation>导出</translation>
    </message>
    <message>
        <source>导入</source>
        <translation>导入</translation>
    </message>
    <message>
        <source>新增</source>
        <translation>新增</translation>
    </message>
    <message>
        <source>序号</source>
        <translation>序号</translation>
    </message>
    <message>
        <source>工艺名称</source>
        <translation>工艺名称</translation>
    </message>
    <message>
        <source>焊接参数</source>
        <translation>焊接参数</translation>
    </message>
    <message>
        <source>摆动参数</source>
        <translation>摆动参数</translation>
    </message>
    <message>
        <source>操作</source>
        <translation>操作</translation>
    </message>
</context>
<context>
    <name>ProcessNaming</name>
    <message>
        <location filename="../logic/home_logic.py" line="694"/>
        <source>请输入新的工艺名称：</source>
        <translation>请输入新的工艺名称：</translation>
    </message>
    <message>
        <source>工艺名称 &apos;{new_process_name}&apos; 已存在，请使用其他名称</source>
        <translation>工艺名称 &apos;{new_process_name}&apos; 已存在，请使用其他名称</translation>
    </message>
</context>
<context>
    <name>ProcessTable</name>
    <message>
        <source>序号</source>
        <translation>序号</translation>
    </message>
    <message>
        <source>工艺名称</source>
        <translation>工艺名称</translation>
    </message>
    <message>
        <source>焊接参数</source>
        <translation>焊接参数</translation>
    </message>
    <message>
        <source>摆动参数</source>
        <translation>摆动参数</translation>
    </message>
    <message>
        <source>操作</source>
        <translation>操作</translation>
    </message>
</context>
<context>
    <name>Program</name>
    <message>
        <location filename="../logic/home_logic.py" line="64"/>
        <source>Untitled Program</source>
        <translation>未命名程序</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2199"/>
        <source>程序 {prog_name} 已成功保存！</source>
        <translation>程序 {prog_name} 已成功保存！</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2201"/>
        <source>保存成功</source>
        <translation>保存成功</translation>
    </message>
</context>
<context>
    <name>Reason</name>
    <message>
        <location filename="../logic/home_logic.py" line="723"/>
        <source>速度、电流、电压必须是有效的数字。</source>
        <translation>速度、电流、电压必须是有效的数字。</translation>
    </message>
    <message>
        <source>工艺 &apos;{new_process_name}&apos; 已成功另存为。</source>
        <translation>工艺 &apos;{new_process_name}&apos; 已成功另存为。</translation>
    </message>
    <message>
        <source>另存为工艺时发生错误：{e}
操作已回滚。</source>
        <translation>另存为工艺时发生错误：{e}
操作已回滚。</translation>
    </message>
    <message>
        <source>未找到该工艺参数！</source>
        <translation>未找到该工艺参数！</translation>
    </message>
    <message>
        <source>工艺名称不能为空！</source>
        <translation>工艺名称不能为空！</translation>
    </message>
    <message>
        <source>输入格式错误：{str(e)}</source>
        <translation>输入格式错误：{str(e)}</translation>
    </message>
    <message>
        <source>保存失败：{str(e)}</source>
        <translation>保存失败</translation>
    </message>
    <message>
        <source>未找到ID为 {process_id} 的工艺参数，可能已被删除或不存在。</source>
        <translation>未找到ID为 {process_id} 的工艺参数，可能已被删除或不存在。</translation>
    </message>
    <message>
        <source>工艺 &apos;{process_name}&apos; 正在被程序&gt; {used_in_programs[0][&apos;program_id&apos;]} &lt;使用中，无法删除！</source>
        <translation>工艺 &apos;{process_name}&apos; 正在被程序&gt; {used_in_programs[0][&apos;program_id&apos;]} &lt;使用中，无法删除！</translation>
    </message>
    <message>
        <source>删除失败：{str(e)}</source>
        <translation>删除失败：{str(e)}</translation>
    </message>
    <message>
        <source>工艺参数已成功保存至：
{file_path}</source>
        <translation>工艺参数已成功保存至：
{file_path}</translation>
    </message>
    <message>
        <source>错误：
{e}</source>
        <translation>错误：
{e}</translation>
    </message>
    <message>
        <source>标准模板已保存至：
{file_path}</source>
        <translation>标准模板已保存至：
{file_path}</translation>
    </message>
    <message>
        <source>已成功导入{imported_count}条工艺参数</source>
        <translation>已成功导入{imported_count}条工艺参数</translation>
    </message>
    <message>
        <source>点焊安全点已重定位。{point_position_dict}</source>
        <translation>点焊安全点已重定位。{point_position_dict}</translation>
    </message>
    <message>
        <source>当前状态不允许切换模式</source>
        <translation>当前状态不允许切换模式</translation>
    </message>
    <message>
        <source>请选择焊机控制方式！</source>
        <translation>请选择焊机控制方式！</translation>
    </message>
    <message>
        <source>焊机延时请输入有效数字！</source>
        <translation>焊机延时请输入有效数字！</translation>
    </message>
    <message>
        <source>焊机控制设置已成功保存！</source>
        <translation>焊机控制设置已成功保存！</translation>
    </message>
    <message>
        <source>语言设置已成功保存！</source>
        <translation>语言设置已成功保存</translation>
    </message>
    <message>
        <source>机器人应用设置已成功保存！</source>
        <translation>机器人应用设置已成功保存！</translation>
    </message>
    <message>
        <source>自定义百分比必须是0到100的整数！</source>
        <translation>自定义百分比必须是0到100的整数！</translation>
    </message>
    <message>
        <source>碰撞设置已成功保存！</source>
        <translation>碰撞设置已成功保存！</translation>
    </message>
    <message>
        <source>保存碰撞设置时发生错误: {e}</source>
        <translation>保存碰撞设置时发生错误: {e}</translation>
    </message>
</context>
<context>
    <name>SafetyPoint</name>
    <message>
        <source>点焊安全点已重定位。位置: {}</source>
        <translation>点焊安全点已重定位。位置: {}</translation>
    </message>
    <message>
        <source>安全点定位成功</source>
        <translation>安全点定位成功</translation>
    </message>
</context>
<context>
    <name>Save</name>
    <message>
        <source>模板保存</source>
        <translation>模板保存</translation>
    </message>
    <message>
        <source>导入成功</source>
        <translation>导入成功</translation>
    </message>
    <message>
        <source>导入失败</source>
        <translation>导入失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2128"/>
        <source>保存失败</source>
        <translation>保存失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2310"/>
        <source>另存为成功</source>
        <translation>另存为成功</translation>
    </message>
</context>
<context>
    <name>SaveDialog</name>
    <message>
        <location filename="../logic/home_logic.py" line="2234"/>
        <source>程序另存为：</source>
        <translation>程序另存为：</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2235"/>
        <source>请输入新的程序名称</source>
        <translation>请输入新的程序名称</translation>
    </message>
</context>
<context>
    <name>SaveError</name>
    <message>
        <location filename="../logic/home_logic.py" line="2225"/>
        <source>另存为失败</source>
        <translation>另存为失败</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2226"/>
        <source>当前程序列表为空，没有内容可另存为</source>
        <translation>当前程序列表为空，没有内容可另存为</translation>
    </message>
</context>
<context>
    <name>Savefause</name>
    <message>
        <source>保存失败</source>
        <translation>保存失败</translation>
    </message>
</context>
<context>
    <name>Savesuccess</name>
    <message>
        <source>保存成功</source>
        <translation>保存成功</translation>
    </message>
</context>
<context>
    <name>SequenceError</name>
    <message>
        <source>编辑失败</source>
        <translation>编辑失败</translation>
    </message>
</context>
<context>
    <name>Start</name>
    <message>
        <location filename="../logic/home_logic.py" line="1864"/>
        <source>程序启动</source>
        <translation>程序启动</translation>
    </message>
</context>
<context>
    <name>Status</name>
    <message>
        <source>未解决</source>
        <translation>未解决</translation>
    </message>
    <message>
        <source>已解决</source>
        <translation>已解决</translation>
    </message>
</context>
<context>
    <name>SwingInfo</name>
    <message>
        <location filename="../logic/home_logic.py" line="1765"/>
        <source>摆动: {swing_type} {frequency}Hz {amplitude}mm;</source>
        <translation>摆动: {swing_type} {frequency}Hz {amplitude}mm;</translation>
    </message>
</context>
<context>
    <name>SwingInfoText</name>
    <message>
        <source>{swing_type}--&gt;频率: {frequency}Hz; 幅度: {amplitude}mm; 停留: {left_stop}ms\{right_stop}ms</source>
        <translation>{swing_type}--&gt;频率: {frequency}Hz; 幅度: {amplitude}mm; 停留: {left_stop}ms\{right_stop}ms</translation>
    </message>
    <message>
        <source>摆动：{swing_type}--&gt;频率: {frequency}Hz; 幅度: {amplitude}mm; 停留: {left_stop}ms\{right_stop}ms</source>
        <translation>摆动：{swing_type}--&gt;频率: {frequency}Hz; 幅度: {amplitude}mm; 停留: {left_stop}ms\{right_stop}ms</translation>
    </message>
</context>
<context>
    <name>SwingParameters</name>
    <message>
        <source>摆动: {0} {1:.1f}HZ {2:.1f}mm;</source>
        <translation>摆动: {0} {1:.1f}HZ {2:.1f}mm;</translation>
    </message>
</context>
<context>
    <name>SwingType</name>
    <message>
        <location filename="../logic/home_logic.py" line="1749"/>
        <source>三角波摆动</source>
        <translation>三角波摆动</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1750"/>
        <source>垂直L型三角波</source>
        <translation>垂直L型三角波</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1751"/>
        <source>圆型（顺时针）</source>
        <translation>圆型（顺时针）</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1752"/>
        <source>圆型（逆时针）</source>
        <translation>圆型（逆时针）</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1753"/>
        <source>正弦波摆动</source>
        <translation>正弦波摆动</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1754"/>
        <source>垂直L型正弦波</source>
        <translation>垂直L型正弦波</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1759"/>
        <source>立焊三角形摆动</source>
        <translation>立焊三角形摆动</translation>
    </message>
</context>
<context>
    <name>SwingTypes</name>
    <message>
        <source>三角波摆动</source>
        <translation>三角波摆动</translation>
    </message>
    <message>
        <source>垂直L型三角波</source>
        <translation>垂直L型三角波</translation>
    </message>
    <message>
        <source>圆型（顺时针）</source>
        <translation>圆型（顺时针）</translation>
    </message>
    <message>
        <source>圆型（逆时针）</source>
        <translation>圆型（逆时针）</translation>
    </message>
    <message>
        <source>正弦波摆动</source>
        <translation>正弦波摆动</translation>
    </message>
    <message>
        <source>垂直L型正弦波</source>
        <translation>垂直L型正弦波</translation>
    </message>
    <message>
        <source>立焊三角形摆动</source>
        <translation>立焊三角形摆动</translation>
    </message>
</context>
<context>
    <name>TableHeader</name>
    <message>
        <location filename="../logic/home_logic.py" line="1665"/>
        <source>序号</source>
        <translation>序号</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1666"/>
        <source>指令类型</source>
        <translation>指令类型</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1667"/>
        <source>点位信息</source>
        <translation>点位信息</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1670"/>
        <source>速度</source>
        <translation>速度</translation>
    </message>
</context>
<context>
    <name>Warning</name>
    <message>
        <source>提示</source>
        <translation>提示</translation>
    </message>
    <message>
        <source>警告</source>
        <translation>警告</translation>
    </message>
</context>
<context>
    <name>WeldParams</name>
    <message>
        <source>电流: {current}A; 电压: {voltage}V; 速度: {speed}mm/s</source>
        <translation>电流: {current}A; 电压: {voltage}V; 速度: {speed}mm/s</translation>
    </message>
</context>
<context>
    <name>data</name>
    <message>
        <source>点位更新</source>
        <translation>点位更新</translation>
    </message>
</context>
<context>
    <name>error</name>
    <message>
        <source>错误</source>
        <translation>错误</translation>
    </message>
    <message>
        <source>输入错误</source>
        <translation>输入错误</translation>
    </message>
    <message>
        <source>保存错误</source>
        <translation>保存错误</translation>
    </message>
</context>
<context>
    <name>expert</name>
    <message>
        <source>导出成功</source>
        <translation>导出成功</translation>
    </message>
    <message>
        <source>导出失败</source>
        <translation>导出失败</translation>
    </message>
</context>
<context>
    <name>point</name>
    <message>
        <source>安全点定位成功</source>
        <translation>安全点定位成功</translation>
    </message>
</context>
<context>
    <name>success</name>
    <message>
        <source>成功</source>
        <translation>成功</translation>
    </message>
</context>
<context>
    <name>widget</name>
    <message>
        <source>智焊</source>
        <translation>智焊</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:20pt; font-weight:700;&quot;&gt;智能焊接&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:20pt; font-weight:700;&quot;&gt;智能焊接&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>   主  页   </source>
        <translation>   主  页   </translation>
    </message>
    <message>
        <source>工艺管理</source>
        <translation>工艺管理</translation>
    </message>
    <message>
        <source>手动控制</source>
        <translation>手动控制</translation>
    </message>
    <message>
        <source>通用设置</source>
        <translation>通用设置</translation>
    </message>
    <message>
        <source>报警管理</source>
        <translation>报警管理</translation>
    </message>
    <message>
        <source>已连接</source>
        <translation>已连接</translation>
    </message>
    <message>
        <source>自动</source>
        <translation>自动</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; color:#ffffff;&quot;&gt;时间&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; color:#ffffff;&quot;&gt;时间&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
</context>
<context>
    <name>断焊</name>
    <message>
        <source> SegWeld: Execute {0:.1f}mm Non-exec {1:.1f}mm;</source>
        <translation>断焊: Execute {0:.1f}mm Non-exec {1:.1f}mm;</translation>
    </message>
</context>
</TS>
