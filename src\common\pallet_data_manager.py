from typing import Dict, Optional, List
from datetime import datetime
import json
from src.common.sql_lite_tool import SqliteTool
from src.common.log import Logger
class PalletDataManager:
    """垛盘数据管理类 - 封装垛盘相关的数据库操作"""

    def __init__(self):
        self.db_tool = SqliteTool.get_instance()

    def get_pallet_status(self, pallet_id: str) -> Optional[Dict]:
        """获取垛盘状态信息"""
        result = self.db_tool.select(
            "pallet_status",
            condition="pallet_id=?",
            params=[pallet_id]
        )
        return result[0] if result else None

    def update_pallet_status(self, pallet_id: str, status: str) -> bool:
        """更新垛盘状态"""
        return self.db_tool.update(
            "pallet_status",
            {"status": status, "updated_at": datetime.now().isoformat()},
            f"pallet_id='{pallet_id}'"
        )

    def set_pallet_ready(self, pallet_id: str) -> bool:
        """设置垛盘为就绪状态（仅当当前状态为空闲时）"""
        current_status = self.get_pallet_status(pallet_id)
        if current_status and current_status['status'] == 'idle':
            return self.db_tool.update(
                "pallet_status",
                {
                    "status": "ready",
                    "is_ready": 1,
                    "updated_at": datetime.now().isoformat()
                },
                f"pallet_id='{pallet_id}'"
            )
        return False

    def toggle_cancel_palletizing(self, pallet_id: str) -> bool:
        """切换取消码垛状态"""
        current_status = self.get_pallet_status(pallet_id)
        if current_status:
            new_cancel_status = 1 - current_status['cancel_palletizing']  # 0变1，1变0

            # 如果从关闭(0)切换为开启(1)，保存当前的层数和工作号
            if current_status['cancel_palletizing'] == 0 and new_cancel_status == 1:
                return self.db_tool.update(
                    "pallet_status",
                    {
                        "cancel_palletizing": new_cancel_status,
                        "paused_layer": current_status['current_layer'],
                        "paused_work_number": current_status['current_layer_boxes'],
                        "updated_at": datetime.now().isoformat()
                    },
                    f"pallet_id='{pallet_id}'"
                )
            else:
                # 从开启(1)切换为关闭(0)，只更新状态
                return self.db_tool.update(
                    "pallet_status",
                    {
                        "cancel_palletizing": new_cancel_status,
                        "updated_at": datetime.now().isoformat()
                    },
                    f"pallet_id='{pallet_id}'"
                )
        return False

    def get_paused_data(self, pallet_id: str) -> Optional[Dict]:
        """获取暂停时的数据"""
        result = self.db_tool.select(
            "pallet_status",
            fields=["paused_layer", "paused_work_number"],
            condition="pallet_id=?",
            params=[pallet_id]
        )
        return result[0] if result else None

    def clear_paused_data(self, pallet_id: str) -> bool:
        """清除暂停数据"""
        return self.db_tool.update(
            "pallet_status",
            {
                "paused_layer": 0,
                "paused_work_number": 0,
                "updated_at": datetime.now().isoformat()
            },
            f"pallet_id='{pallet_id}'"
        )

    def reset_pallet_data(self, pallet_id: str) -> bool:
        """重置垛盘数据"""
        return self.db_tool.update(
            "pallet_status",
            {
                "status": "idle",
                "is_ready": 0,
                "current_layer": 1,
                "current_layer_boxes": 0,
                "current_position_x": 0,
                "current_position_y": 0,
                "updated_at": datetime.now().isoformat()
            },
            f"pallet_id='{pallet_id}'"
        )

    def force_full_pallet(self, pallet_id: str) -> bool:
        """强制满垛"""
        return self.db_tool.update(
            "pallet_status",
            {
                "status": "full",
                "updated_at": datetime.now().isoformat()
            },
            f"pallet_id='{pallet_id}'"
        )

    def update_current_layer_boxes(self, pallet_id: str, current_layer_boxes: int) -> bool:
        """更新当前层箱子数"""
        return self.db_tool.update(
            "pallet_status",
            {
                "current_layer_boxes": current_layer_boxes,
                "updated_at": datetime.now().isoformat()
            },
            f"pallet_id='{pallet_id}'"
        )

    def next_layer(self, pallet_id: str) -> bool:
        """进入下一层（层数+1，当前层箱子数重置为0）"""
        current_status = self.get_pallet_status(pallet_id)
        if current_status:
            new_layer = current_status['current_layer'] + 1
            return self.db_tool.update(
                "pallet_status",
                {
                    "current_layer": new_layer,
                    "current_layer_boxes": 0,
                    "updated_at": datetime.now().isoformat()
                },
                f"pallet_id='{pallet_id}'"
            )
        return False

    def update_layer_progress(self, pallet_id: str, current_layer: int) -> bool:
        """更新层数进度"""
        return self.db_tool.update(
            "pallet_status",
            {
                "current_layer": current_layer,
                "updated_at": datetime.now().isoformat()
            },
            f"pallet_id='{pallet_id}'"
        )

    def get_work_record(self, pallet_id: str, work_session_id: str = None) -> Optional[Dict]:
        """获取作业记录"""
        condition = "pallet_id=? AND status='active'"
        params = [pallet_id]

        if work_session_id:
            condition += " AND work_session_id=?"
            params.append(work_session_id)

        result = self.db_tool.select(
            "pallet_work_record",
            condition=condition + " ORDER BY created_at DESC LIMIT 1",
            params=params
        )
        return result[0] if result else None

    def create_work_record(self, pallet_id: str, work_session_id: str,
                          total_layers: int, total_boxes: int, formula_id: int = None) -> bool:
        """创建作业记录"""
        return self.db_tool.insert(
            "pallet_work_record",
            {
                "pallet_id": pallet_id,
                "work_session_id": work_session_id,
                "start_time": datetime.now().isoformat(),
                "total_layers": total_layers,
                "total_boxes": total_boxes,
                "formula_id": formula_id,
                "status": "active"
            }
        )

    def update_work_progress(self, work_session_id: str, completed_layers: int, completed_boxes: int) -> bool:
        """更新作业进度"""
        return self.db_tool.update(
            "pallet_work_record",
            {
                "completed_layers": completed_layers,
                "completed_boxes": completed_boxes
            },
            f"work_session_id='{work_session_id}' AND status='active'"
        )

    def complete_work_record(self, work_session_id: str) -> bool:
        """完成作业记录"""
        return self.db_tool.update(
            "pallet_work_record",
            {
                "status": "completed",
                "end_time": datetime.now().isoformat()
            },
            f"work_session_id='{work_session_id}'"
        )

    def get_teaching_points_by_type(self) -> Dict[str, Dict]:
        """根据point_type获取所有示教点数据"""
        try:
            # 获取所有示教点数据
            records = self.db_tool.select("teaching_points")

            teaching_data = {}
            for record in records:
                point_type = record['point_type']
                teaching_data[point_type] = {
                    'x': float(record['x']) if record['x'] else 0.0,
                    'y': float(record['y']) if record['y'] else 0.0,
                    'z': float(record['z']) if record['z'] else 0.0,
                    'rx': float(record['rx']) if record['rx'] else 0.0,
                    'ry': float(record['ry']) if record['ry'] else 0.0,
                    'rz': float(record['rz']) if record['rz'] else 0.0,
                    'j1': float(record['j1']) if record['j1'] else 0.0,
                    'j2': float(record['j2']) if record['j2'] else 0.0,
                    'j3': float(record['j3']) if record['j3'] else 0.0,
                    'j4': float(record['j4']) if record['j4'] else 0.0,
                    'j5': float(record['j5']) if record['j5'] else 0.0,
                    'j6': float(record['j6']) if record['j6'] else 0.0,
                    'speed': int(record['speed']) if record['speed'] else 20,
                    'toolNum': int(record['toolNum']) if record['toolNum'] else 1,
                    'workPieceNum': int(record['workPieceNum']) if record['workPieceNum'] else 0
                }

            # 如果没有数据，返回默认示教点
            if not teaching_data:
                return self._get_default_teaching_points()

            return teaching_data

        except Exception as e:
            Logger.error(f"获取示教点数据失败: {e}")
            return self._get_default_teaching_points()

    def _get_default_teaching_points(self) -> Dict[str, Dict]:
        """获取默认示教点数据"""
        return {
            'grab_point': {
                'x': 500.0, 'y': 0.0, 'z': 300.0,
                'rx': 180.0, 'ry': 0.0, 'rz': 0.0,
                'j1': 0.0, 'j2': -90.0, 'j3': 90.0, 'j4': 0.0, 'j5': 90.0, 'j6': 0.0,
                'speed': 20, 'toolNum': 1, 'workPieceNum': 0
            },
            'grab_transition_point': {
                'x': 500.0, 'y': 0.0, 'z': 400.0,
                'rx': 180.0, 'ry': 0.0, 'rz': 0.0,
                'j1': 0.0, 'j2': -90.0, 'j3': 90.0, 'j4': 0.0, 'j5': 90.0, 'j6': 0.0,
                'speed': 20, 'toolNum': 1, 'workPieceNum': 0
            },
            'place_transition_point': {
                'x': 300.0, 'y': 300.0, 'z': 400.0,
                'rx': 180.0, 'ry': 0.0, 'rz': 0.0,
                'j1': 45.0, 'j2': -90.0, 'j3': 90.0, 'j4': 0.0, 'j5': 90.0, 'j6': 0.0,
                'speed': 20, 'toolNum': 1, 'workPieceNum': 0
            },
            'left_pallet_zero_point': {
                'x': 200.0, 'y': 300.0, 'z': 20.0,
                'rx': 180.0, 'ry': 0.0, 'rz': 0.0,
                'j1': 45.0, 'j2': -90.0, 'j3': 90.0, 'j4': 0.0, 'j5': 90.0, 'j6': 0.0,
                'speed': 20, 'toolNum': 1, 'workPieceNum': 0
            },
            'right_pallet_zero_point': {
                'x': 200.0, 'y': -300.0, 'z': 20.0,
                'rx': 180.0, 'ry': 0.0, 'rz': 0.0,
                'j1': -45.0, 'j2': -90.0, 'j3': 90.0, 'j4': 0.0, 'j5': 90.0, 'j6': 0.0,
                'speed': 20, 'toolNum': 1, 'workPieceNum': 0
            },
            'initial_position': {
                'x': 0.0, 'y': 0.0, 'z': 500.0,
                'rx': 180.0, 'ry': 0.0, 'rz': 0.0,
                'j1': 0.0, 'j2': -90.0, 'j3': 90.0, 'j4': 0.0, 'j5': 90.0, 'j6': 0.0,
                'speed': 20, 'toolNum': 1, 'workPieceNum': 0
            }
        }

    def get_pallet_config(self, pallet_id: str = None) -> Dict:
        """获取垛盘配置数据，支持特定垛盘ID"""
        base_config = {
            'pallet': {'length': 900, 'width': 900, 'height': 20},
            'box': {'length': 100, 'width': 100, 'height': 100},
            'material_direction': 'default',
            'layers': 2,
            'layer_pattern': {'rows': 3, 'cols': 3, 'row_spacing': 20, 'col_spacing': 20}
        }

        if pallet_id:
            # 可以根据垛盘ID从数据库获取特定配置
            pallet_specific_config = self.get_pallet_specific_config(pallet_id)
            if pallet_specific_config:
                base_config.update(pallet_specific_config)

            base_config['pallet_id'] = pallet_id

        return base_config

    def get_pallet_specific_config(self, pallet_id: str) -> Optional[Dict]:
        """获取垛盘特定配置"""
        try:
            result = self.db_tool.select(
                "pallet_config",
                condition="pallet_id=?",
                params=[pallet_id]
            )
            return result[0] if result else None
        except Exception as e:
            Logger.error(f"获取垛盘 {pallet_id} 特定配置失败: {e}")
            return None

    def update_pallet_progress(self, pallet_id: str, layer: int, box_count: int) -> bool:
        """更新垛盘执行进度"""
        return self.db_tool.update(
            "pallet_status",
            {
                "current_layer": layer,
                "current_layer_boxes": box_count,
                "updated_at": datetime.now().isoformat()
            },
            f"pallet_id='{pallet_id}'"
        )

    def get_material_direction_rz(self, direction: str = 'default') -> float:
        """根据来料方向获取RZ旋转角度"""
        direction_map = {
            'default': 0.0,
            'rotate_90': 90.0,
            'rotate_180': 180.0,
            'rotate_270': 270.0
        }
        return direction_map.get(direction, 0.0)