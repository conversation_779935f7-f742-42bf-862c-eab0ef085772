import json
from PySide6.QtCore import QRectF

"""
箱子类、托盘类、模式管理器、智能托盘规划器
"""

class BoxItem:
    """箱子项目类，用于存储单个箱子的信息"""
    
    def __init__(self, id, x, y, w, l, rotated=False):
        self.id = id
        self.x = x
        self.y = y
        self.width = w
        self.length = l
        self.rotated = rotated
        self.rotation_angle = 0  # 累积旋转角度（度）
    
    def get_rect(self):
        """获取箱子的矩形区域"""
        return QRectF(self.x, self.y, self.width, self.length)
    
    def get_center(self):
        """获取箱子的中心点坐标"""
        center_x = self.x + self.width / 2
        center_y = self.y + self.length / 2
        return (center_x, center_y)
    
    def intersects(self, other):
        """检查是否与另一个箱子相交"""
        return self.get_rect().intersects(other.get_rect())
    
    def to_dict(self):
        """转换为字典，用于JSON序列化"""
        center = self.get_center()
        return {
            "id": self.id,
            "x": self.x,
            "y": self.y,
            "width": self.width,
            "length": self.length,
            "rotated": self.rotated,
            "rotation_angle": self.rotation_angle,
            "center_x": center[0],
            "center_y": center[1]
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建BoxItem实例"""
        box = cls(
            data["id"],
            data["x"],
            data["y"],
            data["width"],
            data["length"],
            data.get("rotated", False)
        )
        box.rotation_angle = data.get("rotation_angle", 0)
        return box


class PalletPattern:
    """托盘模式类，用于存储完整的托盘布局方案"""
    
    def __init__(self, name, pallet_width=1200, pallet_length=1200,
                 box_width=300, box_length=200, gap=10, auto_mode=False,
                 rows=None, cols=None, row_spacing=None, col_spacing=None):
        self.name = name
        self.pallet_width = pallet_width
        self.pallet_length = pallet_length
        self.box_width = box_width
        self.box_length = box_length
        self.gap = gap
        self.auto_mode = auto_mode
        self.boxes = []
        
        # 手动模式参数
        self.rows = rows if rows is not None else 3
        self.cols = cols if cols is not None else 3
        self.row_spacing = row_spacing if row_spacing is not None else 0
        self.col_spacing = col_spacing if col_spacing is not None else 0
    
    def add_box(self, box):
        """添加箱子，进行严格的边界检查"""
        # 检查托盘尺寸是否有效
        if self.pallet_width <= 0 or self.pallet_length <= 0:
            return False

        # 检查箱子尺寸是否有效
        if box.width <= 0 or box.length <= 0:
            return False

        # 检查箱子是否完全在托盘边界内
        if (box.x < 0 or box.y < 0 or
            box.x + box.width > self.pallet_width or
            box.y + box.length > self.pallet_length):
        
            return False

        # 检查是否与其他箱子重叠
        # for existing_box in self.boxes:
        #     if box.intersects(existing_box):
        #         return False

        self.boxes.append(box)
        return True
    
    def update_box_position(self, box_id, new_x, new_y):
        """更新箱子位置，进行严格的边界检查"""
        # 获取要更新的箱子
        box_to_update = None
        for box in self.boxes:
            if box.id == box_id:
                box_to_update = box
                break

        if not box_to_update:
            return False

        # 检查托盘尺寸是否有效
        if self.pallet_width <= 0 or self.pallet_length <= 0:
            return False

        # 严格检查新位置是否在托盘边界内
        if (new_x < 0 or new_y < 0 or
            new_x + box_to_update.width > self.pallet_width or
            new_y + box_to_update.length > self.pallet_length):
            # 限制新位置在托盘边界内
            new_x = max(0, min(self.pallet_width - box_to_update.width, new_x))
            new_y = max(0, min(self.pallet_length - box_to_update.length, new_y))

        # 更新位置 - 允许重叠
        old_x, old_y = box_to_update.x, box_to_update.y
        box_to_update.x = new_x
        box_to_update.y = new_y

        return True



    def get_box_by_id(self, box_id):
        """根据ID获取箱子"""
        for box in self.boxes:
            if box.id == box_id:
                return box
        return None
    
    def rotate_box(self, box_id, clockwise=True):
        """
        旋转箱子90度

        Args:
            box_id: 箱子ID
            clockwise: 是否顺时针旋转，默认为True
                      True: 顺时针旋转90度
                      False: 逆时针旋转90度

        Returns:
            bool: 旋转是否成功
        """
        # 获取要旋转的箱子
        box_to_rotate = None
        for box in self.boxes:
            if box.id == box_id:
                box_to_rotate = box
                break

        if not box_to_rotate:
            return False

        # 保存原始状态
        original_x = box_to_rotate.x
        original_y = box_to_rotate.y
        original_width = box_to_rotate.width
        original_length = box_to_rotate.length
        original_rotated = box_to_rotate.rotated
        original_rotation_angle = box_to_rotate.rotation_angle

        # 计算箱子中心点坐标（作为旋转基点）
        center_x = box_to_rotate.x + box_to_rotate.width / 2
        center_y = box_to_rotate.y + box_to_rotate.length / 2

        # 旋转箱子（交换宽度和长度）
        box_to_rotate.width, box_to_rotate.length = box_to_rotate.length, box_to_rotate.width

        # 更新旋转角度
        if clockwise:
            # 顺时针旋转90度
            box_to_rotate.rotation_angle = (box_to_rotate.rotation_angle + 90) % 360
        else:
            # 逆时针旋转90度
            box_to_rotate.rotation_angle = (box_to_rotate.rotation_angle - 90) % 360

        # 更新旋转状态（用于兼容性）
        box_to_rotate.rotated = (box_to_rotate.rotation_angle % 180) != 0

        # 根据新的尺寸重新计算位置，保持中心点不变
        box_to_rotate.x = center_x - box_to_rotate.width / 2
        box_to_rotate.y = center_y - box_to_rotate.length / 2

        # 只检查是否超出托盘边界，不检查重叠
        if (box_to_rotate.x < 0 or box_to_rotate.y < 0 or
            box_to_rotate.x + box_to_rotate.width > self.pallet_width or
            box_to_rotate.y + box_to_rotate.length > self.pallet_length):
            # 恢复原始状态
            box_to_rotate.x = original_x
            box_to_rotate.y = original_y
            box_to_rotate.width = original_width
            box_to_rotate.length = original_length
            box_to_rotate.rotated = original_rotated
            box_to_rotate.rotation_angle = original_rotation_angle
            return False

        # 旋转成功，不检查重叠
        return True
    
    def remove_box(self, box_id):
        """移除箱子"""
        self.boxes = [box for box in self.boxes if box.id != box_id]

    def remove_boxes(self, box_ids):
        """移除多个箱子"""
        if not box_ids:
            return
        self.boxes = [box for box in self.boxes if box.id not in box_ids]

    def update_boxes_position(self, box_ids, dx, dy):
        """
        批量更新多个箱子的位置，允许与其他箱子重叠

        Args:
            box_ids: 要更新的箱子ID列表
            dx: X方向的偏移量
            dy: Y方向的偏移量

        Returns:
            bool: 是否成功更新所有箱子位置
        """
        if not box_ids:
            return False

        # 首先检查所有箱子是否都能移动到新位置（边界检查）
        boxes_to_update = []
        for box_id in box_ids:
            box = self.get_box_by_id(box_id)
            if not box:
                continue

            new_x = box.x + dx
            new_y = box.y + dy

            # 检查是否超出托盘边界
            if (new_x < 0 or new_y < 0 or
                new_x + box.width > self.pallet_width or
                new_y + box.length > self.pallet_length):
                # 如果任何一个箱子会超出边界，则不移动任何箱子
                return False

            boxes_to_update.append((box, new_x, new_y))

        # 如果所有箱子都能移动，则执行移动
        for box, new_x, new_y in boxes_to_update:
            box.x = new_x
            box.y = new_y

        return True

    def rotate_boxes(self, box_ids, clockwise=True):
        """
        批量旋转多个箱子90度

        Args:
            box_ids: 要旋转的箱子ID列表
            clockwise: 是否顺时针旋转，默认为True
                      True: 顺时针旋转90度
                      False: 逆时针旋转90度

        Returns:
            bool: 是否成功旋转所有箱子
        """
        if not box_ids:
            return False

        # 逐个旋转箱子，如果任何一个失败则停止
        failed_boxes = []
        for box_id in box_ids:
            if not self.rotate_box(box_id, clockwise):
                failed_boxes.append(box_id)

        # 如果有失败的箱子，返回False
        return len(failed_boxes) == 0
    
    def clear_boxes(self):
        """清空所有箱子"""
        self.boxes = []
    
    def get_box_by_id(self, box_id):
        """通过ID获取箱子"""
        for box in self.boxes:
            if box.id == box_id:
                return box
        return None

    def get_utilization_rate(self):
        """计算托盘利用率"""
        total_box_area = sum(box.width * box.length for box in self.boxes)
        pallet_area = self.pallet_width * self.pallet_length
        return total_box_area / pallet_area * 100 if pallet_area > 0 else 0
    
    def to_dict(self):
        """转换为字典, 用于JSON序列化"""
        return {
            "name": self.name,
            "pallet_width": self.pallet_width,
            "pallet_length": self.pallet_length,
            "box_width": self.box_width,
            "box_length": self.box_length,
            "gap": self.gap,
            "auto_mode": self.auto_mode,
            "rows": self.rows,
            "cols": self.cols,
            "row_spacing": self.row_spacing,
            "col_spacing": self.col_spacing,
            "boxes": [box.to_dict() for box in self.boxes]
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建PalletPattern实例"""
        pattern = cls(
            data["name"],
            data["pallet_width"],
            data["pallet_length"],
            data["box_width"],
            data["box_length"],
            data["gap"],
            data["auto_mode"],
            data.get("rows", 3),
            data.get("cols", 3),
            data.get("row_spacing", 0),
            data.get("col_spacing", 0)
        )
        
        for box_data in data["boxes"]:
            box = BoxItem.from_dict(box_data)
            pattern.boxes.append(box)  # 直接添加，不检查重叠（假设保存的数据是有效的）
        
        return pattern


class PatternManager:
    """模式管理器，用于管理多个托盘模式"""
    
    def __init__(self):
        self.patterns = []
        self.current_pattern_index = -1

    def get_patterns(self):
        """获取模式"""
        return self.patterns

    def add_pattern(self, pattern):
        """添加新模式"""
        self.patterns.append(pattern)
        self.current_pattern_index = len(self.patterns) - 1
        return self.current_pattern_index
    
    def remove_pattern(self, index):
        """移除模式，并重新命名后续方案"""
        if 0 <= index < len(self.patterns):
            # 删除指定索引的方案
            del self.patterns[index]

            # 重新命名所有方案，确保名称连续（方案 1, 方案 2, 方案 3...）
            self._rename_patterns_sequentially()

            # 调整当前方案索引
            if self.current_pattern_index >= len(self.patterns):
                self.current_pattern_index = len(self.patterns) - 1
            elif self.current_pattern_index > index:
                # 如果当前索引在删除的索引之后，需要减1
                self.current_pattern_index -= 1

            return True
        return False
    
    def _rename_patterns_sequentially(self):
        """重新命名所有方案，确保名称连续"""
        for i, pattern in enumerate(self.patterns):
            # A-Z 支持最多26个方案
            pattern.name = chr(ord('A') + i)

    def get_current_pattern(self):
        """获取当前模式"""
        if 0 <= self.current_pattern_index < len(self.patterns):
            return self.patterns[self.current_pattern_index]
        return None
    
    def set_current_pattern(self, index):
        """设置当前模式"""
        if 0 <= index < len(self.patterns):
            self.current_pattern_index = index
            return True
        return False
    
    def next_plan(self):
        """切换到下一个方案"""
        if not self.patterns:
            return False
        
        next_index = (self.current_pattern_index + 1) % len(self.patterns)
        self.current_pattern_index = next_index
        return True
    
    def prev_plan(self):
        """切换到上一个方案"""
        if not self.patterns:
            return False
        
        prev_index = (self.current_pattern_index - 1) % len(self.patterns)
        self.current_pattern_index = prev_index
        return True
    
    def ensure_pattern_at_index(self, index, name=None):
        """确保指定索引处有一个方案，如果没有则创建"""
        # 扩展列表以确保有足够的位置
        while len(self.patterns) <= index:
            if name is None:
                # 使用字母命名方案
                pattern_index = len(self.patterns)
                pattern_name = {chr(ord('A') + pattern_index)}
            else:
                pattern_name = name

            self.patterns.append(PalletPattern(pattern_name, 1200, 1200, 300, 200, 10))
        return self.patterns[index]


class SmartPalletPlanner:
    """智能托盘规划器，用于自动优化箱子布局"""
    
    def __init__(self, pallet_size, box_size, gap=0, rotation=True):
        """
        初始化托盘规划器
        
        Args:
            pallet_size: 托盘尺寸 (宽, 长)
            box_size: 箱子尺寸 (宽, 长)
            gap: 箱子间隙
            rotation: 是否允许箱子旋转
        """
        self.pallet_width, self.pallet_length = pallet_size
        self.box_width, self.box_length = box_size
        self.gap = gap
        self.rotation = rotation
    

    def calculate_max_layout(self, row_spacing=0, col_spacing=0):
        """
        计算给定间距下的最大行列数

        Args:
            row_spacing: 行间距
            col_spacing: 列间距

        Returns:
            tuple: (max_rows, max_cols, 是否可以放置)
        """
        # 检查基本条件
        if (self.pallet_width <= 0 or self.pallet_length <= 0 or
            self.box_width <= 0 or self.box_length <= 0):
            return (0, 0, False)

        # 检查单个箱子是否能放下
        if self.box_width > self.pallet_width or self.box_length > self.pallet_length:
            return (0, 0, False)

        # 计算最大列数
        # 公式: cols * box_width + (cols - 1) * col_spacing <= pallet_width
        # 即: cols * (box_width + col_spacing) - col_spacing <= pallet_width
        # 即: cols <= (pallet_width + col_spacing) / (box_width + col_spacing)
        if col_spacing == 0:
            max_cols = int(self.pallet_width // self.box_width)
        else:
            max_cols = int((self.pallet_width + col_spacing) // (self.box_width + col_spacing))

        # 计算最大行数
        # 公式: rows * box_length + (rows - 1) * row_spacing <= pallet_length
        if row_spacing == 0:
            max_rows = int(self.pallet_length // self.box_length)
        else:
            max_rows = int((self.pallet_length + row_spacing) // (self.box_length + row_spacing))

        # 确保至少为0
        max_rows = max(0, max_rows)
        max_cols = max(0, max_cols)

        # 验证计算结果
        can_place = max_rows > 0 and max_cols > 0

        if can_place:
            # 双重验证：检查实际占用空间
            actual_width = max_cols * self.box_width + (max_cols - 1) * col_spacing
            actual_length = max_rows * self.box_length + (max_rows - 1) * row_spacing

            if actual_width > self.pallet_width or actual_length > self.pallet_length:
                # 如果超出，减少一行或一列
                if actual_width > self.pallet_width:
                    max_cols = max(0, max_cols - 1)
                if actual_length > self.pallet_length:
                    max_rows = max(0, max_rows - 1)
                can_place = max_rows > 0 and max_cols > 0

        return (max_rows, max_cols, can_place)

    def generate_manual_layout(self, rows, cols, row_spacing, col_spacing):
        """
        生成手动布局，按行列均匀分布

        Args:
            rows: 行数
            cols: 列数
            row_spacing: 行间距
            col_spacing: 列间距

        Returns:
            boxes: 生成的箱子列表
        """
        # 首先检查是否能放下指定的行列数
        max_rows, max_cols, can_place = self.calculate_max_layout(row_spacing, col_spacing)

        if not can_place:
            return []

        if rows > max_rows or cols > max_cols:
            rows = min(rows, max_rows)
            cols = min(cols, max_cols)

        # 计算可用空间
        available_width = self.pallet_width - (cols - 1) * col_spacing
        available_length = self.pallet_length - (rows - 1) * row_spacing

        # 计算每个箱子的实际尺寸
        box_width = min(self.box_width, available_width / cols)
        box_length = min(self.box_length, available_length / rows)
        
        # 计算起始位置，使箱子居中放置
        start_x = (self.pallet_width - (cols * box_width + (cols - 1) * col_spacing)) / 2
        start_y = (self.pallet_length - (rows * box_length + (rows - 1) * row_spacing)) / 2
        
        # 生成箱子列表
        boxes = []
        box_id = 1
        
        # 从右下角开始，先沿X轴正方向（向左）再沿Y轴正方向（向上）编号
        for row in range(rows):
            for col in range(cols):
                # 计算箱子位置 - 右下角为原点，x轴正方向向左，y轴正方向向上
                # 先计算列位置（从右向左）
                x = self.pallet_width - start_x - box_width - col * (box_width + col_spacing)
                # 再计算行位置（从下向上）
                y = self.pallet_length - start_y - box_length - row * (box_length + row_spacing)
                
                box = BoxItem(
                    f"box_{box_id}",
                    x,
                    y,
                    box_width,
                    box_length,
                    False
                )
                boxes.append(box)
                box_id += 1
        
        return boxes


