from PySide6.QtWidgets import QWidget,  QPushButton, QCheckBox, QHBoxLayout, QWidget as QCellWidget, \
    QTableWidgetItem, QInputDialog, QMessageBox
from PySide6.QtCore import Signal, Qt
from src.common.sql_lite_tool import SqliteTool
from src.ui.Ui_formula import Ui_formula
from src.logic.layerStyle_logic import FormulaLayerStyleLogic


class FormulaLogic(QWidget,Ui_formula):

    edit_signal = Signal(int)

    def __init__(self, parent=None):
        super(FormulaLogic, self).__init__(parent)
        self.setupUi(self)

        self.db_tool = SqliteTool.get_instance()

        self.formula_table.setRowCount(0)

        # 设置表头文本，在适当位置添加换行符
        headers = ["序号", "配方\n名称", "箱子\n长度", "箱子\n宽度", "箱子\n高度",
                  "托盘X\n方向长度", "托盘Y\n方向长度", "托盘\n高度", "左垛\n层数", "右垛\n层数", "操作"]
        for i, header in enumerate(headers):
            header_item = QTableWidgetItem(header)
            # 设置表头文本居中对齐
            header_item.setTextAlignment(Qt.AlignCenter)
            self.formula_table.setHorizontalHeaderItem(i, header_item)

        # 设置表格属性，允许文本换行
        self.formula_table.setWordWrap(True)

        # 初始列宽设置（将在adjust_table_layout中重新计算）
        # 这里设置基础比例，实际宽度会根据容器宽度动态调整
        # pass

        # 设置表头高度，增加高度以适应换行文本
        self.formula_table.horizontalHeader().setDefaultSectionSize(80)
        self.formula_table.horizontalHeader().setFixedHeight(80)

        # 隐藏垂直表头
        self.formula_table.verticalHeader().setVisible(False)

        # 移除固定宽度限制，让表格自适应容器宽度
        self.formula_table.setMaximumSize(16777215, 357)  # 移除宽度限制，保留高度限制

        # 设置表格水平滚动条策略
        self.formula_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # 设置默认行高，增加高度以适应换行文本
        self.formula_table.verticalHeader().setDefaultSectionSize(60)

        # 设置所有现有行的高度
        for i in range(self.formula_table.rowCount()):
            self.formula_table.setRowHeight(i, 60)

        # 设置表格样式
        self.setup_table_style()

        # 调整表格布局以适应容器
        self.adjust_table_layout()

        # 设置列的编辑权限
        self.setup_column_edit_permissions()

        # 在操作栏添加按钮
        self.add_operation_buttons()
        # 添加新行按钮
        self.add_btn.clicked.connect(self.add_new_row)

    def resizeEvent(self, event):
        """窗口大小改变时重新调整表格布局"""
        super().resizeEvent(event)
        # 延迟调整，确保布局已经更新
        from PySide6.QtCore import QTimer
        QTimer.singleShot(10, self.adjust_table_layout)

    def setup_table_style(self):
        """设置表格样式"""
        style = """
        QTableWidget {
            font-size: 12px;
            background-color: #FFFFFF;
            alternate-background-color: #f9f9f9;
            border: 1px solid #FFFFFF;
            
        }

        QTableWidget::item {
            font-size: 12px;
            font-weight: bold;
            background-color: #FFFFFF;
            text-align: center;
            word-wrap: break-word;
            white-space: normal;
        }

        QTableWidget::item:selected {
            background-color: #FFFFFF;
            color: #000;
        }

        QHeaderView::section {
            font-size: 12px;
            font-weight: bold;
            padding: 6px 4px;
            background-color: #f5f5f5;
            border: 1px solid #FFFFFF;
            text-align: center;
            word-wrap: break-word;
            white-space: normal;
        }
        """
        self.formula_table.setStyleSheet(style)

    def adjust_table_layout(self):
        """调整表格布局以适应容器"""
        # 获取父容器的宽度
        parent_width = self.formula_table.parent().width() if self.formula_table.parent() else 800

        # 设置表格宽度为父容器宽度减去边距
        table_width = parent_width - 40  # 左右各20px边距
        self.formula_table.setFixedWidth(table_width)

        # 重新计算列宽，确保总宽度等于表格宽度
        total_columns = self.formula_table.columnCount()
        if total_columns > 0:
            # 列宽权重分配（总权重100）
            # 序号(6), 配方名称(12), 箱子长度(8), 箱子宽度(8), 箱子高度(8),
            # 托盘X方向长度(12), 托盘Y方向长度(12), 托盘高度(8), 左垛层数(8), 右垛层数(8), 操作(18)
            column_weights = [6, 6, 6, 6, 6, 10, 10, 6, 6, 6, 32]
            total_weight = sum(column_weights)

            # 可用宽度（减去边框等）
            available_width = table_width - 20

            # 按权重分配列宽
            for i, weight in enumerate(column_weights):
                if i < total_columns:
                    column_width = int((available_width * weight) / total_weight)
                    # 设置最小宽度限制
                    min_width = 40 if i != len(column_weights)-1 else 100  # 操作列最小100px
                    column_width = max(column_width, min_width)
                    self.formula_table.setColumnWidth(i, column_width)

    def add_operation_buttons(self):
        """在操作栏的每个单元格中添加三个按钮"""
        for row in range(self.formula_table.rowCount()):
            # 创建容器widget
            cell_widget = QCellWidget()
            layout = QHBoxLayout(cell_widget)
            layout.setContentsMargins(2, 2, 2, 2)  # 设置小的边距
            layout.setSpacing(2)  # 设置按钮间距

            # 设置水平布局居中对齐
            from PySide6.QtCore import Qt
            layout.setAlignment(Qt.AlignCenter)

            # 创建编辑按钮
            edit_btn = QPushButton("编辑")
            edit_btn.setObjectName("edit_btn_{}".format(row))
            edit_btn.setFixedSize(60, 30)
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)

            edit_btn.clicked.connect(lambda checked, r=row: self.edit_row(r))

            # 创建删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.setObjectName("delete_btn_{}".format(row))
            delete_btn.setFixedSize(60, 30)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #F44336;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #D32F2F;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.delete_row(r))

            # 创建复选框
            check_box = QCheckBox()
            check_box.setFixedSize(30, 30)
            check_box.setObjectName("check_box_{}".format(row))
            check_box.setStyleSheet("""
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                    border-radius: 9px;
                    border: 2px solid #ccc;
                }
                QCheckBox::indicator:checked {
                    background-color: #4CAF50;
                    border: 2px solid #4CAF50;
                }
            """)
            # check_box.stateChanged.connect(lambda state, r=row: self.check_row(r, state))

            # 将按钮添加到水平布局
            layout.addWidget(edit_btn)
            layout.addWidget(delete_btn)
            layout.addWidget(check_box)

            # 将容器设置到表格的操作列（第11列，索引为10）
            self.formula_table.setCellWidget(row, 10, cell_widget)

    def edit_row(self, row):
        """编辑按钮点击事件 - 跳转到层样式界面编辑配方"""
        try:
            # 获取配方名称
            formula_name_item = self.formula_table.item(row, 1)  # 第二列是配方名称
            if not formula_name_item:
                QMessageBox.warning(self, "编辑失败", "无法获取配方名称")
                return

            formula_name = formula_name_item.text()

            # 查找配方ID
            formulas = self.db_tool.select("formulas", condition="formula_name=?", params=[formula_name])
            if not formulas:
                QMessageBox.warning(self, "编辑失败", f"未找到配方 '{formula_name}'")
                return

            formula_id = formulas[0]['id']

            # 设置当前编辑的配方ID（用于其他界面获取）
            self.current_editing_formula_id = formula_id
            self.current_editing_formula_name = formula_name

            # 发出编辑信号，传递配方ID和名称
            self.edit_signal.emit(row)

        except Exception as e:
            QMessageBox.critical(self, "编辑错误", f"编辑配方时发生异常：{str(e)}")

    def delete_row(self, row):
        """删除按钮点击事件 - 删除配方及其所有关联数据"""
        try:
            # 获取配方名称
            formula_name_item = self.formula_table.item(row, 1)  # 第二列是配方名称
            if not formula_name_item:
                QMessageBox.warning(self, "删除失败", "无法获取配方名称")
                return

            formula_name = formula_name_item.text()

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除配方 '{formula_name}' 吗？\n\n此操作将删除该配方的所有相关数据（箱子/托盘参数、层样式配置、垛型配置），且无法恢复。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 查找配方ID
            formulas = self.db_tool.select("formulas", condition="formula_name=?", params=[formula_name])
            if not formulas:
                QMessageBox.warning(self, "删除失败", f"未找到配方 '{formula_name}'")
                return

            formula_id = formulas[0]['id']

            # 删除配方（由于外键约束设置了级联删除，相关数据会自动删除）
            success = self.db_tool.delete("formulas", f"id={formula_id}")

            if success:
                # 刷新表格显示
                self.load_formulas_from_database()
                QMessageBox.information(self, "删除成功", f"配方 '{formula_name}' 及其所有相关数据已删除")
            else:
                QMessageBox.critical(self, "删除失败", f"删除配方 '{formula_name}' 时发生错误")

        except Exception as e:
            QMessageBox.critical(self, "删除错误", f"删除配方时发生异常：{str(e)}")

    def add_new_row(self):
        """添加新行并在数据库中创建完整的配方数据"""
        # 弹出窗口，添加配方名称
        dialog = QInputDialog(self)
        dialog.setWindowTitle("新增配方")
        dialog.setLabelText("请输入配方名称:")
        dialog.setTextValue("")
        dialog.setOkButtonText("确定")
        dialog.setCancelButtonText("取消")

        if not dialog.exec() == QInputDialog.Accepted:
            return

        formula_name = dialog.textValue().strip()
        if not formula_name:
            QMessageBox.warning(self, "输入错误", "配方名称不能为空")
            return

        try:
            # 检查配方名称是否已存在
            existing_formulas = self.db_tool.select("formulas", condition="formula_name=?", params=[formula_name])
            if existing_formulas:
                QMessageBox.warning(self, "配方已存在", f"配方名称 '{formula_name}' 已存在，请使用其他名称")
                return

            # 在数据库中创建新配方
            formula_data = {
                'formula_name': formula_name,
                'description': f'{formula_name} - 新建配方',
                'created_at': self.get_current_timestamp(),
                'updated_at': self.get_current_timestamp()
            }

            success = self.db_tool.insert('formulas', formula_data)
            if not success:
                QMessageBox.critical(self, "创建失败", "创建配方失败，请重试")
                return

            # 获取新创建的配方ID
            new_formulas = self.db_tool.select("formulas", condition="formula_name=?", params=[formula_name])
            if not new_formulas:
                QMessageBox.critical(self, "创建失败", "无法获取新创建的配方ID")
                return

            formula_id = new_formulas[0]['id']

            # 创建完整的默认配方数据，确保数据一致性
            success_count = 0
            total_operations = 3

            # 创建默认的箱子/托盘参数
            if self.create_default_box_pallet_params(formula_id):
                success_count += 1
            else:
                QMessageBox.warning(self, "创建警告", "箱子/托盘参数创建失败")

            # 创建默认的层样式配置
            if self.create_default_layer_style_config(formula_id):
                success_count += 1
            else:
                QMessageBox.warning(self, "创建警告", "层样式配置创建失败")

            # 创建默认的垛型配置
            if self.create_default_stack_config(formula_id):
                success_count += 1
            else:
                QMessageBox.warning(self, "创建警告", "垛型配置创建失败")

            # 检查是否所有关联数据都创建成功
            if success_count == total_operations:
                # 刷新表格显示
                self.load_formulas_from_database()
                QMessageBox.information(
                    self,
                    "创建成功",
                    f"配方 '{formula_name}'创建成功！\n\n"
                    f"您可以点击'编辑'按钮进行详细配置。"
                )
            else:
                QMessageBox.warning(
                    self,
                    "创建不完整",
                    f"配方 '{formula_name}' 创建成功，但部分关联数据创建失败。\n"
                    f"成功创建：{success_count}/{total_operations} 项\n"
                    f"请检查数据库连接或重新创建配方。"
                )

        except Exception as e:
            QMessageBox.critical(self, "创建错误", f"创建配方时发生异常：{str(e)}")

    def update_row_numbers(self):
        """更新所有行的序号"""
        for row in range(self.formula_table.rowCount()):
            item = self.formula_table.item(row, 0)  # 第一列
            if item:
                item.setText(str(row + 1))
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # 确保序号列不可编辑

    def setup_column_edit_permissions(self):
        """设置列的编辑权限"""
        for row in range(self.formula_table.rowCount()):
            for col in range(self.formula_table.columnCount() - 1):  # 除了最后一列（操作列）
                item = self.formula_table.item(row, col)
                if item:
                    if col == 0:  # 第一列：序号，不可编辑
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    elif col == 1:  # 第二列：配方名称，可编辑
                        item.setFlags(item.flags() | Qt.ItemIsEditable)
                    else:  # 其他列：不可编辑
                        item.setFlags(item.flags() & ~Qt.ItemIsEditable)

    def load_formulas_from_database(self):
        """从数据库加载配方数据并填入表格，同时检查数据一致性"""
        try:
            # 清空现有表格数据
            self.formula_table.setRowCount(0)

            # 查询所有配方及其关联数据（按创建时间排序）
            formulas = self.db_tool.select("formulas")

            if not formulas:
                return

            # 检查和修复数据一致性
            self.check_and_fix_data_consistency(formulas)

            for row_index, formula in enumerate(formulas):
                formula_id = formula['id']
                formula_name = formula['formula_name']

                # 获取箱子/托盘参数
                box_pallet_params = self.db_tool.select(
                    "box_pallet_params",
                    condition="formula_id=?",
                    params=[formula_id]
                )

                # 获取垛型配置
                stack_config = self.db_tool.select(
                    "stack_type_configs",
                    condition="formula_id=?",
                    params=[formula_id]
                )

                # 插入新行
                self.formula_table.insertRow(row_index)
                self.formula_table.setRowHeight(row_index, 50)

                # 填充数据
                self.set_table_item(row_index, 0, str(row_index + 1))  # 序号
                self.set_table_item(row_index, 1, formula_name)  # 配方名称

                if box_pallet_params:
                    params = box_pallet_params[0]
                    self.set_table_item(row_index, 2, str(params.get('box_length', 0)))  # 箱子长度
                    self.set_table_item(row_index, 3, str(params.get('box_width', 0)))   # 箱子宽度
                    self.set_table_item(row_index, 4, str(params.get('box_height', 0)))  # 箱子高度
                    self.set_table_item(row_index, 5, str(params.get('pallet_length', 0)))  # 托盘X方向长度
                    self.set_table_item(row_index, 6, str(params.get('pallet_width', 0)))   # 托盘Y方向长度
                    self.set_table_item(row_index, 7, str(params.get('pallet_height', 0)))  # 托盘高度
                else:
                    # 如果没有参数数据，填入默认值
                    for col in range(2, 8):
                        self.set_table_item(row_index, col, "0")

                if stack_config:
                    config = stack_config[0]
                    self.set_table_item(row_index, 8, str(config.get('left_stack_layers', 1)))   # 左垛层数
                    self.set_table_item(row_index, 9, str(config.get('right_stack_layers', 1)))  # 右垛层数
                else:
                    # 如果没有垛型配置，填入默认值
                    self.set_table_item(row_index, 8, "1")  # 左垛层数
                    self.set_table_item(row_index, 9, "1")  # 右垛层数

            # 重新添加操作按钮
            self.add_operation_buttons()

            # 重新设置列的编辑权限
            self.setup_column_edit_permissions()

        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"从数据库加载配方数据时发生异常：{str(e)}")

    def set_table_item(self, row, col, text):
        """设置表格单元格内容"""
        item = QTableWidgetItem(str(text))
        item.setTextAlignment(Qt.AlignCenter)

        # 设置编辑权限
        if col == 0:  # 序号列不可编辑
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)
        elif col == 1:  # 配方名称列可编辑
            item.setFlags(item.flags() | Qt.ItemIsEditable)
        else:  # 其他列不可编辑
            item.setFlags(item.flags() & ~Qt.ItemIsEditable)

        self.formula_table.setItem(row, col, item)

    def refresh_table_data(self):
        """刷新表格数据"""
        self.load_formulas_from_database()

    def showEvent(self, event):
        """窗口显示时自动加载数据"""
        super().showEvent(event)
        # 延迟加载数据，确保界面已经完全初始化
        from PySide6.QtCore import QTimer
        QTimer.singleShot(100, self.load_formulas_from_database)

    def create_default_box_pallet_params(self, formula_id):
        """为新配方创建默认的箱子/托盘参数 - 所有值默认为0"""
        default_params = {
            'formula_id': formula_id,
            'box_length': 0.0,      # 箱子长度默认为0
            'box_width': 0.0,       # 箱子宽度默认为0
            'box_height': 0.0,      # 箱子高度默认为0
            'box_weight': 0.0,      # 箱子重量默认为0
            'pallet_length': 0.0,   # 托盘长度默认为0
            'pallet_width': 0.0,    # 托盘宽度默认为0
            'pallet_height': 0.0,   # 托盘高度默认为0
            'created_at': self.get_current_timestamp(),
            'updated_at': self.get_current_timestamp()
        }
        success = self.db_tool.insert('box_pallet_params', default_params)
        if success:
            print(f"为配方ID {formula_id} 创建默认箱子/托盘参数：所有值为0")
        return success

    def create_default_layer_style_config(self, formula_id):
        """为新配方创建默认的层样式配置 - 所有值默认为0"""
        import json

        # 默认的层样式数据 - 所有值为0
        default_pattern_data = {
            'name': 'A',
            'pallet_width': 0,      # 托盘宽度默认为0
            'pallet_length': 0,     # 托盘长度默认为0
            'box_width': 0,         # 箱子宽度默认为0
            'box_length': 0,        # 箱子长度默认为0
            'gap': 0,               # 间隙默认为0
            'auto_mode': True,      # 自动模式保持为True
            'boxes': []             # 空的箱子列表
        }

        default_config = {
            'formula_id': formula_id,
            'layer_index': 1,       # 默认创建第1层
            'pattern_data': json.dumps(default_pattern_data, ensure_ascii=False),
            'auto_mode': 1,         # 自动模式默认开启
            'gap_size': 0.0,        # 间隙大小默认为0
            'rows_count': 0,        # 行数默认为0
            'cols_count': 0,        # 列数默认为0
            'row_spacing': 0.0,     # 行间距默认为0
            'col_spacing': 0.0,     # 列间距默认为0
            'created_at': self.get_current_timestamp(),
            'updated_at': self.get_current_timestamp()
        }
        success = self.db_tool.insert('layer_style_configs', default_config)
        if success:
            print(f"为配方ID {formula_id} 创建默认层样式配置：所有值为0")
        return success

    def create_default_stack_config(self, formula_id):
        """为新配方创建默认的垛型配置 - 所有值默认为0，左右一致和重复模式默认关闭"""
        default_config = {
            'formula_id': formula_id,
            'left_stack_layers': 0,         # 左垛层数默认为0
            'right_stack_layers': 0,        # 右垛层数默认为0
            'left_repeat_enabled': 0,       # 左垛重复模式默认关闭
            'right_repeat_enabled': 0,      # 右垛重复模式默认关闭
            'left_right_consistent': 0,     # 左右一致默认关闭
            'left_stack_layer_styles': '',  # 左垛层样式选择默认为空
            'right_stack_layer_styles': '', # 右垛层样式选择默认为空
            'created_at': self.get_current_timestamp(),
            'updated_at': self.get_current_timestamp()
        }
        success = self.db_tool.insert('stack_type_configs', default_config)
        if success:
            print(f"为配方ID {formula_id} 创建默认垛型配置：层数为0，重复和一致性关闭")
        return success

    def get_current_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

    def check_and_fix_data_consistency(self, formulas):
        """检查并修复配方数据的一致性"""
        try:
            inconsistent_formulas = []

            for formula in formulas:
                formula_id = formula['id']
                formula_name = formula['formula_name']

                # 检查每个配方的关联数据完整性
                missing_data = []

                # 检查箱子/托盘参数
                box_params = self.db_tool.select("box_pallet_params", condition="formula_id=?", params=[formula_id])
                if not box_params:
                    missing_data.append("箱子/托盘参数")

                # 检查层样式配置
                layer_configs = self.db_tool.select("layer_style_configs", condition="formula_id=?", params=[formula_id])
                if not layer_configs:
                    missing_data.append("层样式配置")

                # 检查垛型配置
                stack_configs = self.db_tool.select("stack_type_configs", condition="formula_id=?", params=[formula_id])
                if not stack_configs:
                    missing_data.append("垛型配置")

                # 如果有缺失数据，记录并尝试修复
                if missing_data:
                    inconsistent_formulas.append({
                        'id': formula_id,
                        'name': formula_name,
                        'missing': missing_data
                    })

                    # 自动创建缺失的默认数据
                    if "箱子/托盘参数" in missing_data:
                        self.create_default_box_pallet_params(formula_id)
                        print(f"为配方 '{formula_name}' 创建了默认箱子/托盘参数")

                    if "层样式配置" in missing_data:
                        self.create_default_layer_style_config(formula_id)
                        print(f"为配方 '{formula_name}' 创建了默认层样式配置")

                    if "垛型配置" in missing_data:
                        self.create_default_stack_config(formula_id)
                        print(f"为配方 '{formula_name}' 创建了默认垛型配置")

            # 如果有不一致的配方，显示修复信息
            if inconsistent_formulas:
                missing_info = []
                for formula_info in inconsistent_formulas:
                    missing_info.append(f"• {formula_info['name']}: {', '.join(formula_info['missing'])}")

                print(f"检测到 {len(inconsistent_formulas)} 个配方的数据不完整，已自动修复：")
                for info in missing_info:
                    print(f"  {info}")

        except Exception as e:
            print(f"数据一致性检查失败: {e}")

    def validate_formula_data_integrity(self):
        """验证所有配方数据的完整性"""
        try:
            formulas = self.db_tool.select("formulas")
            if not formulas:
                return True, "没有配方数据"

            total_formulas = len(formulas)
            complete_formulas = 0
            issues = []

            for formula in formulas:
                formula_id = formula['id']
                formula_name = formula['formula_name']

                # 检查关联数据
                box_params = self.db_tool.select("box_pallet_params", condition="formula_id=?", params=[formula_id])
                layer_configs = self.db_tool.select("layer_style_configs", condition="formula_id=?", params=[formula_id])
                stack_configs = self.db_tool.select("stack_type_configs", condition="formula_id=?", params=[formula_id])

                if box_params and layer_configs and stack_configs:
                    complete_formulas += 1
                else:
                    missing = []
                    if not box_params:
                        missing.append("箱子/托盘参数")
                    if not layer_configs:
                        missing.append("层样式配置")
                    if not stack_configs:
                        missing.append("垛型配置")
                    issues.append(f"{formula_name}: 缺少 {', '.join(missing)}")

            if complete_formulas == total_formulas:
                return True, f"所有 {total_formulas} 个配方的数据完整"
            else:
                return False, f"数据完整性问题：\n" + "\n".join(issues)

        except Exception as e:
            return False, f"验证失败: {e}"

