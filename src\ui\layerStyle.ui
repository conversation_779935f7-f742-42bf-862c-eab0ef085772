<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>formula_layer_style</class>
 <widget class="QWidget" name="formula_layer_style">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>871</width>
    <height>500</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>561</width>
    <height>413</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="font">
   <font>
    <pointsize>6</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>智能托盘规划工具</string>
  </property>
  <widget class="QFrame" name="frame">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>20</y>
     <width>839</width>
     <height>50</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>839</width>
     <height>50</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>839</width>
     <height>50</height>
    </size>
   </property>
   <property name="frameShape">
    <enum>QFrame::Shape::StyledPanel</enum>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Shadow::Raised</enum>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout_3">
    <item>
     <widget class="QPushButton" name="boxpallet_btn">
      <property name="minimumSize">
       <size>
        <width>200</width>
        <height>32</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>200</width>
        <height>32</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">background-image: url(:/layerstyle/layerstyle/button.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
color: rgb(255, 255, 255);</string>
      </property>
      <property name="text">
       <string>箱子/托盘</string>
      </property>
      <property name="checkable">
       <bool>true</bool>
      </property>
      <property name="checked">
       <bool>true</bool>
      </property>
      <property name="autoExclusive">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="laystyle_btn">
      <property name="minimumSize">
       <size>
        <width>200</width>
        <height>32</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>200</width>
        <height>32</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="text">
       <string>层样式</string>
      </property>
      <property name="checkable">
       <bool>true</bool>
      </property>
      <property name="autoExclusive">
       <bool>true</bool>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="stacktype_btn">
      <property name="minimumSize">
       <size>
        <width>200</width>
        <height>32</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>200</width>
        <height>32</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="text">
       <string>垛型</string>
      </property>
      <property name="checkable">
       <bool>true</bool>
      </property>
      <property name="checked">
       <bool>false</bool>
      </property>
      <property name="autoExclusive">
       <bool>true</bool>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStackedWidget" name="stackedWidget">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>90</y>
     <width>839</width>
     <height>411</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>839</width>
     <height>34</height>
    </size>
   </property>
   <property name="currentIndex">
    <number>1</number>
   </property>
   <widget class="QWidget" name="page_layer">
    <widget class="QFrame" name="frame_4">
     <property name="geometry">
      <rect>
       <x>280</x>
       <y>-10</y>
       <width>561</width>
       <height>413</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>561</width>
       <height>413</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>561</width>
       <height>413</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QWidget" name="PalletVisualization" native="true">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>20</y>
        <width>338</width>
        <height>382</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>338</width>
        <height>382</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">background-image: url(:/layerstyle/layerstyle/pallet.png);</string>
      </property>
     </widget>
     <widget class="QFrame" name="frame_9">
      <property name="geometry">
       <rect>
        <x>350</x>
        <y>280</y>
        <width>201</width>
        <height>95</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <widget class="QWidget" name="layoutWidget">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>0</y>
         <width>182</width>
         <height>92</height>
        </rect>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_5">
          <item>
           <widget class="QPushButton" name="top_alignment">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/layerstyle/layerstyle/dingdq.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;

</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="bottom_alignment">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/layerstyle/layerstyle/didq.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;

</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="left_alignment">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/layerstyle/layerstyle/zdq.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;

</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="right_alignment">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/layerstyle/layerstyle/ydq.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;

</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_6">
          <item>
           <widget class="QPushButton" name="horizontal_distribution">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/layerstyle/layerstyle/spfb.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;

</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="vertical_distribution">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/layerstyle/layerstyle/czfb.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;

</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="horizontal_flip">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/layerstyle/layerstyle/spfz.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;

</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="Vertical_flip">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>40</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">background-image: url(:/layerstyle/layerstyle/czfz.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;

</string>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>360</x>
        <y>380</y>
        <width>181</width>
        <height>31</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_7">
       <item>
        <widget class="QRadioButton" name="select_multiple_box">
         <property name="font">
          <font>
           <pointsize>-1</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">font-size:12px</string>
         </property>
         <property name="text">
          <string>多选</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="save_plan">
         <property name="minimumSize">
          <size>
           <width>48</width>
           <height>26</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>48</width>
           <height>26</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-image: url(:/layerstyle/layerstyle/save.png);
width: 48px;
height: 26px;
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QFrame" name="frame_12">
      <property name="geometry">
       <rect>
        <x>350</x>
        <y>70</y>
        <width>200</width>
        <height>211</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>200</width>
        <height>150</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <widget class="QPushButton" name="down_btn">
       <property name="geometry">
        <rect>
         <x>80</x>
         <y>110</y>
         <width>50</width>
         <height>50</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>50</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>40</width>
         <height>40</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/down.png);</string>
       </property>
       <property name="text">
        <string>↓</string>
       </property>
      </widget>
      <widget class="QPushButton" name="right_btn">
       <property name="geometry">
        <rect>
         <x>120</x>
         <y>70</y>
         <width>50</width>
         <height>50</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>50</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>40</width>
         <height>40</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/right.png);</string>
       </property>
       <property name="text">
        <string>→</string>
       </property>
      </widget>
      <widget class="QPushButton" name="left_btn">
       <property name="geometry">
        <rect>
         <x>40</x>
         <y>70</y>
         <width>50</width>
         <height>50</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>50</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>40</width>
         <height>40</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-image: url(:/layerstyle/layerstyle/left.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;</string>
       </property>
       <property name="text">
        <string>←</string>
       </property>
      </widget>
      <widget class="QPushButton" name="up_btn">
       <property name="geometry">
        <rect>
         <x>80</x>
         <y>40</y>
         <width>50</width>
         <height>50</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>50</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>40</width>
         <height>40</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-image: url(:/layerstyle/layerstyle/up.png);
background-color: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;

</string>
       </property>
       <property name="text">
        <string>↑</string>
       </property>
      </widget>
      <widget class="QPushButton" name="rotate_btn_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>150</y>
         <width>50</width>
         <height>50</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>50</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>40</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/turnright.png);</string>
       </property>
       <property name="text">
        <string>↻</string>
       </property>
      </widget>
      <widget class="QPushButton" name="rotate_btn">
       <property name="geometry">
        <rect>
         <x>140</x>
         <y>150</y>
         <width>50</width>
         <height>50</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>50</width>
         <height>50</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>40</width>
         <height>40</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/turnleft.png);</string>
       </property>
       <property name="text">
        <string>↺</string>
       </property>
      </widget>
      <widget class="QWidget" name="layoutWidget_3">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>0</y>
         <width>189</width>
         <height>36</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QLabel" name="label_15">
          <property name="minimumSize">
           <size>
            <width>60</width>
            <height>12</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>60</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 12px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
          </property>
          <property name="text">
           <string>步长:mm</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="step_spin">
          <property name="minimumSize">
           <size>
            <width>121</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>100</width>
            <height>26</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>350</x>
        <y>30</y>
        <width>201</width>
        <height>36</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_9">
       <item>
        <widget class="QPushButton" name="add_box_btn">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="minimumSize">
          <size>
           <width>85</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>85</width>
           <height>34</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/add.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="delete_box_btn">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="minimumSize">
          <size>
           <width>85</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>85</width>
           <height>34</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/remove.png);
</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QFrame" name="frame_10">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>268</width>
       <height>226</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>268</width>
       <height>226</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QWidget" name="plan_widget_2" native="true">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>10</y>
        <width>248</width>
        <height>171</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: #FFFFFF;</string>
      </property>
      <widget class="QLabel" name="label_plan">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>10</y>
         <width>180</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>180</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>180</width>
         <height>30</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>-1</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color: #0851A0;
border-radius: 15px 15px 15px 15px;
color:#FFFFFF;
text-align:center;
font-size:16px</string>
       </property>
       <property name="text">
        <string>A</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignCenter</set>
       </property>
      </widget>
      <widget class="QWidget" name="layoutWidget_2">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>90</y>
         <width>241</width>
         <height>22</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_8">
        <item>
         <widget class="QPushButton" name="previous_plan">
          <property name="minimumSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color:#07509F;
color:#FFFFFF
</string>
          </property>
          <property name="text">
           <string>&lt;</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="next_plan">
          <property name="minimumSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-color:#07509F;
color:#FFFFFF
</string>
          </property>
          <property name="text">
           <string>&gt;</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QFrame" name="plan_vision">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>50</y>
         <width>201</width>
         <height>111</height>
        </rect>
       </property>
       <property name="frameShape">
        <enum>QFrame::Shape::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Shadow::Raised</enum>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>180</y>
        <width>241</width>
        <height>41</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <widget class="QPushButton" name="new_pattern_btn">
         <property name="minimumSize">
          <size>
           <width>65</width>
           <height>35</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>65</width>
           <height>35</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">width:65px;
height: 35px;
background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/addplan.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="copy_pattern_btn">
         <property name="minimumSize">
          <size>
           <width>65</width>
           <height>35</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>65</width>
           <height>35</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">width:65px;
height: 35px;
background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/copy.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="delete_pattern_btn">
         <property name="minimumSize">
          <size>
           <width>65</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>65</width>
           <height>34</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">width:65px;
height: 35px;
background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/removeplan.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QFrame" name="frame_11">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>230</y>
       <width>268</width>
       <height>171</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>268</width>
       <height>171</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>268</width>
       <height>171</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QWidget" name="widget" native="true">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>30</y>
        <width>250</width>
        <height>130</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>250</width>
        <height>130</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>250</width>
        <height>130</height>
       </size>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="lines_number_4">
         <property name="font">
          <font>
           <pointsize>-1</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">font-size:12px</string>
         </property>
         <property name="text">
          <string>行数</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLabel" name="colu_number_4">
         <property name="font">
          <font>
           <pointsize>-1</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">font-size:12px
</string>
         </property>
         <property name="text">
          <string>列数</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLineEdit" name="rows_spin_3">
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>26</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>100</width>
           <height>26</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QLineEdit" name="cols_spin_3">
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>26</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>100</width>
           <height>26</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="line_spacing_4">
         <property name="font">
          <font>
           <pointsize>-1</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">font-size:12px</string>
         </property>
         <property name="text">
          <string>行间距:mm</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QLabel" name="column_spacing_4">
         <property name="font">
          <font>
           <pointsize>-1</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">font-size:12px</string>
         </property>
         <property name="text">
          <string>列间距:mm</string>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLineEdit" name="row_spacing_spin_3">
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>26</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>100</width>
           <height>26</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="col_spacing_spin_3">
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>26</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>100</width>
           <height>26</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>10</y>
        <width>231</width>
        <height>36</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QComboBox" name="comboBox">
         <property name="minimumSize">
          <size>
           <width>67</width>
           <height>26</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>26</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="generate_btn">
         <property name="minimumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/layerstyle/layerstyle/gen.png);

</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </widget>
   <widget class="QWidget" name="page_box">
    <widget class="QFrame" name="frame_2">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>-10</y>
       <width>409</width>
       <height>413</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>409</width>
       <height>413</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>409</width>
       <height>413</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QWidget" name="layoutWidget_7">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>10</y>
        <width>391</width>
        <height>36</height>
       </rect>
      </property>
      <layout class="QGridLayout" name="gridLayout_7">
       <item row="0" column="2">
        <widget class="QPushButton" name="save_btn_l">
         <property name="minimumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/boxpallet/boxpallet/save.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="box_size">
         <property name="minimumSize">
          <size>
           <width>65</width>
           <height>16</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>65</width>
           <height>16</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>MiSans-Semibold</family>
           <pointsize>-1</pointsize>
           <fontweight>DemiBold</fontweight>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 16px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
         </property>
         <property name="text">
          <string>箱子参数</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <spacer name="horizontalSpacer_7">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Policy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>200</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QFrame" name="frame_box">
      <property name="geometry">
       <rect>
        <x>60</x>
        <y>50</y>
        <width>283</width>
        <height>167</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>283</width>
        <height>167</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>283</width>
        <height>167</height>
       </size>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LayoutDirection::LeftToRight</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/boxpallet/boxpallet/box.png);

</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
     </widget>
     <widget class="QWidget" name="">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>230</y>
        <width>354</width>
        <height>171</height>
       </rect>
      </property>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0">
        <layout class="QVBoxLayout" name="verticalLayout_6">
         <item>
          <widget class="QLabel" name="label">
           <property name="font">
            <font>
             <family>MiSans-Semibold</family>
             <pointsize>-1</pointsize>
             <fontweight>DemiBold</fontweight>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 12px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
           </property>
           <property name="text">
            <string>来料方向</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="box_direction">
           <property name="minimumSize">
            <size>
             <width>350</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>350</width>
             <height>30</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QcomboBox::up-button, QcomboBox::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QcomboBox::up-arrow, QcomboBox::down-arrow {
    image: none;
}
QcomboBox {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
           </property>
           <item>
            <property name="text">
             <string>横向</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>纵向</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <layout class="QGridLayout" name="gridLayout_2">
         <item row="0" column="0">
          <widget class="QLabel" name="label_6">
           <property name="font">
            <font>
             <family>MiSans-Semibold</family>
             <pointsize>-1</pointsize>
             <fontweight>DemiBold</fontweight>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 12px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
           </property>
           <property name="text">
            <string>长度:mm</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="label_11">
           <property name="font">
            <font>
             <family>MiSans-Semibold</family>
             <pointsize>-1</pointsize>
             <fontweight>DemiBold</fontweight>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 12px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
           </property>
           <property name="text">
            <string>宽度:mm</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLineEdit" name="length_box">
           <property name="minimumSize">
            <size>
             <width>160</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>160</width>
             <height>30</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLineEdit" name="width_box">
           <property name="minimumSize">
            <size>
             <width>160</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>160</width>
             <height>30</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="hight_textEdit">
           <property name="font">
            <font>
             <family>MiSans-Semibold</family>
             <pointsize>-1</pointsize>
             <fontweight>DemiBold</fontweight>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 12px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
           </property>
           <property name="text">
            <string>高度:mm</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLabel" name="label_12">
           <property name="font">
            <font>
             <family>MiSans-Semibold</family>
             <pointsize>-1</pointsize>
             <fontweight>DemiBold</fontweight>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 12px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
           </property>
           <property name="text">
            <string>重量:kg</string>
           </property>
          </widget>
         </item>
         <item row="3" column="0">
          <widget class="QLineEdit" name="hight_box">
           <property name="minimumSize">
            <size>
             <width>160</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>160</width>
             <height>30</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
         <item row="3" column="1">
          <widget class="QLineEdit" name="weight_box">
           <property name="minimumSize">
            <size>
             <width>160</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>160</width>
             <height>30</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignmentFlag::AlignCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QFrame" name="frame_3">
     <property name="geometry">
      <rect>
       <x>430</x>
       <y>-10</y>
       <width>409</width>
       <height>413</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>409</width>
       <height>413</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>10000</width>
       <height>10000</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QFrame" name="frame_pallet">
      <property name="geometry">
       <rect>
        <x>60</x>
        <y>50</y>
        <width>283</width>
        <height>167</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>283</width>
        <height>167</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>283</width>
        <height>167</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/boxpallet/boxpallet/pallet.png);

</string>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>29</x>
        <y>234</y>
        <width>352</width>
        <height>170</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_13">
       <item>
        <widget class="QLabel" name="label_13">
         <property name="font">
          <font>
           <family>MiSans-Semibold</family>
           <pointsize>9</pointsize>
           <fontweight>DemiBold</fontweight>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string>长度:mm</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="length_pallet">
         <property name="minimumSize">
          <size>
           <width>350</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>350</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="width">
         <property name="font">
          <font>
           <family>MiSans-Semibold</family>
           <pointsize>-1</pointsize>
           <fontweight>DemiBold</fontweight>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 12px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
         </property>
         <property name="text">
          <string>宽度:mm</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="width_pallet">
         <property name="minimumSize">
          <size>
           <width>350</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>350</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_18">
         <property name="font">
          <font>
           <family>MiSans-Semibold</family>
           <pointsize>-1</pointsize>
           <fontweight>DemiBold</fontweight>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 12px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
         </property>
         <property name="text">
          <string>高度:mm</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="hight_pallet">
         <property name="minimumSize">
          <size>
           <width>350</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>350</width>
           <height>30</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QLineEdit::up-button, QLineEdit::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QLineEdit::up-arrow, QLineEdit::down-arrow {
    image: none;
}
QLineEdit {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignmentFlag::AlignCenter</set>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>11</y>
        <width>391</width>
        <height>36</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_10">
       <item>
        <widget class="QLabel" name="stack_label_r">
         <property name="minimumSize">
          <size>
           <width>65</width>
           <height>16</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>65</width>
           <height>16</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>MiSans-Semibold</family>
           <pointsize>-1</pointsize>
           <fontweight>DemiBold</fontweight>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 16px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
         </property>
         <property name="text">
          <string>托盘参数</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_8">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Policy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>200</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="save_btn_r">
         <property name="minimumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/boxpallet/boxpallet/save.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </widget>
   <widget class="QWidget" name="page_stype">
    <widget class="QFrame" name="frame_5">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>-10</y>
       <width>411</width>
       <height>413</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>410</width>
       <height>413</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>100000</width>
       <height>413</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QCheckBox" name="Repright_check_l">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>105</y>
        <width>50</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>重复</string>
      </property>
     </widget>
     <widget class="QLabel" name="layer_label_17">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>48</y>
        <width>24</width>
        <height>12</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>24</width>
        <height>12</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>24</width>
        <height>12</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>层数</string>
      </property>
     </widget>
     <widget class="QSpinBox" name="stack_layer_spinBox_l">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>66</y>
        <width>375</width>
        <height>30</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>375</width>
        <height>30</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>375</width>
        <height>30</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>-1</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QSpinBox::up-button, QSpinBox::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QSpinBox::up-arrow, QSpinBox::down-arrow {
    image: none;
}
QSpinBox {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
      </property>
      <property name="suffix">
       <string/>
      </property>
      <property name="minimum">
       <number>0</number>
      </property>
     </widget>
     <widget class="QCheckBox" name="stack_lrl_check">
      <property name="geometry">
       <rect>
        <x>70</x>
        <y>102</y>
        <width>70</width>
        <height>26</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>70</width>
        <height>26</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>70</width>
        <height>26</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>左右一致</string>
      </property>
     </widget>
     <widget class="QScrollArea" name="scrollArea_l">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>130</y>
        <width>390</width>
        <height>270</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>390</width>
        <height>270</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>100000</width>
        <height>100000</height>
       </size>
      </property>
      <property name="widgetResizable">
       <bool>true</bool>
      </property>
      <widget class="QWidget" name="scrollAreaWidgetContents_4">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>388</width>
         <height>268</height>
        </rect>
       </property>
       <layout class="QFormLayout" name="formLayout_3"/>
      </widget>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>10</y>
        <width>381</width>
        <height>36</height>
       </rect>
      </property>
      <layout class="QGridLayout" name="gridLayout_10">
       <item row="0" column="1">
        <spacer name="horizontalSpacer_10">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="0">
        <widget class="QLabel" name="stack_label_r_3">
         <property name="minimumSize">
          <size>
           <width>63</width>
           <height>16</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>63</width>
           <height>16</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>MiSans-Semibold</family>
           <pointsize>-1</pointsize>
           <fontweight>DemiBold</fontweight>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 16px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
         </property>
         <property name="text">
          <string>左垛垛型</string>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QPushButton" name="stack_save_btn_r">
         <property name="minimumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/boxpallet/boxpallet/save.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QFrame" name="frame_6">
     <property name="geometry">
      <rect>
       <x>430</x>
       <y>-10</y>
       <width>411</width>
       <height>413</height>
      </rect>
     </property>
     <property name="minimumSize">
      <size>
       <width>410</width>
       <height>413</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>100000</width>
       <height>100000</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::Shape::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Shadow::Raised</enum>
     </property>
     <widget class="QSpinBox" name="stack_layer_spinBox_r">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>66</y>
        <width>375</width>
        <height>30</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>375</width>
        <height>30</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>375</width>
        <height>30</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>-1</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">QSpinBox::up-button, QSpinBox::down-button {
    width: 0px;
    padding: 0px;
    border: none;
}
QSpinBox::up-arrow, QSpinBox::down-arrow {
    image: none;
}
QSpinBox {
    font-size: 12px;
    background-color: #E2EAF5;
    border-radius: 13px 13px 13px 13px;
}
</string>
      </property>
      <property name="suffix">
       <string/>
      </property>
      <property name="minimum">
       <number>0</number>
      </property>
     </widget>
     <widget class="QLabel" name="layer_label_18">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>48</y>
        <width>24</width>
        <height>12</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>24</width>
        <height>12</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>24</width>
        <height>12</height>
       </size>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>层数</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="Repright_check_r">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>102</y>
        <width>50</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>重复</string>
      </property>
     </widget>
     <widget class="QCheckBox" name="stack_lrr_check">
      <property name="geometry">
       <rect>
        <x>70</x>
        <y>102</y>
        <width>74</width>
        <height>20</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <pointsize>9</pointsize>
       </font>
      </property>
      <property name="text">
       <string>左右一致</string>
      </property>
     </widget>
     <widget class="QScrollArea" name="scrollArea_r">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>130</y>
        <width>390</width>
        <height>270</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>390</width>
        <height>270</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>100000</width>
        <height>100000</height>
       </size>
      </property>
      <property name="widgetResizable">
       <bool>true</bool>
      </property>
      <widget class="QWidget" name="scrollAreaWidgetContents_2">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>388</width>
         <height>268</height>
        </rect>
       </property>
       <layout class="QFormLayout" name="formLayout"/>
      </widget>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>10</y>
        <width>381</width>
        <height>36</height>
       </rect>
      </property>
      <layout class="QGridLayout" name="gridLayout_11">
       <item row="0" column="0">
        <widget class="QLabel" name="stack_label_r_4">
         <property name="minimumSize">
          <size>
           <width>63</width>
           <height>16</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>63</width>
           <height>16</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>MiSans-Semibold</family>
           <pointsize>-1</pointsize>
           <fontweight>DemiBold</fontweight>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">/* QLabel 的全局样式 */
QLabel {
    font-family: &quot;MiSans-Semibold&quot;; /* 字体 */
    font-weight: 600;              /* 字重：Semibold 对应 600 */
    color: #000000;                /* 文本颜色：黑色 */
    font-size: 16px;               /* 字号 */
    line-height: 14px;             /* 行高 */
    text-align: left;              /* 水平对齐：左对齐 */
    vertical-align: middle;        /* 垂直对齐：垂直居中 */
}</string>
         </property>
         <property name="text">
          <string>右垛垛型</string>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QPushButton" name="stack_save_btn_l">
         <property name="minimumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>56</width>
           <height>34</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
background-image: url(:/boxpallet/boxpallet/save.png);</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <spacer name="horizontalSpacer_11">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </widget>
  </widget>
 </widget>
 <resources>
  <include location="../resources/formula/formula.qrc"/>
 </resources>
 <connections/>
</ui>
