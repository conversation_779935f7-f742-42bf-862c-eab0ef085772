<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fe3765f8-9970-4bb1-9be0-619728619ef4" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/src/resources/fetching/保存.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/resources/fetching/保存@2x.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/resources/fetching/提示.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/resources/fetching/提示@2x.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/resources/fetching/示教点位.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/resources/fetching/示教点位@2x.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/resources/fetching/返回示教点.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/resources/fetching/返回示教点@2x.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/ui/teachingPage_fetching111.ui" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/main.py" beforeDir="false" afterPath="$PROJECT_DIR$/main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/logic/main_windows.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/logic/main_windows.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/logic/teaching_logic.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/logic/teaching_logic.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/resources/fetching/resources_fetching.qrc" beforeDir="false" afterPath="$PROJECT_DIR$/src/resources/fetching/resources_fetching.qrc" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/resources/fetching/resources_fetching_rc.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/resources/fetching/resources_fetching_rc.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/resources/main_window/resources_rc.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/resources/main_window/resources_rc.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/resources/main_window/窗口.png" beforeDir="false" afterPath="$PROJECT_DIR$/src/resources/main_window/窗口.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/ui/Ui_teachingPage_fetching.py" beforeDir="false" afterPath="$PROJECT_DIR$/src/ui/Ui_teachingPage_fetching.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/ui/teachingPage_fetching.ui" beforeDir="false" afterPath="$PROJECT_DIR$/src/ui/teachingPage_fetching.ui" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 8
}]]></component>
  <component name="ProjectId" id="30GP6zLFCX9uUjGGZ5ZIWjVi0f3" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.Ui_teachingPage_fetching.executor": "Run",
    "Python.main.executor": "Run",
    "Python.main_windows.executor": "Run",
    "Python.teaching_logic.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "dev",
    "last_opened_file_path": "D:/PycharmCode/vscode/palletizer1/palletizer/src/ui",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\PycharmCode\vscode\palletizer1\palletizer\src\ui" />
      <recent name="D:\PycharmCode\vscode\palletizer1\palletizer\src\resources\main_window" />
      <recent name="D:\PycharmCode\vscode\palletizer1\palletizer\src\resources" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-PY-241.14494.241" />
        <option value="bundled-python-sdk-0509580d9d50-28c9f5db9ffe-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.14494.241" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="fe3765f8-9970-4bb1-9be0-619728619ef4" name="更改" comment="" />
      <created>1753250765187</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753250765187</updated>
      <workItem from="1753250766283" duration="7496000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/palletizer$main_windows.coverage" NAME="main_windows 覆盖结果" MODIFIED="1753256022227" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/logic" />
    <SUITE FILE_PATH="coverage/palletizer$teaching_logic.coverage" NAME="teaching_logic 覆盖结果" MODIFIED="1753257726371" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/logic" />
    <SUITE FILE_PATH="coverage/palletizer$Ui_teachingPage_fetching.coverage" NAME="Ui_teachingPage_fetching 覆盖结果" MODIFIED="1753251082966" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/ui" />
    <SUITE FILE_PATH="coverage/palletizer$main.coverage" NAME="main 覆盖结果" MODIFIED="1753257734614" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>