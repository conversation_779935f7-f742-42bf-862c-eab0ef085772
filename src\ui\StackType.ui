<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>stack_type</class>
 <widget class="QWidget" name="stack_type">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>842</width>
    <height>510</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QWidget" name="">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>10</y>
     <width>841</width>
     <height>491</height>
    </rect>
   </property>
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0" colspan="2">
     <widget class="QFrame" name="frame">
      <property name="minimumSize">
       <size>
        <width>839</width>
        <height>50</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>839</width>
        <height>50</height>
       </size>
      </property>
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QPushButton" name="pushButton_3">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>50</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="text">
          <string>箱子/托盘</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_4">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>50</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="text">
          <string>层样式</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_5">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>50</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="text">
          <string>垛型</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="0">
     <widget class="QFrame" name="frame_2">
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <widget class="QLabel" name="layer_label">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>70</y>
         <width>24</width>
         <height>12</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="text">
        <string>层数</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="layer_spinBox_l">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>100</y>
         <width>375</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>375</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>375</width>
         <height>30</height>
        </size>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
      </widget>
      <widget class="QSpinBox" name="layer_spinBox_2">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>220</y>
         <width>171</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
      </widget>
      <widget class="QLabel" name="layer_label_l1">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>200</y>
         <width>24</width>
         <height>12</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="text">
        <string>层1</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="layer_spinBox_3">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>220</y>
         <width>171</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
      </widget>
      <widget class="QLabel" name="layer_label_l2">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>200</y>
         <width>24</width>
         <height>12</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="text">
        <string>层2</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="layer_spinBox_4">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>290</y>
         <width>171</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
      </widget>
      <widget class="QLabel" name="layer_label_l3">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>270</y>
         <width>24</width>
         <height>12</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="text">
        <string>层3</string>
       </property>
      </widget>
      <widget class="QLabel" name="layer_label_l4">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>270</y>
         <width>24</width>
         <height>12</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="text">
        <string>层4</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="layer_spinBox_5">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>290</y>
         <width>171</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
      </widget>
      <widget class="QSpinBox" name="layer_spinBox_6">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>370</y>
         <width>171</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
      </widget>
      <widget class="QLabel" name="layer_label_l5">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>350</y>
         <width>24</width>
         <height>12</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="text">
        <string>层5</string>
       </property>
      </widget>
      <widget class="QLabel" name="layer_label_l6">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>350</y>
         <width>24</width>
         <height>12</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>24</width>
         <height>12</height>
        </size>
       </property>
       <property name="text">
        <string>层6</string>
       </property>
      </widget>
      <widget class="QSpinBox" name="layer_spinBox_7">
       <property name="geometry">
        <rect>
         <x>220</x>
         <y>370</y>
         <width>171</width>
         <height>30</height>
        </rect>
       </property>
       <property name="minimumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>171</width>
         <height>30</height>
        </size>
       </property>
       <property name="suffix">
        <string/>
       </property>
       <property name="minimum">
        <number>0</number>
       </property>
      </widget>
      <widget class="QCheckBox" name="Repleft_check">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>160</y>
         <width>82</width>
         <height>20</height>
        </rect>
       </property>
       <property name="text">
        <string>重复</string>
       </property>
      </widget>
      <widget class="QCheckBox" name="lrl_check">
       <property name="geometry">
        <rect>
         <x>130</x>
         <y>160</y>
         <width>82</width>
         <height>20</height>
        </rect>
       </property>
       <property name="text">
        <string>左右一致</string>
       </property>
      </widget>
      <widget class="QWidget" name="">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>20</y>
         <width>381</width>
         <height>32</height>
        </rect>
       </property>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="2">
         <widget class="QPushButton" name="save_btn_l">
          <property name="minimumSize">
           <size>
            <width>52</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>52</width>
            <height>30</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>保存</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="stack_label_l">
          <property name="minimumSize">
           <size>
            <width>63</width>
            <height>16</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>63</width>
            <height>16</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">text-align: center;</string>
          </property>
          <property name="text">
           <string>左垛垛型</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item row="1" column="1">
     <widget class="QFrame" name="frame_3">
      <property name="frameShape">
       <enum>QFrame::Shape::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Shadow::Raised</enum>
      </property>
      <widget class="QFrame" name="frame_4">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>417</width>
         <height>433</height>
        </rect>
       </property>
       <property name="frameShape">
        <enum>QFrame::Shape::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Shadow::Raised</enum>
       </property>
       <widget class="QLabel" name="layer_label_15">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>70</y>
          <width>24</width>
          <height>12</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="text">
         <string>层数</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="layer_spinBox_r">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>100</y>
          <width>375</width>
          <height>30</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>375</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>375</width>
          <height>30</height>
         </size>
        </property>
        <property name="suffix">
         <string/>
        </property>
        <property name="minimum">
         <number>0</number>
        </property>
       </widget>
       <widget class="QCheckBox" name="Repright_check">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>160</y>
          <width>82</width>
          <height>20</height>
         </rect>
        </property>
        <property name="text">
         <string>重复</string>
        </property>
       </widget>
       <widget class="QCheckBox" name="lrr_check">
        <property name="geometry">
         <rect>
          <x>130</x>
          <y>160</y>
          <width>82</width>
          <height>20</height>
         </rect>
        </property>
        <property name="text">
         <string>左右一致</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="layer_spinBox_16">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>220</y>
          <width>171</width>
          <height>30</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="suffix">
         <string/>
        </property>
        <property name="minimum">
         <number>0</number>
        </property>
       </widget>
       <widget class="QLabel" name="layer_label_r1">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>200</y>
          <width>24</width>
          <height>12</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="text">
         <string>层1</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="layer_spinBox_17">
        <property name="geometry">
         <rect>
          <x>220</x>
          <y>220</y>
          <width>171</width>
          <height>30</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="suffix">
         <string/>
        </property>
        <property name="minimum">
         <number>0</number>
        </property>
       </widget>
       <widget class="QLabel" name="layer_label_r2">
        <property name="geometry">
         <rect>
          <x>220</x>
          <y>200</y>
          <width>24</width>
          <height>12</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="text">
         <string>层2</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="layer_spinBox_18">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>290</y>
          <width>171</width>
          <height>30</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="suffix">
         <string/>
        </property>
        <property name="minimum">
         <number>0</number>
        </property>
       </widget>
       <widget class="QLabel" name="layer_label_r3">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>270</y>
          <width>24</width>
          <height>12</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="text">
         <string>层3</string>
        </property>
       </widget>
       <widget class="QLabel" name="layer_label_r4">
        <property name="geometry">
         <rect>
          <x>220</x>
          <y>270</y>
          <width>24</width>
          <height>12</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="text">
         <string>层4</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="layer_spinBox_19">
        <property name="geometry">
         <rect>
          <x>220</x>
          <y>290</y>
          <width>171</width>
          <height>30</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="suffix">
         <string/>
        </property>
        <property name="minimum">
         <number>0</number>
        </property>
       </widget>
       <widget class="QSpinBox" name="layer_spinBox_20">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>370</y>
          <width>171</width>
          <height>30</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="suffix">
         <string/>
        </property>
        <property name="minimum">
         <number>0</number>
        </property>
       </widget>
       <widget class="QLabel" name="layer_label_r5">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>350</y>
          <width>24</width>
          <height>12</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="text">
         <string>层5</string>
        </property>
       </widget>
       <widget class="QLabel" name="layer_label_r6">
        <property name="geometry">
         <rect>
          <x>220</x>
          <y>350</y>
          <width>24</width>
          <height>12</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>24</width>
          <height>12</height>
         </size>
        </property>
        <property name="text">
         <string>层6</string>
        </property>
       </widget>
       <widget class="QSpinBox" name="layer_spinBox_21">
        <property name="geometry">
         <rect>
          <x>220</x>
          <y>370</y>
          <width>171</width>
          <height>30</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>171</width>
          <height>30</height>
         </size>
        </property>
        <property name="suffix">
         <string/>
        </property>
        <property name="minimum">
         <number>0</number>
        </property>
       </widget>
       <widget class="QWidget" name="layoutWidget_2">
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>20</y>
          <width>381</width>
          <height>32</height>
         </rect>
        </property>
        <layout class="QGridLayout" name="gridLayout_4">
         <item row="0" column="2">
          <widget class="QPushButton" name="save_btn_r">
           <property name="minimumSize">
            <size>
             <width>52</width>
             <height>30</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>52</width>
             <height>30</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="text">
            <string>保存</string>
           </property>
          </widget>
         </item>
         <item row="0" column="0">
          <widget class="QLabel" name="stack_label_r">
           <property name="minimumSize">
            <size>
             <width>63</width>
             <height>16</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>63</width>
             <height>16</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">text-align: center;</string>
           </property>
           <property name="text">
            <string>右垛垛型</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
