<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>HomeWidget</class>
 <widget class="QWidget" name="HomeWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>858</width>
    <height>509</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>智能分拣系统 - 主页</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget {
    background-color: #e8f0ff;
    font-family: &quot;Microsoft YaHei&quot;, Arial, sans-serif;
}

QFrame {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #d0d7de;
}

QPushButton {
    border: none;
    border-radius: 8px;
    font-weight: bold;
    font-size: 12px;
    color: white;
}

QLabel {
    color: #2d3748;
    font-size: 12px;
}

.status-ready {
    background-color: #22c55e;
    color: white;
    border-radius: 12px;
    padding: 4px 12px;
    font-size: 11px;
    font-weight: bold;
}

.status-maintenance {
    background-color: #f59e0b;
    color: white;
    border-radius: 12px;
    padding: 4px 12px;
    font-size: 11px;
    font-weight: bold;
}

.count-large {
    font-size: 24px;
    font-weight: bold;
    color: #3b82f6;
}

.count-medium {
    font-size: 18px;
    font-weight: bold;
    color: #3b82f6;
}

.count-small {
    font-size: 14px;
    color: #6b7280;
}

.btn-ready {
    background-color: #22c55e;
    min-height: 32px;
}

.btn-maintenance {
    background-color: #f59e0b;
    min-height: 32px;
}

.btn-reset {
    background-color: #3b82f6;
    min-height: 32px;
}

.btn-start {
    background-color: #3b82f6;
    min-height: 36px;
}

.btn-stop {
    background-color: #ef4444;
    min-height: 36px;
}

.btn-pause {
    background-color: #f59e0b;
    min-height: 36px;
}

.btn-custom {
    background-color: #06b6d4;
    min-height: 36px;
}

.btn-system-reset {
    background-color: #6b7280;
    min-height: 36px;
}

.toggle-on {
    background-color: #22c55e;
    border-radius: 12px;
    min-height: 24px;
    min-width: 48px;
}

.toggle-off {
    background-color: #d1d5db;
    border-radius: 12px;
    min-height: 24px;
    min-width: 48px;
}

.robot-workspace {
    border: 2px dashed #9ca3af;
    background-color: #f9fafb;
    border-radius: 8px;
}</string>
  </property>
  <layout class="QVBoxLayout" name="mainVerticalLayout">
   <property name="spacing">
    <number>12</number>
   </property>
   <property name="leftMargin">
    <number>12</number>
   </property>
   <property name="topMargin">
    <number>12</number>
   </property>
   <property name="rightMargin">
    <number>12</number>
   </property>
   <property name="bottomMargin">
    <number>12</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="topControlLayout">
     <property name="spacing">
      <number>12</number>
     </property>
     <property name="sizeConstraint">
      <enum>QLayout::SizeConstraint::SetDefaultConstraint</enum>
     </property>
     <item>
      <widget class="QFrame" name="leftConveyorFrame">
       <property name="minimumSize">
        <size>
         <width>260</width>
         <height>230</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>260</width>
         <height>300</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="leftConveyorMainLayout">
        <property name="spacing">
         <number>8</number>
        </property>
        <property name="leftMargin">
         <number>12</number>
        </property>
        <property name="topMargin">
         <number>12</number>
        </property>
        <property name="rightMargin">
         <number>12</number>
        </property>
        <property name="bottomMargin">
         <number>12</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="leftTitleStatusLayout">
          <item>
           <widget class="QLabel" name="leftConveyorTitleLabel">
            <property name="styleSheet">
             <string notr="true">font-size: 15px; font-weight: bold; color: #1f2937;</string>
            </property>
            <property name="text">
             <string>左垛盘</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="leftTitleSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="leftStatusButton">
            <property name="styleSheet">
             <string notr="true">background-color: #22c55e; color: white; border-radius: 13px; padding: 4px 12px; font-size: 11px; font-weight: bold; min-width: 40px;</string>
            </property>
            <property name="text">
             <string>就绪</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="leftCurrentStatsLayout">
          <item>
           <layout class="QVBoxLayout" name="leftCurrentLayerLayout">
            <item>
             <widget class="QLabel" name="leftCurrentLayerLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 12px; color: #6b7280;
border:0px;</string>
              </property>
              <property name="text">
               <string>当前层数</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="leftCurrentLayerValueLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 20px; font-weight: bold; color: #3b82f6;
border:0px;</string>
              </property>
              <property name="text">
               <string>1/12</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="leftCurrentBoxLayout">
            <item>
             <widget class="QLabel" name="leftCurrentBoxLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 12px; color: #6b7280;
border:0px;</string>
              </property>
              <property name="text">
               <string>当前层箱子数</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="leftCurrentBoxValueLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 20px; font-weight: bold; color: #3b82f6;
border:0px;</string>
              </property>
              <property name="text">
               <string>5</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="leftTotalStatsLayout">
          <item>
           <layout class="QVBoxLayout" name="leftTotalBoxLayout">
            <item>
             <widget class="QLabel" name="leftTotalBoxLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 12px; color: #6b7280;
border:0px;</string>
              </property>
              <property name="text">
               <string>当前总箱子数</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="leftTotalBoxValueLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 14px; font-weight: bold; color: #3b82f6;
border:0px;</string>
              </property>
              <property name="text">
               <string>5/120</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="leftClearanceLayout">
            <item>
             <widget class="QLabel" name="leftClearanceLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 12px; color: #6b7280;
border:0px;</string>
              </property>
              <property name="text">
               <string>取消码垛</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="leftClearanceToggle">
              <property name="minimumSize">
               <size>
                <width>48</width>
                <height>24</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: #d1d5db; border-radius: 12px; min-height: 24px; min-width: 48px;
background:url(:/run/run/OFF.png);
background-repeat: no-repeat;</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="leftControlButtonsLayout">
          <property name="spacing">
           <number>6</number>
          </property>
          <item>
           <widget class="QPushButton" name="leftReadyButton">
            <property name="styleSheet">
             <string notr="true">background-color: #22c55e; min-height: 28px; font-size: 13px;</string>
            </property>
            <property name="text">
             <string>就绪</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="leftMaintenanceButton">
            <property name="styleSheet">
             <string notr="true">background-color: #f59e0b; min-height: 28px; font-size: 13px;</string>
            </property>
            <property name="text">
             <string>强制满垛</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="leftResetButton">
            <property name="styleSheet">
             <string notr="true">background-color: #3b82f6; min-height: 28px; font-size: 13px;</string>
            </property>
            <property name="text">
             <string>重置</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="controlCenterFrame">
       <property name="minimumSize">
        <size>
         <width>280</width>
         <height>230</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>280</width>
         <height>300</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="controlCenterMainLayout">
        <property name="spacing">
         <number>8</number>
        </property>
        <property name="leftMargin">
         <number>12</number>
        </property>
        <property name="topMargin">
         <number>12</number>
        </property>
        <property name="rightMargin">
         <number>12</number>
        </property>
        <property name="bottomMargin">
         <number>12</number>
        </property>
        <item>
         <widget class="QLabel" name="controlCenterTitleLabel">
          <property name="styleSheet">
           <string notr="true">font-size: 16px; font-weight: bold; color: #1f2937;</string>
          </property>
          <property name="text">
           <string>控制中心</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <layout class="QGridLayout" name="mainControlButtonsLayout">
          <property name="spacing">
           <number>6</number>
          </property>
          <item row="0" column="0">
           <widget class="QPushButton" name="startButton">
            <property name="styleSheet">
             <string notr="true">min-height: 32px; font-size: 12px;
background-color: #3b82f6;</string>
            </property>
            <property name="text">
             <string>启动</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QPushButton" name="stopButton">
            <property name="styleSheet">
             <string notr="true">background-color: #ef4444; min-height: 32px; font-size: 12px;</string>
            </property>
            <property name="text">
             <string>停止</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QPushButton" name="pauseButton">
            <property name="styleSheet">
             <string notr="true">background-color: #f59e0b; min-height: 32px; font-size: 12px;</string>
            </property>
            <property name="text">
             <string>暂停</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0" colspan="2">
           <widget class="QPushButton" name="customAutoButton">
            <property name="styleSheet">
             <string notr="true">background-color: #06b6d4; min-height: 32px; font-size: 12px;</string>
            </property>
            <property name="text">
             <string>自定义启动</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QPushButton" name="systemResetButton">
            <property name="styleSheet">
             <string notr="true">background-color: #6b7280; min-height: 32px; font-size: 12px;</string>
            </property>
            <property name="text">
             <string>复位</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QGridLayout" name="parametersDisplayLayout">
          <property name="spacing">
           <number>8</number>
          </property>
          <item row="0" column="0">
           <widget class="QLabel" name="leftSpeedLabel">
            <property name="styleSheet">
             <string notr="true">font-size: 13px; font-weight: bold; color: #374151;
border:0px;</string>
            </property>
            <property name="text">
             <string>左垛: 10</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="rightSpeedLabel">
            <property name="styleSheet">
             <string notr="true">font-size: 13px; font-weight: bold; color: #374151;
border:0px;</string>
            </property>
            <property name="text">
             <string>右垛: 10</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="totalSpeedLabel">
            <property name="styleSheet">
             <string notr="true">font-size: 13px; font-weight: bold; color: #374151;
border:0px;</string>
            </property>
            <property name="text">
             <string>总垛数: 20</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="leftTotalLabel">
            <property name="styleSheet">
             <string notr="true">font-size: 13px; font-weight: bold; color: #374151;
border:0px;</string>
            </property>
            <property name="text">
             <string>左垛: 500</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="rightTotalLabel">
            <property name="styleSheet">
             <string notr="true">font-size: 13px; font-weight: bold; color: #374151;
border:0px;</string>
            </property>
            <property name="text">
             <string>右垛: 500</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="totalBoxCountLabel">
            <property name="styleSheet">
             <string notr="true">font-size: 13px; font-weight: bold; color: #374151;
border:0px;</string>
            </property>
            <property name="text">
             <string>总箱数: 1000</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QFrame" name="rightConveyorFrame">
       <property name="minimumSize">
        <size>
         <width>260</width>
         <height>230</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>260</width>
         <height>300</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="rightConveyorMainLayout">
        <property name="spacing">
         <number>8</number>
        </property>
        <property name="leftMargin">
         <number>12</number>
        </property>
        <property name="topMargin">
         <number>12</number>
        </property>
        <property name="rightMargin">
         <number>12</number>
        </property>
        <property name="bottomMargin">
         <number>12</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="rightTitleStatusLayout">
          <item>
           <widget class="QLabel" name="rightConveyorTitleLabel">
            <property name="styleSheet">
             <string notr="true">font-size: 15px; font-weight: bold; color: #1f2937;</string>
            </property>
            <property name="text">
             <string>右垛盘</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="rightTitleSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="rightStatusButton">
            <property name="styleSheet">
             <string notr="true">background-color: #f59e0b; color: white; border-radius: 13px; padding: 4px 12px; font-size: 11px; font-weight: bold; min-width: 40px;</string>
            </property>
            <property name="text">
             <string>满垛</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="rightCurrentStatsLayout">
          <item>
           <layout class="QVBoxLayout" name="rightCurrentLayerLayout">
            <item>
             <widget class="QLabel" name="rightCurrentLayerLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 12px; color: #6b7280;
border:0px;</string>
              </property>
              <property name="text">
               <string>当前层数</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="rightCurrentLayerValueLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 20px; font-weight: bold; color: #3b82f6;
border:0px;</string>
              </property>
              <property name="text">
               <string>12/12</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="rightCurrentBoxLayout">
            <item>
             <widget class="QLabel" name="rightCurrentBoxLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 12px; color: #6b7280;
border:0px;</string>
              </property>
              <property name="text">
               <string>当前层箱子数</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="rightCurrentBoxValueLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 20px; font-weight: bold; color: #3b82f6;
border:0px;</string>
              </property>
              <property name="text">
               <string>5</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="rightTotalStatsLayout">
          <item>
           <layout class="QVBoxLayout" name="rightTotalBoxLayout">
            <item>
             <widget class="QLabel" name="rightTotalBoxLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 12px; color: #6b7280;
border:0px;</string>
              </property>
              <property name="text">
               <string>当前总箱子数</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="rightTotalBoxValueLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 14px; font-weight: bold; color: #3b82f6;
border:0px;</string>
              </property>
              <property name="text">
               <string>120/120</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="rightClearanceLayout">
            <item>
             <widget class="QLabel" name="rightClearanceLabel">
              <property name="styleSheet">
               <string notr="true">font-size: 12px; color: #6b7280;
border:0px;</string>
              </property>
              <property name="text">
               <string>取消码垛</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="rightClearanceToggle">
              <property name="styleSheet">
               <string notr="true">background-color: #22c55e; border-radius: 12px; min-height: 24px; min-width: 48px;
background:url(:/run/run/ON.png);
background-repeat: no-repeat;</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="rightControlButtonsLayout">
          <property name="spacing">
           <number>6</number>
          </property>
          <item>
           <widget class="QPushButton" name="rightReadyButton">
            <property name="styleSheet">
             <string notr="true">background-color: #22c55e; min-height: 28px; font-size: 13px;</string>
            </property>
            <property name="text">
             <string>就绪</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="rightMaintenanceButton">
            <property name="styleSheet">
             <string notr="true">background-color: #f59e0b; min-height: 28px; font-size: 13px;</string>
            </property>
            <property name="text">
             <string>强制满垛</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="rightResetButton">
            <property name="styleSheet">
             <string notr="true">background-color: #3b82f6; min-height: 28px; font-size: 13px;</string>
            </property>
            <property name="text">
             <string>重置</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="robotWorkspaceLayout">
     <property name="spacing">
      <number>12</number>
     </property>
     <item>
      <widget class="QLabel" name="leftRobotWorkspaceLabel">
       <property name="minimumSize">
        <size>
         <width>260</width>
         <height>120</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>260</width>
         <height>270</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">border: 2px dashed #9ca3af; background-color: #f9fafb; border-radius: 8px; color: #6b7280; font-size: 12px;</string>
       </property>
       <property name="text">
        <string>左侧机器人工作区</string>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="centerRobotWorkspaceLabel">
       <property name="minimumSize">
        <size>
         <width>280</width>
         <height>120</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>280</width>
         <height>270</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">background-color: white; border-radius: 8px; color: #6b7280; font-size: 12px; border: 1px solid #d1d5db;</string>
       </property>
       <property name="text">
        <string>中央机器人工作区</string>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="rightRobotWorkspaceLabel">
       <property name="minimumSize">
        <size>
         <width>260</width>
         <height>120</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>260</width>
         <height>270</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">border: 2px dashed #9ca3af; background-color: #f9fafb; border-radius: 8px; color: #6b7280; font-size: 12px;</string>
       </property>
       <property name="text">
        <string>右侧机器人工作区</string>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
       <property name="alignment">
        <set>Qt::AlignmentFlag::AlignCenter</set>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../resources/images.qrc"/>
 </resources>
 <connections/>
</ui>
