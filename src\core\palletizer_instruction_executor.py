"""
码垛机器人指令序列执行器
基于焊接机器人指令序列逻辑，专门为码垛作业设计
"""

import time
import math
from typing import Dict, List
from PySide6.QtCore import QObject, Signal, QTimer
from ..core.robot_tcp_controller import Robot<PERSON>cpController
from ..core.palletizer_program import Command<PERSON>anager
from ..core.cmd_tool import RobotCommand
from ..logic import IO_logic
from ..common.log import Logger
from ..common import global_vars as gv
from ..common.robot_status import RobotStatus


class PalletizerInstructionExecutor(QObject):
    """码垛指令序列执行器"""

    # 信号定义
    execution_started = Signal(str)  # pallet_id
    execution_paused = Signal()
    execution_resumed = Signal()
    execution_stopped = Signal()
    execution_finished = Signal(str, bool, str)  # pallet_id, success, message
    execution_progress = Signal(str, int, int, int)  # pallet_id, layer, box_count, progress
    error_occurred = Signal(str, str, str)  # pallet_id, error_code, error_message
    status_changed = Signal(str)  # 状态变化

    def __init__(self):
        super().__init__()
        self.command_manager = CommandManager()
        self.robot_controller = RobotTcpController.get_instance()
        self.robot_status = RobotStatus()
        self.io_logic = IO_logic.IOLogic()

        # 执行状态
        self.is_running = False
        self.is_paused = False
        self.current_index = 0
        self.instruction_list = []
        self.current_box_gripped = False
        self.pause_position = None

        # 多垛盘执行状态跟踪
        self.active_pallets = {}  # {pallet_id: execution_state}
        self.current_executing_pallet = None

        # 逆运动学缓存
        self.inverse_kinematics_cache = {}

        # 临时测试数据
        self.temp_test_data = {
            'pallet': {'length': 900, 'width': 900, 'height': 20},
            'box': {'length': 100, 'width': 100, 'height': 100},
            'material_direction': 'default',
            'layers': 2,
            'layer_pattern': {
                'rows': 3, 'cols': 3, 'row_spacing': 20, 'col_spacing': 20
            }
        }

    def generate_instruction_preview(self, pallet_config, recipe_data, teaching_data, pallet_id=None):
        """生成指令序列预览（不执行）"""
        try:
            if pallet_id:
                Logger.info(f"开始生成垛盘 {pallet_id} 的指令序列预览")
            else:
                Logger.info("开始生成指令序列预览")

            # 如果提供了pallet_id，可以在pallet_config中添加该信息
            if pallet_id and pallet_config:
                pallet_config['pallet_id'] = pallet_id

            # 使用相同的逻辑生成指令序列
            instruction_sequence = self._generate_instruction_sequence(pallet_config, recipe_data, teaching_data)

            # 提取指令字符串列表
            preview_list = []
            for instruction in instruction_sequence:
                if isinstance(instruction, tuple) and len(instruction) >= 3:
                    cmd, box_index, description = instruction[0], instruction[1], instruction[2]
                    preview_list.append(f"{description}: {cmd}")
                else:
                    preview_list.append(str(instruction))

            if pallet_id:
                Logger.info(f"垛盘 {pallet_id} 指令序列预览生成完成，共 {len(preview_list)} 条指令")
            else:
                Logger.info(f"指令序列预览生成完成，共 {len(preview_list)} 条指令")
            return preview_list

        except Exception as e:
            Logger.error(f"生成指令序列预览失败: {e}")
            return None

    def start_palletizing(self, pallet_config, recipe_data, teaching_data):
        """开始码垛作业"""
        try:
            if self.is_running:
                Logger.warning("码垛作业已在运行中")
                return False

            Logger.info("开始码垛作业")

            # 生成指令序列
            self.instruction_list = self._generate_instruction_sequence(
                pallet_config, recipe_data, teaching_data
            )

            if not self.instruction_list:
                Logger.error("指令序列生成失败")
                return False

            # 重置状态
            self.current_index = 0
            self.is_running = True
            self.is_paused = False
            self.current_box_gripped = False

            # 发送开始信号
            self.execution_started.emit()
            self.status_changed.emit("码垛作业开始")

            # 开始执行指令序列
            self._execute_instruction_sequence()

            return True

        except Exception as e:
            Logger.error(f"启动码垛作业失败: {e}")
            self.error_occurred.emit(f"启动失败: {e}")
            return False

    def pause_palletizing(self):
        """暂停码垛作业"""
        if self.is_running and not self.is_paused:
            self.is_paused = True
            self.execution_paused.emit()
            self.status_changed.emit("码垛作业暂停")
            Logger.info("码垛作业已暂停")
            return True
        return False

    def resume_palletizing(self):
        """恢复码垛作业"""
        if self.is_running and self.is_paused:
            self.is_paused = False
            self.execution_resumed.emit()
            self.status_changed.emit("码垛作业恢复")
            Logger.info("码垛作业已恢复")
            return True
        return False

    def stop_palletizing(self):
        """停止码垛作业"""
        if self.is_running:
            self.is_running = False
            self.is_paused = False

            # 如果当前抓着箱子，执行紧急放置
            if self.current_box_gripped:
                self._emergency_box_placement()

            self.execution_stopped.emit()
            self.status_changed.emit("码垛作业停止")
            Logger.info("码垛作业已停止")
            return True
        return False

    def _generate_instruction_sequence(self, pallet_config, recipe_data, teaching_data):
        """生成码垛指令序列"""
        instructions = []

        try:
            # 1. 移动到初始位置
            initial_position = teaching_data.get('initial_position')
            if initial_position:
                position_data = self._get_position_from_teaching_point(initial_position)
                if position_data:
                    cmd = self._create_move_j_command_with_offset(position_data)
                    instructions.append((cmd, -1, "移动到初始位置"))

            # 2. 生成码垛指令
            pallet_instructions = self._generate_pallet_instructions(
                pallet_config, recipe_data, teaching_data
            )
            instructions.extend(pallet_instructions)

            # 3. 返回初始位置
            if initial_position:
                position_data = self._get_position_from_teaching_point(initial_position)
                if position_data:
                    cmd = self._create_move_j_command_with_offset(position_data)
                    instructions.append((cmd, -1, "返回初始位置"))

            Logger.info(f"生成指令序列完成，共{len(instructions)}条指令")
            return instructions

        except Exception as e:
            Logger.error(f"生成指令序列失败: {e}")
            return []

    def _generate_pallet_instructions(self, pallet_config, recipe_data, teaching_data):
        """生成具体的码垛指令"""
        instructions = []

        # 使用临时测试数据或传入的配置
        if pallet_config:
            box_config = pallet_config.get('box', self.temp_test_data['box'])
            layer_count = pallet_config.get('layers', self.temp_test_data['layers'])
            pattern = pallet_config.get('layer_pattern', self.temp_test_data['layer_pattern'])
        else:
            box_config = self.temp_test_data['box']
            layer_count = self.temp_test_data['layers']
            pattern = self.temp_test_data['layer_pattern']

        # 获取示教点数据
        grab_point = teaching_data.get('grab_point')
        grab_transition = teaching_data.get('grab_transition_point')
        place_transition = teaching_data.get('place_transition_point')
        left_pallet_zero = teaching_data.get('left_pallet_zero_point')

        if not all([grab_point, grab_transition, place_transition, left_pallet_zero]):
            Logger.error("缺少必要的示教点数据")
            return instructions

        # 生成层样式位置
        layer_positions = self._generate_layer_positions(pattern)

        box_index = 0

        # 遍历每一层
        for layer_index in range(layer_count):
            layer_height = layer_index * box_config['height']

            # 遍历该层的每个箱子位置
            for position in layer_positions:
                box_index += 1

                # 生成单个箱子的码垛指令
                box_instructions = self._generate_single_box_instructions_with_offset(
                    box_index, position, layer_height,
                    grab_point, grab_transition, place_transition,
                    left_pallet_zero, teaching_data
                )

                instructions.extend(box_instructions)

        return instructions

    def _generate_layer_positions(self, pattern):
        """生成层样式位置"""
        positions = []
        rows, cols = pattern['rows'], pattern['cols']
        row_spacing, col_spacing = pattern['row_spacing'], pattern['col_spacing']

        # 计算起始偏移（使网格居中）
        start_x = -(cols - 1) * col_spacing / 2
        start_y = -(rows - 1) * row_spacing / 2

        for row in range(rows):
            for col in range(cols):
                x = start_x + col * col_spacing
                y = start_y + row * row_spacing
                positions.append({'x': x, 'y': y, 'rotation': 0})

        return positions

    def _generate_single_box_instructions_with_offset(self, box_index, position, layer_height,
                                                     grab_point, grab_transition, place_transition,
                                                     pallet_zero, teaching_data):
        """生成单个箱子的码垛指令序列"""
        instructions = []

        try:
            # 1. 移动到抓取过渡点（MoveJ）
            grab_trans_pos = self._get_position_from_teaching_point(grab_transition)
            if grab_trans_pos:
                cmd = self._create_move_j_command_with_offset(grab_trans_pos)
                if cmd:  # 确保命令生成成功
                    instructions.append((cmd, box_index, f"移动到抓取过渡点-箱子{box_index}"))
                else:
                    Logger.error(f"生成抓取过渡点指令失败-箱子{box_index}")
                    return []

            # 2. 移动到抓取点（MoveL）
            grab_pos = self._get_position_from_teaching_point(grab_point)
            if grab_pos:
                cmd = self._create_move_l_command_with_offset(grab_pos)
                if cmd:  # 确保命令生成成功
                    instructions.append((cmd, box_index, f"移动到抓取点-箱子{box_index}"))
                else:
                    Logger.error(f"生成抓取点指令失败-箱子{box_index}")
                    return []

            # 3. 执行抓取操作
            instructions.append(("GRAB_BOX", box_index, f"抓取箱子{box_index}"))

            # 4. 移动到抓取过渡点（MoveJ）
            if grab_trans_pos:
                cmd = self._create_move_j_command_with_offset(grab_trans_pos)
                if cmd:  # 确保命令生成成功
                    instructions.append((cmd, box_index, f"移动到抓取后过渡点-箱子{box_index}"))
                else:
                    Logger.error(f"生成抓取后过渡点指令失败-箱子{box_index}")
                    return []

            # 5. 移动到放置过渡点（MoveJ）
            place_trans_pos = self._get_position_from_teaching_point(place_transition)
            if place_trans_pos:
                cmd = self._create_move_j_command_with_offset(place_trans_pos)
                if cmd:  # 确保命令生成成功
                    instructions.append((cmd, box_index, f"移动到放置过渡点-箱子{box_index}"))
                else:
                    Logger.error(f"生成放置过渡点指令失败-箱子{box_index}")
                    return []

            # 6. 计算放置点位置并移动（MoveL）
            place_pos = self._calculate_place_point_with_offset(position, layer_height, pallet_zero)
            if place_pos:
                cmd = self._create_move_l_command_with_offset(place_pos)
                if cmd:  # 确保命令生成成功
                    instructions.append((cmd, box_index, f"移动到放置点-箱子{box_index}"))
                else:
                    Logger.error(f"生成放置点指令失败-箱子{box_index}")
                    return []

            # 7. 执行放置操作
            instructions.append(("PLACE_BOX", box_index, f"放置箱子{box_index}"))

            # 8. 移动到放置后过渡点（MoveJ）
            if place_trans_pos:
                cmd = self._create_move_j_command_with_offset(place_trans_pos)
                if cmd:  # 确保命令生成成功
                    instructions.append((cmd, box_index, f"移动到放置后过渡点-箱子{box_index}"))
                else:
                    Logger.error(f"生成放置后过渡点指令失败-箱子{box_index}")
                    return []

            Logger.debug(f"成功生成箱子{box_index}的{len(instructions)}条指令")
            return instructions

        except Exception as e:
            Logger.error(f"生成箱子{box_index}指令序列失败: {e}")
            return []

    def _get_position_from_teaching_point(self, teaching_point):
        """从示教点获取位置信息"""
        if not teaching_point:
            return None

        try:
            # 直接使用示教点的关节角度和TCP坐标
            position_data = {
                'joint_j1': float(teaching_point.get('j1', 0)),
                'joint_j2': float(teaching_point.get('j2', 0)),
                'joint_j3': float(teaching_point.get('j3', 0)),
                'joint_j4': float(teaching_point.get('j4', 0)),
                'joint_j5': float(teaching_point.get('j5', 0)),
                'joint_j6': float(teaching_point.get('j6', 0)),
                'tcp_x': float(teaching_point.get('x', 0)),
                'tcp_y': float(teaching_point.get('y', 0)),
                'tcp_z': float(teaching_point.get('z', 0)),
                'tcp_rx': float(teaching_point.get('rx', 0)),
                'tcp_ry': float(teaching_point.get('ry', 0)),
                'tcp_rz': float(teaching_point.get('rz', 0))
            }

            Logger.info(f"示教点数据获取成功: TCP({position_data['tcp_x']}, {position_data['tcp_y']}, {position_data['tcp_z']})")
            return position_data

        except Exception as e:
            Logger.error(f"处理示教点数据失败: {e}")
            return None

    def _calculate_place_point_with_offset(self, position, layer_height, pallet_zero):
        """计算放置点位置"""
        if not pallet_zero:
            Logger.error("缺少托盘零点数据")
            return None

        try:
            # 获取托盘零点的坐标
            zero_x = float(pallet_zero.get('x', 0))
            zero_y = float(pallet_zero.get('y', 0))
            zero_z = float(pallet_zero.get('z', 0))
            zero_rx = float(pallet_zero.get('rx', 0))  # 新增
            zero_ry = float(pallet_zero.get('ry', 0))  # 新增
            zero_rz = float(pallet_zero.get('rz', 0))

            # 计算目标位置
            target_x = round(zero_x + position['x'], 3)
            target_y = round(zero_y + position['y'], 3)
            target_z = round(zero_z + layer_height + self.temp_test_data['pallet']['height'], 3)
            target_rx = round(zero_rx, 3)
            target_ry = round(zero_ry, 3)
            target_rz = round(zero_rz + position['rotation'], 3)

            # 使用逆运动学计算关节角度
            place_position = self.get_inverse_kinematics(target_x, target_y, target_z, target_rx, target_ry, target_rz)

            if place_position:
                Logger.info(f"放置点计算成功: ({target_x}, {target_y}, {target_z}, {target_rz})")
                return place_position
            else:
                Logger.error(f"放置点逆运动学解算失败: ({target_x}, {target_y}, {target_z}, {target_rz})")
                # 返回基于托盘零点的简化位置
                return {
                    'joint_j1': float(pallet_zero.get('j1', 0)),
                    'joint_j2': float(pallet_zero.get('j2', 0)),
                    'joint_j3': float(pallet_zero.get('j3', 0)),
                    'joint_j4': float(pallet_zero.get('j4', 0)),
                    'joint_j5': float(pallet_zero.get('j5', 0)),
                    'joint_j6': float(pallet_zero.get('j6', 0)),
                    'tcp_x': target_x,
                    'tcp_y': target_y,
                    'tcp_z': target_z,
                    'tcp_rx': float(pallet_zero.get('rx', 0)),
                    'tcp_ry': float(pallet_zero.get('ry', 0)),
                    'tcp_rz': target_rz
                }

        except Exception as e:
            Logger.error(f"计算放置点失败: {e}")
            return None

    def get_inverse_kinematics(self, x, y, z, rx, ry, rz, config=0):
        """获取逆运动学解算结果"""
        try:
            # 创建缓存键
            cache_key = f"{x:.2f},{y:.2f},{z:.2f},{rx:.2f},{ry:.2f},{rz:.2f},{config}"
            print(cache_key)
            # 检查缓存
            if cache_key in self.inverse_kinematics_cache:
                Logger.info(f"使用缓存的逆运动学结果: {cache_key}")
                return self.inverse_kinematics_cache[cache_key]

            # 计算逆运动学
            result = self._calculate_inverse_kinematics(x, y, z, rx, ry, rz, config)

            # 缓存结果
            if result:
                self.inverse_kinematics_cache[cache_key] = result

            return result

        except Exception as e:
            Logger.error(f"逆运动学解算异常: {e}")
            return None

    def _calculate_inverse_kinematics(self, x, y, z, rx, ry, rz, config=0):
        """计算逆运动学"""
        try:
            # 构造GetInverseKin指令
            params = {
                'flag':0,'x': x, 'y': y, 'z': z,
                'a': rx, 'b': ry, 'c': rz,
                'config': config
            }
            cmd = RobotCommand.get_command_str('GetInverseKin', params, 377)

            # 发送指令并等待响应
            result = self.command_manager.send_and_wait(cmd)

            if result["success"]:
                # 解析响应
                response = result["response"]
                position_data = self._parse_inverse_kinematics_response(response)

                if position_data:
                    Logger.info(f"逆运动学解算成功: {cmd}")
                    return position_data
                else:
                    Logger.error(f"逆运动学响应解析失败: {response}")
                    return None
            else:
                Logger.error(f"逆运动学指令执行失败: {result['response']}")
                return None

        except Exception as e:
            Logger.error(f"逆运动学计算异常: {e}")
            return None

    def _parse_inverse_kinematics_response(self, response):
        """解析逆运动学响应"""
        try:
            # 响应格式: /f/bIII377III377III62III-72.944328,28.410639,-127.666801,9.343328,-90.045433,17.055746III/b/f
            # 需要提取III分隔符之间的关节值
            if isinstance(response, str) and 'III' in response:
                # 按III分割，找到包含关节值的部分（通常是倒数第二个部分）
                parts = response.split('III')
                for part in parts:
                    # 查找包含逗号分隔数值的部分
                    if ',' in part and part.count(',') >= 5:
                        # 提取数值部分，去除可能的前缀字符
                        joint_data = part.strip()
                        # 如果开头有非数字字符，尝试找到第一个数字或负号的位置
                        import re
                        match = re.search(r'(-?\d+\.\d+,-?\d+\.\d+,-?\d+\.\d+,-?\d+\.\d+,-?\d+\.\d+,-?\d+\.\d+)', joint_data)
                        if match:
                            joint_values_str = match.group(1)
                            joint_values = joint_values_str.split(',')
                            if len(joint_values) >= 6:
                                return {
                                    'joint_j1': float(joint_values[0]),
                                    'joint_j2': float(joint_values[1]),
                                    'joint_j3': float(joint_values[2]),
                                    'joint_j4': float(joint_values[3]),
                                    'joint_j5': float(joint_values[4]),
                                    'joint_j6': float(joint_values[5]),
                                    'tcp_x': 0,  # 这些值需要根据实际情况填充
                                    'tcp_y': 0,
                                    'tcp_z': 0,
                                    'tcp_rx': 0,
                                    'tcp_ry': 0,
                                    'tcp_rz': 0
                                }

            # 如果上述方法失败，尝试原来的简单解析方法
            if isinstance(response, str) and ',' in response:
                joint_values = response.strip().split(',')
                if len(joint_values) >= 6:
                    return {
                        'joint_j1': float(joint_values[0]),
                        'joint_j2': float(joint_values[1]),
                        'joint_j3': float(joint_values[2]),
                        'joint_j4': float(joint_values[3]),
                        'joint_j5': float(joint_values[4]),
                        'joint_j6': float(joint_values[5]),
                        'tcp_x': 0,
                        'tcp_y': 0,
                        'tcp_z': 0,
                        'tcp_rx': 0,
                        'tcp_ry': 0,
                        'tcp_rz': 0
                    }

            Logger.error(f"逆运动学响应解析失败: {response}")
            return None

        except Exception as e:
            Logger.error(f"解析逆运动学响应异常: {e}")
            return None

    def _create_move_j_command_with_offset(self, position):
        """创建带全局偏移的MoveJ指令"""
        try:
            if not position:
                Logger.error("位置数据为空，无法创建MoveJ指令")
                return None

            params = {
                'J1': float(position.get('joint_j1', 0)),
                'J2': float(position.get('joint_j2', 0)),
                'J3': float(position.get('joint_j3', 0)),
                'J4': float(position.get('joint_j4', 0)),
                'J5': float(position.get('joint_j5', 0)),
                'J6': float(position.get('joint_j6', 0)),
                'x': float(position.get('tcp_x', 0)),
                'y': float(position.get('tcp_y', 0)),
                'z': float(position.get('tcp_z', 0)),
                'rx': float(position.get('tcp_rx', 0)),
                'ry': float(position.get('tcp_ry', 0)),
                'rz': float(position.get('tcp_rz', 0)),
                'speed': float(gv.GLOBAL_SPEED_ or 30.0),
                'toolNum': 1,
                'workPieceNum': 0,
                'acc': 100,
                'ovl': 100,
                # 添加全局偏移变量
                'offset_x': f"{gv.OFFSET_COMPENSATIONX_}",
                'offset_y': f"{gv.OFFSET_COMPENSATIONY_}",
                'offset_z': f"{gv.OFFSET_COMPENSATIONZ_}",
                'offset_rx': f"{gv.OFFSET_COMPENSATIONRX_}",
                'offset_ry': f"{gv.OFFSET_COMPENSATIONRY_}",
                'offset_rz': f"{gv.OFFSET_COMPENSATIONRZ_}"
            }

            cmd = RobotCommand.get_command_str('MoveJ', params, 201)
            if not cmd or cmd.strip() == "":
                Logger.error("生成的MoveJ指令为空")
                return None

            # Logger.debug(f"生成MoveJ指令: {cmd}")
            return cmd

        except Exception as e:
            Logger.error(f"创建MoveJ指令失败: {e}")
            return None

    def _create_move_l_command_with_offset(self, position):
        """创建带全局偏移的MoveL指令"""
        try:
            if not position:
                Logger.error("位置数据为空，无法创建MoveL指令")
                return None

            params = {
                'J1': float(position.get('joint_j1', 0)),
                'J2': float(position.get('joint_j2', 0)),
                'J3': float(position.get('joint_j3', 0)),
                'J4': float(position.get('joint_j4', 0)),
                'J5': float(position.get('joint_j5', 0)),
                'J6': float(position.get('joint_j6', 0)),
                'x': float(position.get('tcp_x', 0)),
                'y': float(position.get('tcp_y', 0)),
                'z': float(position.get('tcp_z', 0)),
                'rx': float(position.get('tcp_rx', 0)),
                'ry': float(position.get('tcp_ry', 0)),
                'rz': float(position.get('tcp_rz', 0)),
                'speed': float(gv.GLOBAL_SPEED_ or 30.0),
                'toolNum': 1,
                'workPieceNum': 0,
                'acc': 100,
                'ovl': 100,
                # 添加全局偏移变量
                'offset_x': f"{gv.OFFSET_COMPENSATIONX_}",
                'offset_y': f"{gv.OFFSET_COMPENSATIONY_}",
                'offset_z': f"{gv.OFFSET_COMPENSATIONZ_}",
                'offset_rx': f"{gv.OFFSET_COMPENSATIONRX_}",
                'offset_ry': f"{gv.OFFSET_COMPENSATIONRY_}",
                'offset_rz': f"{gv.OFFSET_COMPENSATIONRZ_}"
            }

            cmd = RobotCommand.get_command_str('MoveL', params, 203)
            if not cmd or cmd.strip() == "":
                Logger.error("生成的MoveL指令为空")
                return None

            # Logger.debug(f"生成MoveL指令: {cmd}")
            return cmd

        except Exception as e:
            Logger.error(f"创建MoveL指令失败: {e}")
            return None

    def _execute_instruction_sequence(self):
        """执行指令序列"""
        try:
            while self.is_running and self.current_index < len(self.instruction_list):
                # 检查暂停状态
                if self.is_paused:
                    self._handle_pause_state()
                    continue

                # 检查停止状态
                if not self.is_running:
                    break

                # 获取当前指令
                instruction = self.instruction_list[self.current_index]
                cmd = instruction[0]
                box_index = instruction[1]
                description = instruction[2] if len(instruction) > 2 else "执行指令"

                # 发送进度信号
                if box_index >= 0:
                    self.execution_progress.emit(box_index, description)

                # 执行指令
                success = self._execute_single_instruction(cmd, description)

                if not success:
                    self.error_occurred.emit(f"指令执行失败: {description}")
                    self.stop_palletizing()
                    return

                self.current_index += 1

            # 执行完成
            if self.is_running:
                self.is_running = False
                self.execution_finished.emit()
                self.status_changed.emit("码垛作业完成")
                Logger.info("码垛作业执行完成")

        except Exception as e:
            Logger.error(f"执行指令序列异常: {e}")
            self.error_occurred.emit(f"执行异常: {e}")
            self.stop_palletizing()

    def _execute_single_instruction(self, cmd, description):
        """执行单条指令"""
        try:
            if cmd == "GRAB_BOX":
                return self._execute_grab_operation()
            elif cmd == "PLACE_BOX":
                return self._execute_place_operation()
            else:
                # 普通运动指令 - 添加命令验证
                if not cmd or cmd.strip() == "":
                    Logger.error(f"指令为空: {description}")
                    return False

                # 验证命令格式
                if not self._validate_command_format(cmd):
                    Logger.error(f"指令格式无效: {description} - {cmd}")
                    return False

                Logger.debug(f"发送指令: {cmd} - {description}")

                result = self.command_manager.send_and_wait(cmd)

                if result.get("success", False):
                    Logger.info(f"指令执行成功: {description}")
                    return True
                else:
                    error_msg = result.get('response', result.get('error', '未知错误'))
                    Logger.error(f"指令执行失败: {description} - {error_msg}")
                    return False

        except Exception as e:
            Logger.error(f"执行指令异常: {description} - {e}")
            return False

    def _validate_command_format(self, cmd):
        """验证命令格式"""
        try:
            # 检查是否包含必要的III分隔符
            if "III" not in cmd:
                return False
            # 检查是否以正确的格式开始和结束
            if not cmd.startswith("/f/bIII") or not cmd.endswith("III/b/f"):
                return False
            return True
        except Exception:
            return False

    def _execute_grab_operation(self):
        """执行抓取操作"""
        try:
            # 启动吸盘
            if not self.io_logic.activate_suction_cup():
                Logger.error("启动吸盘失败")
                return False

            # 等待吸盘建立真空
            time.sleep(0.5)

            # 检查吸盘状态
            if not self.io_logic.check_suction_cup_vacuum():
                Logger.error("吸盘未建立真空")
                self.io_logic.deactivate_suction_cup()
                return False

            self.current_box_gripped = True
            Logger.info("抓取操作成功")
            return True

        except Exception as e:
            Logger.error(f"抓取操作异常: {e}")
            return False

    def _execute_place_operation(self):
        """执行放置操作"""
        try:
            # 关闭吸盘
            if not self.io_logic.deactivate_suction_cup():
                Logger.error("关闭吸盘失败")
                return False

            # 等待箱子完全放置
            time.sleep(0.3)

            self.current_box_gripped = False
            Logger.info("放置操作成功")
            return True

        except Exception as e:
            Logger.error(f"放置操作异常: {e}")
            return False

    def _handle_pause_state(self):
        """处理暂停状态"""
        # 记录暂停位置
        if not self.pause_position:
            self.pause_position = self.robot_status.get_current_position()

        # 等待恢复信号
        while self.is_paused and self.is_running:
            time.sleep(0.1)

        # 恢复后的处理
        if self.is_running and not self.is_paused:
            self.pause_position = None

    def _emergency_box_placement(self):
        """紧急情况下的箱子放置"""
        try:
            Logger.warning("执行紧急箱子放置")
            # 关闭吸盘，放置箱子
            self.io_logic.deactivate_suction_cup()
            self.current_box_gripped = False
        except Exception as e:
            Logger.error(f"紧急箱子放置失败: {e}")

    def _validate_position_data(self, position):
        """验证位置数据完整性"""
        if not position:
            return False

        required_keys = ['joint_j1', 'joint_j2', 'joint_j3', 'joint_j4', 'joint_j5', 'joint_j6',
                        'tcp_x', 'tcp_y', 'tcp_z', 'tcp_rx', 'tcp_ry', 'tcp_rz']

        return all(key in position for key in required_keys)

    def start_multi_pallet_execution(self, pallet_instructions: Dict[str, List]):
        """开始多垛盘执行"""
        try:
            Logger.info(f"开始多垛盘执行，垛盘数量: {len(pallet_instructions)}")

            # 初始化所有垛盘的执行状态
            for pallet_id, instructions in pallet_instructions.items():
                self.active_pallets[pallet_id] = {
                    'instructions': instructions,
                    'current_index': 0,
                    'total_count': len(instructions),
                    'status': 'ready',
                    'current_layer': 1,
                    'current_box': 0
                }

            self.is_running = True
            self.is_paused = False

            # 开始执行第一个垛盘
            self._start_next_pallet_execution()

        except Exception as e:
            Logger.error(f"多垛盘执行启动失败: {e}")

    def _start_next_pallet_execution(self):
        """开始执行下一个垛盘"""
        # 找到下一个需要执行的垛盘
        next_pallet = None
        for pallet_id, state in self.active_pallets.items():
            if state['status'] == 'ready':
                next_pallet = pallet_id
                break

        if next_pallet:
            self.current_executing_pallet = next_pallet
            self.active_pallets[next_pallet]['status'] = 'executing'

            # 发送开始信号
            self.execution_started.emit(next_pallet)

            # 开始执行该垛盘的指令
            self._execute_pallet_instructions(next_pallet)
        else:
            # 所有垛盘执行完成
            self._finish_all_executions()

    def _execute_pallet_instructions(self, pallet_id: str):
        """执行特定垛盘的指令"""
        try:
            pallet_state = self.active_pallets[pallet_id]
            instructions = pallet_state['instructions']
            current_index = pallet_state['current_index']

            if current_index < len(instructions):
                instruction = instructions[current_index]

                # 修复参数传递 - 正确解析指令元组
                cmd = instruction[0]
                description = instruction[2]  # 第三个元素是描述
                success = self._execute_single_instruction(cmd, description)

                if success:
                    # 更新进度
                    pallet_state['current_index'] += 1
                    progress = int((current_index + 1) / len(instructions) * 100)

                    # 发送进度信号
                    self.execution_progress.emit(
                        pallet_id,
                        pallet_state['current_layer'],
                        pallet_state['current_box'],
                        progress
                    )

                    # 继续执行下一条指令
                    QTimer.singleShot(100, lambda: self._execute_pallet_instructions(pallet_id))
                else:
                    # 执行失败
                    self.error_occurred.emit(pallet_id, "EXEC_ERROR", "指令执行失败")
            else:
                # 该垛盘执行完成
                self._finish_pallet_execution(pallet_id)

        except Exception as e:
            Logger.error(f"垛盘 {pallet_id} 指令执行失败: {e}")
            self.error_occurred.emit(pallet_id, "EXEC_EXCEPTION", str(e))

    def _finish_pallet_execution(self, pallet_id: str):
        """完成垛盘执行"""
        self.active_pallets[pallet_id]['status'] = 'completed'
        self.execution_finished.emit(pallet_id, True, "垛盘作业完成")

        # 开始执行下一个垛盘
        self._start_next_pallet_execution()

    def _finish_all_executions(self):
        """完成所有垛盘执行"""
        self.is_running = False
        self.status_changed.emit("所有垛盘作业完成")
        Logger.info("所有垛盘作业执行完成")


def generate_instruction_batch(self, pallet_config, recipe_data, teaching_data):
    """生成指令批次（收集到数组中，不执行）"""
    try:
        # 生成完整的指令序列
        instruction_sequence = self._generate_instruction_sequence(pallet_config, recipe_data, teaching_data)

        # 提取所有指令到数组中
        instruction_batch = []
        for instruction in instruction_sequence:
            if isinstance(instruction, tuple) and len(instruction) >= 3:
                cmd, box_index, description = instruction[0], instruction[1], instruction[2]
                # 只收集实际的运动指令，跳过特殊操作
                if cmd not in ["GRAB_BOX", "PLACE_BOX"]:
                    instruction_batch.append(cmd)

        Logger.info(f"指令批次生成完成，共 {len(instruction_batch)} 条指令")
        return instruction_batch

    except Exception as e:
        Logger.error(f"生成指令批次失败: {e}")
        return []

def execute_instruction_batch(self, instruction_batch):
    """批量执行指令数组"""
    try:
        if not instruction_batch:
            Logger.warning("指令批次为空")
            return False

        Logger.info(f"开始批量执行 {len(instruction_batch)} 条指令")

        # 批量发送指令
        success_count = 0
        for i, cmd in enumerate(instruction_batch):
            if not self.is_running:
                break

            result = self.command_manager.send_and_wait(cmd)
            if result.get("success", False):
                success_count += 1
                Logger.debug(f"指令 {i+1}/{len(instruction_batch)} 执行成功")
            else:
                error_msg = result.get('response', result.get('error', '未知错误'))
                Logger.error(f"指令 {i+1}/{len(instruction_batch)} 执行失败: {error_msg}")
                return False

        Logger.info(f"批量指令执行完成: 成功 {success_count}/{len(instruction_batch)} 条")
        return True

    except Exception as e:
        Logger.error(f"批量执行指令失败: {e}")
        return False

def start_batch_palletizing(self, pallet_config, recipe_data, teaching_data):
    """开始批量码垛作业"""
    try:
        if self.is_running:
            Logger.warning("码垛作业已在运行中")
            return False

        Logger.info("开始批量码垛作业")

        # 生成指令批次
        instruction_batch = self.generate_instruction_batch(pallet_config, recipe_data, teaching_data)

        if not instruction_batch:
            Logger.error("指令批次生成失败")
            return False

        # 设置运行状态
        self.is_running = True
        self.is_paused = False

        # 发送开始信号
        self.execution_started.emit("")
        self.status_changed.emit("批量码垛作业开始")

        # 批量执行指令
        success = self.execute_instruction_batch(instruction_batch)

        if success:
            self.execution_finished.emit("", True, "批量码垛作业完成")
            self.status_changed.emit("批量码垛作业完成")
            Logger.info("批量码垛作业执行完成")
        else:
            self.error_occurred.emit("", "BATCH_ERROR", "批量指令执行失败")

        self.is_running = False
        return success

    except Exception as e:
        Logger.error(f"启动批量码垛作业失败: {e}")
        self.error_occurred.emit("", "START_ERROR", f"启动失败: {e}")
        self.is_running = False
        return False
