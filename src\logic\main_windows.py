from PySide6.QtCore import QTimer, QDateTime, QThread, Signal, QCoreApplication
from PySide6.QtWidgets import QPushButton, QMessageBox, QDialog, QSlider, QHBoxLayout, QLineEdit, QVBoxLayout, QLabel
from PySide6.QtGui import QGuiApplication, QIntValidator
from src.ui.Ui_main_window import Ui_Form
from src.ui.Ui_Ip import Ui_Dialog 
from .formula_logic import FormulaLogic
from .run_logic import RunLogic
from .layerStyle_logic import FormulaLayerStyleLogic
from .IO_logic import IOLogic
from .teaching_logic import TeachingLogic
from .log_logic import LogLogic
from ..common.robot_status import RobotStatus
from ..common.sql_lite_tool import SqliteTool
from ..core.cmd_tool import RobotCommand
from ..core.robot_8083_socket import Robot8083Socket
from ..core.palletizer_program import command_manager
from ..core.robot_tcp_controller import RobotTcp<PERSON>ontroller
from ..core.solve_keyboard import *
import re
import time

DRAG_SIGN_ = ""
ERROR_CODE_ = ""  # 错误编码

Acceleration_smoothing_change = False  # 判断加速度平滑是否关闭局部变量


# IP设置对话框类（使用优化后的Ui_Ip.py文件）
class IpSettingsDialog(QDialog, Ui_Dialog):
    def __init__(self, parent=None, current_ip="", main_window=None):
        super().__init__(parent)

        # 设置UI
        self.setupUi(self)

        self.main_window = main_window
        self.current_ip = current_ip

        # 获取数据库实例
        self.db_tool = SqliteTool.get_instance()

        # 设置当前IP
        self.process_electricInput_lineEdit.setText(current_ip)

        # 连接信号
        self.process_weldingParameterApply_button.clicked.connect(self.cancel_clicked)
        self.process_weldingParameterApply_button_2.clicked.connect(self.save_clicked)


    def save_clicked(self):
        """保存按钮点击事件"""
        new_ip = self.process_electricInput_lineEdit.text().strip()

        if not new_ip:
            QMessageBox.warning(self, "警告", "请输入IP地址！")
            return

        # 简单的IP格式验证
        parts = new_ip.split('.')
        if len(parts) != 4:
            QMessageBox.warning(self, "警告", "IP地址格式不正确！\n正确格式: *************")
            return

        try:
            for part in parts:
                num = int(part)
                if num < 0 or num > 255:
                    raise ValueError()
        except ValueError:
            QMessageBox.warning(self, "警告", "IP地址格式不正确！\n每个数字应在0-255之间")
            return

        # 如果IP没有变化，直接关闭
        if new_ip == self.current_ip:
            self.accept()
            return

        try:
            # 更新数据库
            existing_records = self.db_tool.select("ip_settings", condition="1=1")

            if existing_records:
                self.db_tool.update("ip_settings", {"ip": new_ip}, condition="1=1")
            else:
                self.db_tool.insert("ip_settings", {"ip": new_ip})

            # 设置全局变量标志
            import src.common.global_var as gv
            gv.IP_change = True

            # 通知主窗口IP已更改
            if self.main_window:
                self.main_window.on_ip_changed(new_ip)


            # 关闭对话框
            self.accept()

        except Exception as e:

            QMessageBox.critical(self, "保存失败", f"保存失败：{str(e)}")

    def cancel_clicked(self):
        """取消按钮点击事件"""
        self.reject()


# 速度滑动条对话框类
class SpeedSliderDialog(QDialog):
    def __init__(self, parent=None, current_speed=30, main_window=None):
        super().__init__(parent)
        self.main_window = main_window
        self.current_speed = current_speed

        self.setWindowTitle("速度设置")
        self.setFixedSize(300, 150)

        # 创建布局
        layout = QVBoxLayout()

        # 速度标签
        self.speed_label = QLabel(f"当前速度: {current_speed}%")
        self.speed_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.speed_label)

        # 滑动条
        self.speed_slider = QSlider(Qt.Orientation.Horizontal)
        self.speed_slider.setMinimum(1)
        self.speed_slider.setMaximum(100)
        self.speed_slider.setValue(current_speed)
        self.speed_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.speed_slider.setTickInterval(10)
        self.speed_slider.valueChanged.connect(self.on_slider_changed)
        layout.addWidget(self.speed_slider)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_button)

        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def on_slider_changed(self, value):
        """滑动条值改变时更新显示"""
        self.speed_label.setText(f"当前速度: {value}%")
        self.current_speed = value

    def get_speed(self):
        """获取设置的速度值"""
        return self.current_speed


# 在类中添加新的工作对象
class MainWindow(QWidget, Ui_Form):
    # 发送指令 信号
    send_command_signal = Signal(str)

    # 连接机器人信号
    connect_requested = Signal()
    send_cmd = Signal(str)
    reconnect = Signal()

    def __init__(self):
        super().__init__()

        self.current_speed = 30  # 默认速度30%
        self.current_collision_level = 60  # 默认碰撞等级60%

        self.setupUi(self)

        # 设置窗口控制功能
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.FramelessWindowHint)

        # 连接窗口控制按钮
        self.Scale_downButton_2.clicked.connect(self.minimize_window)
        self.Scale_upButton_2.clicked.connect(self.toggle_maximize)
        self.CloseButton_2.clicked.connect(self.close_window)

        # 拖动相关变量
        self.drag_position = None
        self.is_dragging = False
        self.drag_start_position = None

        # 窗口状态标志
        self.window_is_maximized = False

        # 拖动限制设置
        self.allow_drag_resize = False  # 禁止拖动时自动缩小窗口


        self.current_background_type = None  # 当前背景类型：'large' 或 'small'
        self.background_update_timer = QTimer()
        self.background_update_timer.setSingleShot(True)

        # 设置初始窗口大小和位置
        self.resize(1024, 768)
        self.center_window()

        self.TimeLabel_2.setText(QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss"))  # 更新时间显示标签的文本

        self.db_tool = SqliteTool.get_instance()
        # 1. 从数据库获取初始IP地址
        self.current_ip = self.db_tool.select("ip_settings", condition="1=1 ORDER BY id DESC LIMIT 1")

        # 初始化IP显示
        if self.current_ip:
            self.update_ip_display(self.current_ip[0]['ip'])

        self.robot = RobotTcpController.get_instance(self.current_ip[0]['ip'], 8080)
        self.timer = QTimer(self)  # 创建定时器
        self.timer.timeout.connect(self.update_time)  # 连接定时器信号到时间显示函数
        self.timer.start(1000)  # 启动定时器，每秒更新一次时间

        # 8080端口重连定时器
        self.reconnect_timer = QTimer(self)
        self.reconnect_timer.timeout.connect(self._try_reconnect)
        self.reconnect_timer.start(3000)

        # 初始化机器人客户端socket (8080端口)
        self.network_thread = QThread()
        self.robot_controller = RobotTcpController.get_instance(self.current_ip[0]['ip'], 8080)
        self.robot_controller.moveToThread(self.network_thread)

        # 连接8080端口的信号到新的处理函数
        self.robot_controller.connected.connect(self._on_8080_connected)
        self.robot_controller.disconnected.connect(self._on_8080_disconnected)
        self.robot_controller.response_received.connect(self._on_robot_data)

        self.send_command_signal.connect(self.robot_controller.send_command)
        self.send_cmd.connect(self.robot.send_command)
        self.connect_requested.connect(self.robot_controller.connect_to_robot)
        self.reconnect.connect(self.robot_controller.connect_to_robot)

        self.network_thread.started.connect(lambda: self.connect_requested.emit())
        self.network_thread.start()

        # 初始化监听端口 (8083端口)
        self.robot_8083_socket = Robot8083Socket(self.current_ip[0]['ip'], 8083)
        # 连接8083端口的所有信号
        self.robot_8083_socket.connected.connect(self._on_8083_connected)
        self.robot_8083_socket.disconnected.connect(self._on_8083_disconnected)
        self.robot_8083_socket.data_signal.connect(self.message_8083)

        # 初始时，更新一次UI状态（根据实际连接状态显示：已连接/连接中/已断开）
        self._update_overall_connection_status()

        # 初始化页面字典
        self.pages = {}
        self.init_pages()
        self.setup_page_connections()
        self.init_navigation()

        # 初始化所有按钮的默认背景图
        self.init_button_backgrounds()

        # 初始化IP按钮背景图为已断开状态
        self.update_ip_button_background("disconnected")

        # 初始化模式按钮背景图为自动模式
        self.current_robot_mode = 0  # 跟踪当前机器人模式：0=自动，1=手动，2=拖动
        self.update_mode_button_background("auto")

        # 默认显示首页
        self.switch_page('run_page')

        # 使用机器人状态数据
        self.robot_status = RobotStatus()
        # 使用统一的指令管理器
        command_manager.set_robot_controller(self.robot_controller)
        command_manager.set_send_command_signal(self.robot_controller.command_signal)

        # 从数据库加载保存的设置（在设置滑动条之前）
        self.load_speed_from_database()
        self.load_collision_level_from_database()

        # 设置速度的滑动条(范围1-100) - 使用正确的属性名pushSlider_2
        if hasattr(self, 'pushSlider_2'):
            self.pushSlider_2.setEnabled(True)
            self.pushSlider_2.setRange(1, 100)
            self.pushSlider_2.setValue(self.current_speed)  # 使用从数据库加载的值
            self.pushSlider_2.setOrientation(Qt.Orientation.Horizontal)
            self.setup_speed_slider_style()

        # 设置碰撞等级的滑动条(范围5-100) - 使用正确的属性名pushSlider
        if hasattr(self, 'pushSlider'):
            self.pushSlider.setEnabled(True)
            self.pushSlider.setRange(5, 100)
            self.pushSlider.setValue(self.current_collision_level)  # 使用从数据库加载的值

        # 速度和碰撞等级设置信号连接
        self.pushSlider_2.valueChanged.connect(self.update_speed_display_and_send)
        self.pushSlider.valueChanged.connect(self.update_collision_display_and_send)
        self.UPButton_2.clicked.connect(self.increase_speed)
        self.DownButton_2.clicked.connect(self.decrease_speed)
        self.UpcoButton_2.clicked.connect(self.increase_collision_level)
        self.downcopushButton_2.clicked.connect(self.decrease_collision_level)

        # 设置标签点击事件
        if hasattr(self, 'Speedvaluelabel_2'):
            self.Speedvaluelabel_2.mousePressEvent = self.on_speed_label_clicked
        if hasattr(self, 'collilabellabel_2'):
            self.collilabellabel_2.mousePressEvent = self.on_collision_label_clicked

        # 设置标签可直接编辑
        self.setup_direct_edit_labels()

        # 初始化显示
        self.update_speed_display()
        self.update_collision_level_display()

        # 初始化版本信息显示
        self.init_version_display()

        # 连接按钮信号
        self.ModeButton_2.clicked.connect(self.model_change)
        self.EStopButton_9.clicked.connect(self.Estop)
        self.IpButton_2.clicked.connect(self.open_ip_settings)  # 连接IP设置按钮

        # 添加报警相关初始化
        self.current_alarm_count = 0  # 当前未解决的报警数量
        self.latest_alarm_info = ""  # 最新报警信息
        self.latest_alarm_time = ""  # 最新报警时间

        # 连接清除报警按钮
        self.ClearalarmButton_2.clicked.connect(self.clear_alarm_action)

        # 初始化报警显示
        self.update_alarm_display()

        # 初始化时隐藏报警提示
        self.hide_alarm_badge()

        # （间隔执行函数）
        self.db_monitor_timer = QTimer(self)
        self.db_monitor_timer.timeout.connect(self.check_database_field)
        self.db_monitor_timer.timeout.connect(self.changeIP)
        self.db_monitor_timer.start(500)

        # 初始化警报状态
        self.is_alerting = False
        self.alert_overlay = None  # 用于存储闪烁覆盖层
        self.check_database_field()

        # 版本号的判断
        self.db_monitor_timer2 = QTimer(self)
        self.db_monitor_timer2.timeout.connect(self.Version_Number)
        self.db_monitor_timer2.start(300000)  # 5分钟
        self.Version_Number()

        # 初始化按钮背景
        QTimer.singleShot(100, self.update_scale_button_background)

    def init_version_display(self):
        """初始化版本信息显示"""
        try:
            # 设置应用版本
            app_version = "v3.0.0"

            # 初始化显示
            if hasattr(self, 'CV_label2'):
                self.CV_label2.setText("获取中")
                self.CV_label2.setStyleSheet("color: #B4B4B4;")

            if hasattr(self, 'Versionlabel_2'):
                self.Versionlabel_2.setText(app_version)
                self.Versionlabel_2.setStyleSheet("color: #B4B4B4;")

        except Exception as e:
            print(f"初始化版本显示时出错: {e}")

    def update_version_display(self):
        """更新版本信息显示"""
        try:
            # 更新机械臂型号和控制器版本 (CV_label2显示机械臂型号-控制器版本)
            if hasattr(self, 'CV_label2'):
                if hasattr(gv, 'Version_Number_details') and gv.Version_Number_details != "None":
                    self.CV_label2.setText(gv.Version_Number_details)
                    self.CV_label2.setStyleSheet("color: #B4B4B4;")  # 白色表示正常获取
                else:
                    self.CV_label2.setText("None")
                    self.CV_label2.setStyleSheet("color: #B4B4B4;")  # 灰色表示未获取

            # 应用版本 (Versionlabel_2显示应用版本)

            if hasattr(self, 'Versionlabel_2'):
                app_version = "v3.0.0"  # 使用参考代码中的应用版本
                self.Versionlabel_2.setText(f"Version: {app_version}")
                self.Versionlabel_2.setStyleSheet("color: #B4B4B4;")
                self.CVlabel_2.setText(f"CV: {gv.Version_Number_details}")

            print(f"版本信息已更新: {gv.Version_Number_details if hasattr(gv, 'Version_Number_details') else '未获取'}")

        except Exception as e:
            print(f"更新版本显示时出错: {e}")

    def setup_direct_edit_labels(self):
        """设置标签可以直接编辑"""
        # 初始化编辑状态
        self.speed_editing = False
        self.collision_editing = False

        # 为速度标签添加可编辑功能
        if hasattr(self, 'Speedvaluelabel_2'):
            # 设置鼠标指针样式
            self.Speedvaluelabel_2.setCursor(Qt.CursorShape.PointingHandCursor)
            # 添加工具提示
            self.Speedvaluelabel_2.setToolTip("点击直接编辑速度值 (0-100)")

        # 为碰撞等级标签添加可编辑功能
        if hasattr(self, 'collilabellabel_2'):
            # 设置鼠标指针样式
            self.collilabellabel_2.setCursor(Qt.CursorShape.PointingHandCursor)
            # 添加工具提示
            self.collilabellabel_2.setToolTip("点击直接编辑碰撞等级值 (5-100)")

    def on_speed_label_clicked(self, event):
        """速度标签点击事件 - 直接编辑"""
        if hasattr(self, 'speed_editing') and self.speed_editing:
            return  # 如果正在编辑，不处理

        # 设置编辑状态
        self.speed_editing = True

        # 创建输入框
        self.speed_line_edit = QLineEdit(self.Speedvaluelabel_2.parent())
        self.speed_line_edit.setGeometry(self.Speedvaluelabel_2.geometry())

        # 设置输入验证器（只允许0-100的整数）
        validator = QIntValidator(0, 100)
        self.speed_line_edit.setValidator(validator)

        # 设置当前值（去掉%符号）
        current_text = self.Speedvaluelabel_2.text().replace('%', '')
        self.speed_line_edit.setText(current_text)

        # 连接信号
        self.speed_line_edit.editingFinished.connect(self.finish_speed_editing)
        self.speed_line_edit.returnPressed.connect(self.finish_speed_editing)

        # 显示输入框并获取焦点
        self.speed_line_edit.show()
        self.speed_line_edit.setFocus()
        self.speed_line_edit.selectAll()

        # 隐藏原标签
        self.Speedvaluelabel_2.hide()

    def finish_speed_editing(self):
        """完成速度编辑"""
        if not hasattr(self, 'speed_editing') or not self.speed_editing:
            return

        try:
            # 获取输入值
            new_value = int(self.speed_line_edit.text())

            # 验证范围
            if 1 <= new_value <= 100:
                # 更新速度
                self.current_speed = new_value

                # 更新标签显示
                self.Speedvaluelabel_2.setText(f"{new_value}%")

                # 更新滑动条
                if hasattr(self, 'pushSlider_2'):
                    self.pushSlider_2.blockSignals(True)
                    self.pushSlider_2.setValue(new_value)
                    self.pushSlider_2.blockSignals(False)

                # 保存到数据库并应用设置
                self.save_speed_to_database()
                self.apply_speed_setting()

                print(f"通过标签设置速度为: {new_value}%")
            else:
                # 超出范围，恢复原值
                self.Speedvaluelabel_2.setText(f"{self.current_speed}%")

        except ValueError:
            # 输入无效，恢复原值
            self.Speedvaluelabel_2.setText(f"{self.current_speed}%")

        # 显示标签并清理输入框
        self.Speedvaluelabel_2.show()
        self.speed_line_edit.deleteLater()
        self.speed_editing = False

    def on_collision_label_clicked(self, event):
        """碰撞等级标签点击事件 - 直接编辑"""
        if hasattr(self, 'collision_editing') and self.collision_editing:
            return  # 如果正在编辑，不处理

        # 设置编辑状态
        self.collision_editing = True

        # 创建输入框
        self.collision_line_edit = QLineEdit(self.collilabellabel_2.parent())
        self.collision_line_edit.setGeometry(self.collilabellabel_2.geometry())

        validator = QIntValidator(0, 100)
        self.collision_line_edit.setValidator(validator)

        # 设置当前值（去掉%符号）
        current_text = self.collilabellabel_2.text().replace('%', '')
        self.collision_line_edit.setText(current_text)

        # 设置样式
        self.collision_line_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #4CAF50;
                padding: 2px;
                background-color: white;
                selection-background-color: #4CAF50;
            }
        """)

        # 连接信号
        self.collision_line_edit.editingFinished.connect(self.finish_collision_editing)
        self.collision_line_edit.returnPressed.connect(self.finish_collision_editing)

        # 显示输入框并获取焦点
        self.collision_line_edit.show()
        self.collision_line_edit.setFocus()
        self.collision_line_edit.selectAll()

        # 隐藏原标签
        self.collilabellabel_2.hide()

    def finish_collision_editing(self):
        """完成碰撞等级编辑"""
        if not hasattr(self, 'collision_editing') or not self.collision_editing:
            return

        try:
            # 获取输入值
            new_value = int(self.collision_line_edit.text())

            # 验证范围
            if 0 <= new_value <= 100:
                # 更新碰撞等级
                self.current_collision_level = new_value

                # 更新标签显示
                self.collilabellabel_2.setText(f"{new_value}%")

                # 更新滑动条
                if hasattr(self, 'pushSlider'):
                    self.pushSlider.blockSignals(True)
                    self.pushSlider.setValue(new_value)
                    self.pushSlider.blockSignals(False)

                # 保存到数据库并应用设置
                self.save_collision_level_to_database()
                self.apply_collision_level_setting()

                print(f"通过标签设置碰撞等级为: {new_value}%")
            else:
                # 超出范围，恢复原值
                self.collilabellabel_2.setText(f"{self.current_collision_level}%")

        except ValueError:
            # 输入无效，恢复原值
            self.collilabellabel_2.setText(f"{self.current_collision_level}%")

        # 显示标签并清理输入框
        self.collilabellabel_2.show()
        self.collision_line_edit.deleteLater()
        self.collision_editing = False

    def update_window_state(self):
        """更新窗口状态标志并切换背景图片"""
        self.window_is_maximized = self.isMaximized()
        # 同时更新按钮背景
        self.update_scale_button_background()

    def minimize_window(self):
        """最小化窗口"""
        self.showMinimized()


    def toggle_maximize(self):
        """切换最大化/还原窗口"""
        if self.isMaximized():
            # 当前是最大化状态，切换为普通窗口
            self.showNormal()
            # 设置默认大小并居中显示
            self.resize(1024, 768)
            self.center_window()
            # 还原窗口时设置对应的背景图片
            self.Scale_upButton_2.setStyleSheet("""
                        background-image: url(:/big/窗口1.png);
                        background-repeat: no-repeat;
                        background-position: center;
                        background-size: contain;
                        color: red;
                        font-weight: bold;
                    """)

        else:
            # 当前是普通状态，最大化窗口
            self.showMaximized()
            # 最大化窗口时设置对应的背景图片
            self.Scale_upButton_2.setStyleSheet("""
                        background-image: url(:/big/窗口.png);
                        background-repeat: no-repeat;
                        background-position: center;
                        background-size: contain;
                        color: red;
                        font-weight: bold;
                    """)
        # 更新窗口状态
        self.update_window_state()

    def update_scale_button_background(self):
        """根据窗口状态更新放大/缩小按钮的背景图片"""
        try:
            if self.isMaximized():
                # 最大化状态 - 显示还原图标
                self.Scale_upButton_2.setStyleSheet("""
                            background-image: url(:/big/窗口.png);
                            background-repeat: no-repeat;
                            background-position: center;
                            background-size: contain;
                            color: red;
                            font-weight: bold;
                        """)
            else:
                # 普通状态 - 显示最大化图标
                self.Scale_upButton_2.setStyleSheet("""
                            background-image: url(:/big/窗口1.png);
                            background-repeat: no-repeat;
                            background-position: center;
                            background-size: contain;
                            color: red;
                            font-weight: bold;
                        """)
        except Exception as e:
            print(f"更新按钮背景时出错: {e}")

    def center_window(self):
        """将窗口居中显示"""
        screen = QGuiApplication.primaryScreen()
        if screen:
            screen_geometry = screen.geometry()
            window_geometry = self.geometry()

            # 计算居中位置
            x = (screen_geometry.width() - window_geometry.width()) // 2
            y = (screen_geometry.height() - window_geometry.height()) // 2

            # 移动窗口到居中位置
            self.move(x, y)

    def close_window(self):
        """关闭窗口"""
        self.close()

    def set_drag_resize_enabled(self, enabled: bool):
        """设置是否允许拖动时自动缩小窗口

        Args:
            enabled (bool): True-允许拖动缩小, False-禁止拖动缩小
        """
        self.allow_drag_resize = enabled
        print(f"拖动缩小功能已{'启用' if enabled else '禁用'}")

    def is_drag_resize_enabled(self) -> bool:
        """检查是否允许拖动缩小

        Returns:
            bool: 当前拖动缩小功能状态
        """
        return self.allow_drag_resize

    def mousePressEvent(self, event):
        """处理鼠标按下事件以支持窗口拖动（限制最大化状态下的拖动）"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.globalPosition().toPoint()

            # 检查是否点击在标题栏区域（这里可以根据需要调整判断逻辑）
            if event.position().y() < 50:  # 假设标题栏高度为50像素
                # 如果窗口是最大化状态且不允许拖动缩放，则不启用拖动
                if self.isMaximized() and not self.allow_drag_resize:
                    self.is_dragging = False
                    self.drag_position = None
                else:
                    self.is_dragging = True
                    self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
                event.accept()
            else:
                self.is_dragging = False
                self.drag_position = None
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 拖动窗口（限制自动缩小功能）"""
        if event.buttons() == Qt.MouseButton.LeftButton and self.is_dragging:
            current_pos = event.globalPosition().toPoint()

            # 计算移动距离
            move_distance = (current_pos - self.drag_start_position).manhattanLength()

            # 如果允许拖动缩放且窗口是最大化的，且移动距离足够大，先还原窗口
            if self.allow_drag_resize and self.isMaximized() and move_distance > 10:
                # 还原窗口
                self.showNormal()
                self.window_is_maximized = False
                # 设置窗口大小并居中
                self.resize(1024, 768)
                self.center_window()

                # 重新计算拖动位置 - 假设鼠标在窗口顶部中央
                window_center_x = self.frameGeometry().center().x()
                self.drag_position = current_pos - self.frameGeometry().topLeft()
                # 调整拖动位置，让鼠标在标题栏中央
                self.drag_position.setX(window_center_x - self.frameGeometry().left())

            # 只有在非最大化状态下才允许拖动移动窗口
            if not self.isMaximized() and self.drag_position is not None:
                # 普通拖动 - 计算新的窗口位置
                new_pos = current_pos - self.drag_position
                self.move(new_pos)

            event.accept()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_dragging = False
            self.drag_position = None
            self.drag_start_position = None
            event.accept()

    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)

        self.update_window_state()

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self.update_window_state()

    def changeEvent(self, event):
        """窗口状态改变事件"""
        super().changeEvent(event)
        if event.type() == event.Type.WindowStateChange:
            self.update_window_state()

    def get_error_code_dict(self):
        """返回错误码映射字典"""
        return {
            0: QCoreApplication.translate("ErrorCode", "无故障"),
            1: QCoreApplication.translate("ErrorCode", "驱动器故障"),
            2: QCoreApplication.translate("ErrorCode", "超出软限位故障"),
            3: QCoreApplication.translate("ErrorCode", "碰撞故障"),
            4: QCoreApplication.translate("ErrorCode", "奇异位姿"),
            5: QCoreApplication.translate("ErrorCode", "从站错误"),
            6: QCoreApplication.translate("ErrorCode", "指令点错误"),
            7: QCoreApplication.translate("ErrorCode", "IO错误"),
            8: QCoreApplication.translate("ErrorCode", "夹爪错误"),
            9: QCoreApplication.translate("ErrorCode", "文件错误"),
            10: QCoreApplication.translate("ErrorCode", "参数错误"),
            11: QCoreApplication.translate("ErrorCode", "扩展轴超出软限位错误"),
            12: QCoreApplication.translate("ErrorCode", "关节配置警告")
        }

    def handle_alarm_logic(self, error_code):
        """处理报警逻辑"""
        try:
            if error_code == 0:
                return  # 无错误，不处理

            # 获取当前时间
            current_time = time.strftime("%Y/%m/%d %H:%M:%S", time.localtime())

            # 获取报警描述
            error_code_dict = self.get_error_code_dict()
            alarm_des = error_code_dict.get(error_code, f"检测到未知错误码：{error_code}")

            # 检查数据库中是否存在相同alarm_code的未解决记录
            existing_records = self.db_tool.select(
                "alarm_record",
                fields=["status"],
                condition=f"alarm_code = '{error_code}' AND status = 0"
            )

            # 如果没有未解决的相同错误码记录，则插入新记录
            if not existing_records:
                insert_success = self.db_tool.insert(
                    table="alarm_record",
                    data={
                        "time": current_time,
                        "alarm_code": str(error_code),
                        "alarm_des": alarm_des,
                        "status": 0  # 0表示未解决状态
                    }
                )

                if insert_success:
                    # 更新最新报警信息
                    self.latest_alarm_info = alarm_des
                    self.latest_alarm_time = current_time
                    # 更新报警显示
                    self.update_alarm_display()

        except Exception as e:
            print(f"处理报警逻辑时出错: {e}")

    def update_alarm_display(self):
        """更新报警显示"""
        try:
            # 查询未解决的报警数量
            unresolved_alarms = self.db_tool.select(
                "alarm_record",
                fields=["COUNT(*) as count"],
                condition="status = 0"
            )

            if unresolved_alarms:
                self.current_alarm_count = unresolved_alarms[0]["count"]
            else:
                self.current_alarm_count = 0

            # 获取最新的未解决报警信息
            if self.current_alarm_count > 0:
                latest_alarm = self.db_tool.select(
                    "alarm_record",
                    fields=["alarm_des", "time"],
                    condition="status = 0 ORDER BY id DESC LIMIT 1"
                )

                if latest_alarm:
                    self.latest_alarm_info = latest_alarm[0]["alarm_des"]
                    self.latest_alarm_time = latest_alarm[0]["time"]

                    # 分离日期和时间
                    date_part = self.latest_alarm_time.split(" ")[0]  # 年月日
                    time_part = self.latest_alarm_time.split(" ")[1]  # 时分秒

                    # 保留背景图片，只修改文字颜色
                    self.AlarmLabel.setStyleSheet("""
                        background-image: url(:/Alarm/报警信息.png);
                        background-repeat: no-repeat;
                        background-position: center;
                        background-size: contain;
                        color: red;
                        font-weight: bold;
                    """)

                    # 显示报警数量提示
                    self.show_alarm_badge()
                    # 保留背景图片，只修改文字颜色
                    self.ClearalarmButton_2.setStyleSheet("""
                        background-image: url(:/clearAlarm/清除报警(1).png);
                        background-repeat: no-repeat;
                        background-position: center;
                        background-size: contain;
                        color: red;
                    """)

                    # 报警信息标签 - 显示3次最后一次报警信息（红色）
                    alarm_text_3times = f"{self.latest_alarm_info}{self.latest_alarm_info}{self.latest_alarm_info}\n{self.latest_alarm_info}{self.latest_alarm_info}{self.latest_alarm_info}"
                    self.Alarminforlabel_2.setText(alarm_text_3times)
                    self.Alarminforlabel_2.setStyleSheet("color: red;")

                    # 日期和时间标签 - 一直显示 #B4B4B4
                    self.datalabel_2.setText(date_part)
                    self.datalabel_2.setStyleSheet("color: red;")

                    self.hourlabel_2.setText(time_part)
                    self.hourlabel_2.setStyleSheet("color: red;")
                else:
                    self.reset_alarm_display()
            else:
                self.reset_alarm_display()

        except Exception as e:
            print(f"更新报警显示时出错: {e}")

    def show_alarm_badge(self):
        """显示报警数量提示（红色数字1）"""
        if not hasattr(self, 'alarm_badge'):
            # 创建报警提示标签
            self.alarm_badge = QLabel(self.AlarmLabel)
            self.alarm_badge.setText("1")
            self.alarm_badge.setStyleSheet("""
                QLabel {
                    background-color: red;
                    color: white;
                    border-radius: 10px;
                    font-weight: bold;
                    font-size: 12px;
                    min-width: 20px;
                    max-width: 20px;
                    min-height: 20px;
                    max-height: 20px;
                    text-align: center;
                }
            """)
            self.alarm_badge.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 定位到右上角
        parent_size = self.AlarmLabel.size()
        badge_size = 20
        x = parent_size.width() - badge_size - 2
        y = 2
        self.alarm_badge.setGeometry(x, y, badge_size, badge_size)
        self.alarm_badge.show()

    def hide_alarm_badge(self):
        """隐藏报警数量提示"""
        if hasattr(self, 'alarm_badge'):
            self.alarm_badge.hide()

    def reset_alarm_display(self):
        """重置报警显示"""
        self.current_alarm_count = 0
        self.latest_alarm_info = ""
        self.latest_alarm_time = ""

        # 保留背景图片，只修改文字颜色
        self.AlarmLabel.setStyleSheet("""
            background-image: url(:/Alarm/报警信息.png);
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            color: black;
        """)

        # 隐藏报警数量提示
        self.hide_alarm_badge()
        # 保留背景图片，只修改文字颜色
        self.ClearalarmButton_2.setStyleSheet("""
            background-image: url(:/clearAlarm/清除报警(1).png);
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            color: black;
        """)

        # 查询最后一次报警信息（包括已解决的）
        try:
            last_alarm = self.db_tool.select(
                "alarm_record",
                fields=["alarm_des", "time"],
                condition="1=1 ORDER BY id DESC LIMIT 1"
            )

            if last_alarm:
                # 有历史报警信息，显示3次最后一次报警信息
                last_alarm_des = last_alarm[0]["alarm_des"]
                last_time = last_alarm[0]["time"]

                # 报警信息标签 - 显示3次最后一次报警信息（#B4B4B4颜色）
                alarm_text_3times = f"{last_alarm_des}{last_alarm_des}{last_alarm_des}\n{last_alarm_des}{last_alarm_des}{last_alarm_des}"
                self.Alarminforlabel_2.setText(alarm_text_3times)
                self.Alarminforlabel_2.setStyleSheet("color: #B4B4B4;")

                # 设置日期和时间
                date_part = last_time.split(" ")[0]
                time_part = last_time.split(" ")[1]
                self.datalabel_2.setText(date_part)
                self.hourlabel_2.setText(time_part)
            else:
                # 没有任何报警信息，显示3次"无报警信息"
                no_alarm_text_3times = "无报警信息无报警信息无报警信息\n无报警信息无报警信息无报警信息"
                self.Alarminforlabel_2.setText(no_alarm_text_3times)
                self.Alarminforlabel_2.setStyleSheet("color: #B4B4B4;")

                # 设置占位符时间
                self.datalabel_2.setText("----/--/--")
                self.hourlabel_2.setText("--:--:--")
                self.datalabel_2.setStyleSheet("color: #B4B4B4;")
                self.hourlabel_2.setStyleSheet("color: #B4B4B4;")

        except Exception as e:
            print(f"查询最后报警时间出错: {e}")
            # 出错时显示无报警信息
            no_alarm_text_3times = "无报警信息无报警信息无报警信息\n无报警信息无报警信息无报警信息"
            self.Alarminforlabel_2.setText(no_alarm_text_3times)
            self.Alarminforlabel_2.setStyleSheet("color: #B4B4B4;")

            self.datalabel_2.setText("----/--/--")
            self.hourlabel_2.setText("--:--:--")

            self.datalabel_2.setStyleSheet("color: #B4B4B4;")
            self.hourlabel_2.setStyleSheet("color: #B4B4B4;")

    def clear_alarm_action(self):
        """清除报警按钮点击事件"""
        try:
            # 检查是否有未解决的报警
            unresolved_count = self.db_tool.select(
                "alarm_record",
                fields=["COUNT(*) as count"],
                condition="status = 0"
            )

            if unresolved_count and unresolved_count[0]["count"] > 0:
                # 更新数据库中所有未解决的报警状态为已解决
                self.db_tool.update(
                    table="alarm_record",
                    data={"status": 1},
                    condition="status = 0"
                )

                # 发送清除错误指令到机器人
                if gv.is_8080_connected and gv.is_8083_connected:
                    data = 'RESETALLERROR()'
                    info = '/f/bIII107III' + '107' + 'III' + str(len(data)) + 'III' + data + 'III/b/f'
                    self.send_cmd.emit(info)

                # 重置报警显示
                self.reset_alarm_display()

                # 显示成功提示
                QMessageBox.information(
                    self,
                    QCoreApplication.translate("Success", "操作成功"),
                    QCoreApplication.translate("Message", f"已成功清除 {unresolved_count[0]['count']} 个报警信息")
                )

                print("报警已清除")
            else:
                # 没有未解决的报警
                QMessageBox.information(
                    self,
                    QCoreApplication.translate("Info", "提示"),
                    QCoreApplication.translate("Message", "当前没有需要清除的报警信息")
                )

        except Exception as e:
            print(f"清除报警时出错: {e}")
            QMessageBox.critical(
                self,
                QCoreApplication.translate("Error", "操作失败"),
                QCoreApplication.translate("Message", f"清除报警时发生异常：{str(e)}")
            )

    def load_speed_from_database(self):
        """从数据库加载速度设置 - 使用 speed_settings 表"""
        try:
            record = self.db_tool.select("speed_settings",
                                         fields=["speed"],  # 修正字段名为 speed
                                         condition="1=1 ORDER BY id DESC LIMIT 1")

            if record and record[0]["speed"]:
                self.current_speed = int(record[0]["speed"])
                gv.GLOBAL_SPEED_ = str(self.current_speed)
            else:
                # 如果没有记录，插入默认值
                self.current_speed = 30
                gv.GLOBAL_SPEED_ = "30"
                self.db_tool.insert("speed_settings", {"speed": "30"})

        except Exception as e:
            print(f"加载速度设置失败: {e}")
            self.current_speed = 30
            gv.GLOBAL_SPEED_ = "30"

    def save_speed_to_database(self):
        """保存速度设置到数据库"""
        try:
            # 删除旧记录并插入新记录
            self.db_tool.execute_sql("DELETE FROM speed_settings")
            data = {"speed": str(self.current_speed)}  # 修正字段名为 speed
            self.db_tool.insert("speed_settings", data)

            # 更新全局变量
            gv.GLOBAL_SPEED_ = str(self.current_speed)

        except Exception as e:
            print(f"保存速度设置失败: {e}")

    def increase_speed(self):
        """增加速度"""
        print(f"[按钮点击] 增加速度: {self.current_speed}% → ", end="")
        if self.current_speed < 100:
            self.current_speed = min(100, self.current_speed + 5)
            print(f"{self.current_speed}%")
            self.update_speed_slider_from_button()
        else:
            print("已达到最大值")

    def decrease_speed(self):
        """减少速度"""
        print(f"[按钮点击] 减少速度: {self.current_speed}% → ", end="")
        if self.current_speed > 1:
            self.current_speed = max(1, self.current_speed - 5)
            print(f"{self.current_speed}%")
            self.update_speed_slider_from_button()
        else:
            print("已达到最小值")

    def reset_speed(self):
        """重置速度为默认值"""
        self.current_speed = 30
        self.update_speed_slider_from_button()

    def open_speed_slider(self):
        """打开速度滑动条设置对话框"""
        dialog = SpeedSliderDialog(self, self.current_speed, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_speed = dialog.get_speed()
            if new_speed != self.current_speed:
                self.current_speed = new_speed
                self.update_speed_slider_from_button()

    def apply_speed_setting(self):
        """应用速度设置到机器人 - 参考SettingsLogic的方式"""
        if gv.is_8083_connected and gv.is_8080_connected:
            try:
                # 构建速度设置命令
                speed_cmd = RobotCommand.get_command_str('SetSpeed', {'speed': str(self.current_speed)}, 206)

                if speed_cmd:
                    result = command_manager.send_and_wait(speed_cmd, command_type="速度设置")
                    if result.get("success"):
                        print(f"已设置全局速度为: {self.current_speed}%")
                    else:
                        print(f"设置速度失败: {result.get('error', '未知错误')}")

            except Exception as e:
                print(f"设置速度失败: {e}")

    def open_ip_settings(self):
        """打开IP设置对话框"""
        try:
            # 获取当前IP
            current_ip = ""
            if self.current_ip:
                current_ip = self.current_ip[0]['ip'] if isinstance(self.current_ip, list) else self.current_ip

            # 创建并显示对话框
            dialog = IpSettingsDialog(self, current_ip, self)
            dialog.exec()

        except Exception as e:
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"打开IP设置对话框失败:\n{e}")

    def on_ip_changed(self, new_ip):
        """IP更改后的回调函数"""
        # 更新内部变量
        self.current_ip = [{'ip': new_ip}]
        # 更新显示
        self.update_ip_display(new_ip)

    def update_ip_display(self, ip_address):
        """更新IP地址显示"""
        # 根据你的UI中的标签名称来更新显示
        # 如果有专门的IP显示标签，可以在这里添加
        if hasattr(self, 'IpDisplayLabel'):
            self.IpDisplayLabel.setText(f"IP: {ip_address}")

    def changeIP(self):
        if gv.IP_change:
            # 1. 从数据库获取新IP - 使用正确的表名
            record = self.db_tool.select("ip_settings", condition="1=1 ORDER BY id DESC LIMIT 1")
            if not record:
                return
            new_ip = record[0]['ip']

            # 2. 更新IP显示
            self.update_ip_display(new_ip)

            # 3. 调用 reconfigure 方法，而不是重新创建实例
            RobotTcpController.get_instance().reconfigure(new_ip, 8080)
            Robot8083Socket.get_instance().reconfigure(new_ip, 8083)

            # 4. 更新界面中的IP记录和重置标志
            self.current_ip = record
            gv.IP_change = False

            # 5. 重新开始重连定时器
            if not self.reconnect_timer.isActive():
                self.reconnect_timer.start(3000)

    def Version_Number(self):
        if gv.is_8080_connected and gv.is_8083_connected:
            try:
                cmd = RobotCommand.get_command_str('GetSoftwareVersion', None, 905)
                result = command_manager.send_and_wait(cmd, command_type="版本查询")
                if result.get("success"):
                    import re
                    response_str = result['response']
                    match = re.search(r',(v[\d.]+),', response_str)

                    if match:
                        full_version = match.group(1)
                        version_match = re.search(r'v(\d+)\.(\d+)', full_version)
                        if version_match:
                            major = int(version_match.group(1))
                            minor = int(version_match.group(2))
                            main_version = f"{major}.{minor}"
                            RobotCommand.Version_change(main_version)
                            match = re.search(r"([A-Z0-9]{2,})", response_str.split("III")[4])
                            model = match.group(1)
                            gv.Version_Number_details = model + '-' + full_version

                            # 更新UI中的版本信息显示
                            self.update_version_display()

                            if main_version == "3.8":
                                print("检测到3.8版本")
                                global Acceleration_smoothing_change
                                if Acceleration_smoothing_change == False:
                                    Acceleration_smoothing_change = True
                                    cmd = RobotCommand.get_command_str('AccSmoothEnd', None, 1147)
                                    result = command_manager.send_and_wait(cmd, command_type="加速度平滑设置")
                                    if not result.get("success"):
                                        print(f"设置加速度平滑失败: {result.get('error', '未知错误')}")
                            elif main_version == "3.7":
                                print("检测到3.7版本")
                            else:
                                print(f"检测到其他版本: {main_version}")
                        else:
                            print(f"无法解析版本号: {full_version}")
                            gv.Version_Number_details = "None"
                    else:
                        print("未找到版本号信息")
                        gv.Version_Number_details = "None"
                else:
                    print(f"获取版本号失败: {result.get('error', '未知错误')}")
            except Exception as e:
                pass

    def check_database_field(self):
        try:
            global ERROR_CODE_
            if ERROR_CODE_ == "":
                pass
            elif int(ERROR_CODE_) == 0:
                if self.is_alerting:
                    self.stop_alert()
            else:
                if not self.is_alerting:
                    self.start_alert()

            # 添加报警显示更新
            self.update_alarm_display()

        except Exception as e:
            pass

    def start_alert(self):
        """启动界面闪烁警报"""
        self.is_alerting = True
        self.blink_timer = QTimer(self)
        self.blink_state = False

    def stop_alert(self):
        """停止警报"""
        self.is_alerting = False
        if hasattr(self, 'blink_timer') and self.blink_timer:
            self.blink_timer.stop()
            self.blink_timer.deleteLater()
        if self.alert_overlay:
            self.alert_overlay.deleteLater()
            self.alert_overlay = None

    def _try_reconnect(self):
        """尝试重新连接机器人"""
        if not self.robot_controller.is_connecting:
            self.reconnect.emit()

    def _on_robot_data(self, data):
        pass

    def message_8083(self, data):
        try:
            global ERROR_CODE_
            ERROR_CODE_ = data.get('error_code', 0)

            # global DRAG_SIGN_
            # DRAG_SIGN_ = data.get("DI0")
            #
            # global DRAG_SIGN_
            # DRAG_SIGN_ = data.get("DI0")

            # 处理报警逻辑
            self.handle_alarm_logic(ERROR_CODE_)
            # 处理报警逻辑
            self.handle_alarm_logic(ERROR_CODE_)

            # 安全检查 - 确保robot_status已初始化且有program_state属性
            program_state = getattr(self.robot_status, 'program_state', 0) if hasattr(self, 'robot_status') else 0

            if program_state != 1:
                # 将modelChange 设置为不可点击状态
                self.ModeButton_2.setEnabled(False)
            else:
                self.ModeButton_2.setEnabled(True)

            if data.get("robot_mode") == 0:
                self.current_robot_mode = 0
                self.ModeButton_2.setText("")  # 清空文字，使用背景图
                self.update_mode_button_background("auto")
            elif data.get("robot_mode") == 1:
                self.current_robot_mode = 1
                self.ModeButton_2.setText("")  # 清空文字，使用背景图
                self.update_mode_button_background("manual")
            elif data.get("robot_mode") == 2:
                self.current_robot_mode = 2
                self.ModeButton_2.setText("")  # 清空文字，使用背景图
                self.update_mode_button_background("drag")

            self.ModeButton_2.setEnabled(program_state == 1)
        except Exception as e:
            print(f"处理8083消息时出错: {e}")

    def model_change(self):
        if self.robot_status.program_state != 1:
            QMessageBox.warning(
                self,
                QCoreApplication.translate("Warning", "提示"),
                QCoreApplication.translate("Reason", "当前状态不允许切换模式"))
            return

        # 根据当前模式变量判断，而不是按钮文字
        if self.current_robot_mode == 0:  # 当前是自动模式
            # 切换到手动模式
            mode_cmd = RobotCommand.get_command_str('Mode', {'mode': '1'}, 20)
            command_manager.send_command(mode_cmd, "模式切换-手动")
        elif self.current_robot_mode == 1:  # 当前是手动模式
            # 切换到自动模式
            mode_cmd = RobotCommand.get_command_str('Mode', {'mode': '0'}, 20)
            command_manager.send_command(mode_cmd, "模式切换-自动")
        elif self.current_robot_mode == 2:  # 当前是拖动模式
            QMessageBox.warning(self, "提示", "拖动中不允许切换模式")
            return

    def init_pages(self):
        self.pages = {
            'run_page': RunLogic(),
            'formulation_page': FormulaLogic(parent=self),
            'teaching_page': TeachingLogic(),
            'IO_page': IOLogic(),
            'log_page': LogLogic(),
        }

        # 设置全局IO逻辑实例
        import src.common.global_vars as gv
        gv.io_logic_instance = self.pages['IO_page']

        #  初始化配方层样式页面
        self.layer_style_page = FormulaLayerStyleLogic()

        for name, page in self.pages.items():
            self.stackedWidget.addWidget(page)

        self.stackedWidget.addWidget(self.layer_style_page)

    def init_navigation(self):
        nav_buttons = {
            'RunButton_2': 'run_page',
            'FormulaButton_2': 'formulation_page',
            'TeachingButton_2': 'teaching_page',
            'IOButton_2': 'IO_page',
            'LogButton_2': 'log_page'
        }
        for btn_name, page_name in nav_buttons.items():
            button = self.findChild(QPushButton, btn_name)
            if button:
                button.setCheckable(True)
                button.clicked.connect(
                    lambda _, p=page_name: self.switch_page(p)
                )

    def setup_page_connections(self):
        """设置页面间的信号槽连接"""
        # 连接formula页面的编辑信号
        if 'formulation_page' in self.pages:
            self.pages['formulation_page'].edit_signal.connect(self.switch_to_edit_page)

        # 连接层样式页面的数据更新信号
        if hasattr(self, 'layer_style_page'):
            self.layer_style_page.formula_data_updated.connect(self.on_formula_data_updated)

    def switch_to_edit_page(self, row):
        """切换到编辑页面并加载配方数据"""
        try:
            # 获取配方页面的当前编辑配方信息
            formula_page = self.pages.get('formulation_page')
            if formula_page and hasattr(formula_page, 'current_editing_formula_id'):
                formula_id = formula_page.current_editing_formula_id
                formula_name = getattr(formula_page, 'current_editing_formula_name', f'配方{formula_id}')

                # 设置层样式页面的当前编辑配方
                self.layer_style_page.set_current_editing_formula(formula_id, formula_name)

                print(f"切换到编辑页面，加载配方: {formula_name} (ID: {formula_id})")
            else:
                print("未找到要编辑的配方信息")

            # 切换到层样式页面
            self.stackedWidget.setCurrentWidget(self.layer_style_page)

        except Exception as e:
            print(f"切换到编辑页面时发生错误: {e}")
            # 即使出错也要切换页面
            self.stackedWidget.setCurrentWidget(self.layer_style_page)

    def on_formula_data_updated(self, formula_id):
        """处理配方数据更新信号"""
        try:
            print(f"配方数据已更新，配方ID: {formula_id}")

            # 刷新配方页面的数据
            formula_page = self.pages.get('formulation_page')
            if formula_page and hasattr(formula_page, 'refresh_table_data'):
                formula_page.refresh_table_data()
                print("配方表格数据已刷新")

        except Exception as e:
            print(f"处理配方数据更新时发生错误: {e}")

    def switch_page(self, page_name):
        # 取消所有按钮选中状态并恢复默认背景图
        for btn in self.findChildren(QPushButton):
            if btn.isCheckable():
                btn.setChecked(False)
                self.set_button_background(btn, False)

        # 设置当前按钮选中状态并切换背景图
        # 页面名称到按钮名称的映射
        page_to_button_map = {
            'run_page': 'RunButton_2',
            'formulation_page': 'FormulaButton_2',
            'teaching_page': 'TeachingButton_2',
            'IO_page': 'IOButton_2',
            'log_page': 'LogButton_2'
        }

        button_name = page_to_button_map.get(page_name)
        if button_name:
            current_button = self.findChild(QPushButton, button_name)
            if current_button:
                current_button.setChecked(True)
                self.set_button_background(current_button, True)

        # 切换页面
        if page_name in self.pages:
            self.stackedWidget.setCurrentWidget(self.pages[page_name])

    def set_button_background(self, button, is_selected):
        """设置按钮背景图，根据选中状态切换图片"""
        if not button:
            return

        button_name = button.objectName()

        # 定义按钮名称到资源前缀的映射
        button_resource_map = {
            'RunButton_2': 'run',
            'FormulaButton_2': 'formulation',
            'TeachingButton_2': 'teach',
            'IOButton_2': 'IO',
            'LogButton_2': 'log'
        }

        # 定义按钮名称到图片名称的映射
        button_image_map = {
            'RunButton_2': '运行',
            'FormulaButton_2': '配方',
            'TeachingButton_2': '示教',
            'IOButton_2': 'I／O',
            'LogButton_2': '日志'
        }

        if button_name in button_resource_map:
            resource_prefix = button_resource_map[button_name]
            image_name = button_image_map[button_name]

            if is_selected:
                image_path = f":/{resource_prefix}/{image_name}.png"
            else:
                image_path = f":/{resource_prefix}/{image_name}(1).png"

            # 只修改背景图，保留其他样式
            button.setStyleSheet(f"""
                background-image: url({image_path});
                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
                border: none;
            """)

    def init_button_backgrounds(self):
        # 获取所有导航按钮
        nav_buttons = ['RunButton_2', 'FormulaButton_2', 'IOButton_2', 'TeachingButton_2', 'LogButton_2']

        for button_name in nav_buttons:
            button = self.findChild(QPushButton, button_name)
            if button:
                self.set_button_background(button, False)

    def update_ip_button_background(self, connection_status):
        """根据连接状态更新IP按钮背景图"""
        ip_button = self.findChild(QPushButton, "IpButton_3")
        if not ip_button:
            return

        # 根据连接状态选择对应的背景图
        if connection_status == "connected":
            # 已连接状态
            image_path = ":/connect/已连接.png"
        elif connection_status == "disconnected":
            # 已断开状态
            image_path = ":/connect/已断开.png"
        elif connection_status == "connecting":
            # 连接中状态
            image_path = ":/connect/连接中.png"
        else:
            return

        # 设置按钮背景图
        ip_button.setStyleSheet(f"""
            background-image: url({image_path});
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            border: none;
        """)

    def update_mode_button_background(self, mode):
        """根据机器人模式更新模式按钮背景图"""
        mode_button = self.findChild(QPushButton, "ModeButton_2")
        if not mode_button:
            return

        # 根据模式选择对应的背景图
        if mode == "auto":
            # 自动模式
            image_path = ":/mode/自动(1).png"
        elif mode == "manual":
            # 手动模式
            image_path = ":/mode/手动.png"
        elif mode == "drag":
            # 拖动模式
            image_path = ":/mode/拖动.png"
        else:
            return

        # 设置按钮背景图，隐藏文字（背景图上已有文字）
        mode_button.setStyleSheet(f"""
            QPushButton {{
                background-image: url({image_path});
                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
                border: none;
                color: transparent;
            }}
            QPushButton:hover {{
                background-color: rgba(255, 255, 255, 0.1);
            }}
            QPushButton:pressed {{
                background-color: rgba(0, 0, 0, 0.1);
            }}
        """)

    def update_time(self):
        current_time = QDateTime.currentDateTime().toString("yyyy/MM/dd hh:mm:ss")
        current_font = self.TimeLabel_2.font()
        self.TimeLabel_2.setText(current_time)
        self.TimeLabel_2.setStyleSheet("background: transparent;color: #B4B4B4;")

        self.TimeLabel_2.setFont(current_font)

    def _on_8080_connected(self):
        """处理8080端口连接成功的事件"""
        gv.is_8080_connected = True
        self._update_overall_connection_status()
        self.Version_Number()
        # 连接成功后，应用当前的速度设置
        self.apply_speed_setting()
        # 连接成功后，应用当前的碰撞等级设置
        self.apply_collision_level_setting()
        # 连接成功后，更新版本信息显示
        self.update_version_display()

    def _on_8080_disconnected(self):
        """处理8080端口断开连接的事件"""
        gv.is_8080_connected = False
        self._update_overall_connection_status()
        # 断开连接后，重置版本显示为获取中状态
        if hasattr(self, 'CV_label2'):
            self.CV_label2.setText("连接中")
            self.CV_label2.setStyleSheet("color: #B4B4B4;")

    def _on_8083_connected(self):
        """处理8083端口连接成功的事件"""
        gv.is_8083_connected = True
        self._update_overall_connection_status()

    def _on_8083_disconnected(self):
        """处理8083端口断开连接的事件"""
        gv.is_8083_connected = False
        self._update_overall_connection_status()

    def _update_overall_connection_status(self):
        """
        根据两个端口的综合连接状态，更新UI显示。
        支持三种状态：已连接、连接中、已断开
        这是唯一的UI更新入口。
        """
        # 声明全局变量
        global Acceleration_smoothing_change

        # 获取当前IP地址
        current_ip = "未知"
        if self.current_ip:
            current_ip = self.current_ip[0]['ip'] if isinstance(self.current_ip, list) else self.current_ip

        # 判断连接状态并更新UI
        if gv.is_8080_connected and gv.is_8083_connected:
            # 状态1：已连接 - 两个端口都连接成功
            self.Ipinforlabel_2.setText(QCoreApplication.translate("ConnectionStatus", f"已连接"))
            self.Ipinforlabel_2.setStyleSheet("color: green; font-weight: bold;")
            # 更新IP按钮背景图为已连接状态
            self.update_ip_button_background("connected")
            # 连接成功后，停止重连定时器
            if self.reconnect_timer.isActive():
                self.reconnect_timer.stop()

        elif not gv.is_8080_connected and not gv.is_8083_connected:
            # 状态2：已断开 - 两个端口都断开
            self.Ipinforlabel_2.setText(QCoreApplication.translate("ConnectionStatus", f"已断开"))
            self.Ipinforlabel_2.setStyleSheet("color: red; font-weight: bold;")
            # 更新IP按钮背景图为已断开状态
            self.update_ip_button_background("disconnected")
            # 重置加速度平滑设置标志
            if Acceleration_smoothing_change == True:
                Acceleration_smoothing_change = False
            # 启动重连定时器
            if not self.reconnect_timer.isActive():
                self.reconnect_timer.start(3000)

        else:
            # 状态3：连接中 - 部分连接（一个端口连接，另一个断开）
            self.Ipinforlabel_2.setText(QCoreApplication.translate("ConnectionStatus", f"连接中"))
            self.Ipinforlabel_2.setStyleSheet("color: orange; font-weight: bold;")
            # 更新IP按钮背景图为连接中状态
            self.update_ip_button_background("connecting")
            # 重置加速度平滑设置标志
            if Acceleration_smoothing_change == True:
                Acceleration_smoothing_change = False
            # 确保重连定时器在工作
            if not self.reconnect_timer.isActive():
                self.reconnect_timer.start(3000)

    def Estop(self):
        global STOP
        STOP = True
        cmd = RobotCommand.get_command_str('STOP', None, 102)
        self.send_cmd.emit(cmd)
        QMessageBox.information(
            self,
            QCoreApplication.translate("MainWindow", "操作确认"),
            QCoreApplication.translate("MainWindow", "紧急停止已触发！"),
            QMessageBox.StandardButton.Ok
        )

    def load_collision_level_from_database(self):
        """从数据库加载碰撞等级设置 - 参考1.py的方式，支持多表读取"""
        try:
            # 1. 优先从robot_settings表读取（参考1.py的结构）
            robot_record = self.db_tool.select("robot_settings",
                                               fields=["collision_grade_type", "j1", "j2", "j3", "j4", "j5", "j6"],
                                               condition="collision_grade_type IS NOT NULL ORDER BY id DESC LIMIT 1")

            if robot_record and robot_record[0]["collision_grade_type"] == "自定义百分比":
                # 从robot_settings表读取，使用J1的值作为统一的碰撞等级
                self.current_collision_level = int(robot_record[0]["j1"])
                return

            # 2. 如果robot_settings表没有数据，从collision_settings表读取（保持兼容性）
            collision_record = self.db_tool.select("collision_settings",
                                                   fields=["collision_level", "collision_grade_type"],
                                                   condition="1=1 ORDER BY id DESC LIMIT 1")
            if collision_record and collision_record[0]["collision_level"]:
                self.current_collision_level = int(collision_record[0]["collision_level"])
                return

            # 3. 如果两个表都没有记录，插入默认值
            self.current_collision_level = 60
            self.save_collision_level_to_database()

        except Exception as e:
            QMessageBox.warning(self, "加载失败", f"加载碰撞等级设置失败: {e}")
            self.current_collision_level = 60

    def save_collision_level_to_database(self):
        """保存碰撞等级设置到数据库"""
        try:
            # 删除旧记录并插入新记录
            self.db_tool.execute_sql("DELETE FROM collision_settings")
            data = {
                "collision_grade_type": "自定义百分比",
                "j1": str(self.current_collision_level),
                "j2": str(self.current_collision_level),
                "j3": str(self.current_collision_level),
                "j4": str(self.current_collision_level),
                "j5": str(self.current_collision_level),
                "j6": str(self.current_collision_level)
            }
            self.db_tool.insert("collision_settings", data)

        except Exception as e:
            pass  # 不显示错误信息

    def increase_collision_level(self):
        """增加碰撞等级"""
        print(f"[按钮点击] 增加碰撞等级: {self.current_collision_level}% → ", end="")
        if self.current_collision_level < 100:
            self.current_collision_level = min(100, self.current_collision_level + 10)
            print(f"{self.current_collision_level}%")
            self.update_collision_slider_from_button()
        else:
            print("已达到最大值")

    def decrease_collision_level(self):
        """减少碰撞等级"""
        print(f"[按钮点击] 减少碰撞等级: {self.current_collision_level}% → ", end="")
        if self.current_collision_level > 5:
            self.current_collision_level = max(5, self.current_collision_level - 10)
            print(f"{self.current_collision_level}%")
            self.update_collision_slider_from_button()
        else:
            print("已达到最小值")

    def reset_collision_level(self):
        """重置碰撞等级为默认值"""
        self.current_collision_level = 60
        self.update_collision_slider_from_button()

    def apply_collision_level_setting(self):
        """应用碰撞等级设置到机器人 - 使用统一指令管理器"""
        if gv.is_8083_connected and gv.is_8080_connected:
            try:
                # 使用统一指令管理器发送碰撞等级设置
                result = command_manager.send_collision_command(self.current_collision_level)
                if result:
                    pass  # 成功，不打印调试信息
                else:
                    pass  # 失败，不打印调试信息

            except Exception as e:
                pass  # 不显示错误信息

    def setup_speed_slider_style(self):
        """设置速度滑动条样式"""
        if hasattr(self, 'pushSlider_2'):
            # 设置工具提示
            self.pushSlider_2.setToolTip(f"当前速度: {self.current_speed}%")

    def update_speed_display_and_send(self, value):
        """滑动条变化时更新速度显示并发送命令"""
        self.current_speed = value
        if hasattr(self, 'Speedvaluelabel_2'):
            self.Speedvaluelabel_2.setText(f"{value}%")
        if hasattr(self, 'pushSlider_2'):
            self.pushSlider_2.setToolTip(f"当前速度: {value}%")
        self.save_speed_to_database()
        self.apply_speed_setting()

    def update_collision_display_and_send(self, value):
        """滑动条变化时更新碰撞等级显示并发送命令"""
        self.current_collision_level = value
        if hasattr(self, 'collilabellabel_2'):
            self.collilabellabel_2.setText(f"{value}%")
        if hasattr(self, 'pushSlider'):
            self.pushSlider.setToolTip(f"当前碰撞等级: {value}%")
        self.save_collision_level_to_database()
        self.apply_collision_level_setting()

    def update_speed_slider_from_button(self):
        """按钮点击后更新滑动条和显示"""
        if hasattr(self, 'Speedvaluelabel_2'):
            self.Speedvaluelabel_2.setText(f"{self.current_speed}%")
        if hasattr(self, 'pushSlider_2'):
            if 1 <= self.current_speed <= 100:  # 确保值在范围内
                self.pushSlider_2.blockSignals(True)
                self.pushSlider_2.setValue(self.current_speed)
                self.pushSlider_2.setToolTip(f"当前速度: {self.current_speed}%")
                self.pushSlider_2.blockSignals(False)
        self.save_speed_to_database()
        self.apply_speed_setting()

    def update_collision_slider_from_button(self):
        """按钮点击后更新滑动条和显示"""
        if hasattr(self, 'collilabellabel_2'):
            self.collilabellabel_2.setText(f"{self.current_collision_level}%")
        if hasattr(self, 'pushSlider'):
            if 5 <= self.current_collision_level <= 100:  # 确保值在范围内
                self.pushSlider.blockSignals(True)
                self.pushSlider.setValue(self.current_collision_level)
                self.pushSlider.setToolTip(f"当前碰撞等级: {self.current_collision_level}%")
                self.pushSlider.blockSignals(False)
        self.save_collision_level_to_database()
        self.apply_collision_level_setting()

    def update_speed_display(self):
        """初始化时更新速度显示"""
        if hasattr(self, 'Speedvaluelabel_2'):
            self.Speedvaluelabel_2.setText(f"{self.current_speed}%")
        if hasattr(self, 'pushSlider_2'):
            self.pushSlider_2.blockSignals(True)
            self.pushSlider_2.setValue(self.current_speed)
            self.pushSlider_2.setToolTip(f"当前速度: {self.current_speed}%")
            self.pushSlider_2.blockSignals(False)

    def update_collision_level_display(self):
        """初始化时更新碰撞等级显示"""
        if hasattr(self, 'collilabellabel_2'):
            self.collilabellabel_2.setText(f"{self.current_collision_level}%")
        if hasattr(self, 'pushSlider'):
            self.pushSlider.blockSignals(True)
            self.pushSlider.setValue(self.current_collision_level)
            self.pushSlider.setToolTip(f"当前碰撞等级: {self.current_collision_level}%")
            self.pushSlider.blockSignals(False)

    def closeEvent(self, event):
        # 调用关闭数据库的方法
        db = SqliteTool.get_instance()  # 直接获取已存在的单例实例
        db.close()
        # 停止网络线程（优化版）
        if hasattr(self, 'network_thread'):
            # 先断开所有信号连接
            self.network_thread.started.disconnect()
            self.robot_controller.connected.disconnect()
            self.robot_controller.disconnected.disconnect()
            self.robot_controller.response_received.disconnect()

            # 安全停止线程
            if self.network_thread.isRunning():
                self.network_thread.quit()
                self.network_thread.wait(1000)  # 等待1秒

            # 删除C++对象
            self.network_thread.deleteLater()
            del self.network_thread
            # 停止8083监听
            self.robot_8083_socket.close()

        event.accept()

