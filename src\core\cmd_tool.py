# cmd_tool.py
from src.common import global_vars as gv
from functools import partial

"""
    此工具类主要是根据命令和参数生成对应的机器人指令，如有其他指令的需求可以在 CMD_MAP中维护命令和ID的映射
    并在CMD_GENERATOR 中添加参数生成函数的映射关系
"""

WeaveSetPara_ = 0
Acc_=100#加速度百分比
Ovl_=100#速度缩放因子e
Version_Number__=''

#TODO  check_all_zero_movec和check_all_zero 被我放到此文件里面，后续可能需要修改
def check_all_zero():
# return OFFSET_COMPENSATIONX_
    if all(value == 0 for value in [
        int(gv.OFFSET_COMPENSATIONX_),
        int(gv.OFFSET_COMPENSATIONY_),
        int(gv.OFFSET_COMPENSATIONZ_),
        int(gv.OFFSET_COMPENSATIONRX_),
        int(gv.OFFSET_COMPENSATIONRY_),
        int(gv.OFFSET_COMPENSATIONRZ_)
    ])==True:
        return 0
    else:
        return 2
def check_all_zero_movec():
    # return OFFSET_COMPENSATIONX_
    if all(value == 0 for value in [
        int(gv.OFFSET_COMPENSATIONX_),
        int(gv.OFFSET_COMPENSATIONY_),
        int(gv.OFFSET_COMPENSATIONZ_),
        int(gv.OFFSET_COMPENSATIONRX_),
        int(gv.OFFSET_COMPENSATIONRY_),
        int(gv.OFFSET_COMPENSATIONRZ_)
    ])==True:
        return 0
    else:
        return 1

class RobotCommand:

    @classmethod
    def Version_change(self, Version_Number):
        global Version_Number__
        Version_Number__=Version_Number
        global Acc_
        global Ovl_
        if Version_Number == "3.8":
            # WeaveSetPara_ = 252
            Acc_ = 100
            Ovl_ = 100
        else:
            # WeaveSetPara_ = 252
            Acc_ = 0
            Ovl_ = 0

    # 命令 ID 映射表
    CMD_MAP = {
        #TODO 码垛需要更换执行器指令

        #TODO 原始通用运动指令
        'SetAnticollision': 305,  
        'SetLoadWeight': 306,
        'StartJOG': 232,
        'StopJOG': 233,
        'PAUSE': 103,
        'STOP': 102,
        'RESUME': 104,
        'RESETALLERROR': 107,
        'Mode': 303,
        'GetActualJointPosDegree': 375,
        'GetActualTCPPose': 1152,
        'MoveJ': 201,
        'MoveL': 203,
        'MoveC': 202,
        'SetSpeed': 206,
        'WaitMs' : 207, # 延迟时间
        'GetInverseKin': 377, #逆运动学
        'GetSoftwareVersion':905,#获取软件版本号
        'AccSmoothEnd':1147,#关闭加速度平滑

        'SetDO':204,
        'GetDI':212
    }
    # 参数生成函数映射
    CMD_GENERATOR = {
        'SetSpeed': lambda p: f"SetSpeed({p.get('speed', 0)})",
        'SetAnticollision': lambda p: (
            f"SetAnticollision({p.get('type', 0)},{{{p.get('J1', 0)},{p.get('J2', 0)},{p.get('J3', 0)},"
            f"{p.get('J4', 0)},{p.get('J5', 0)},{p.get('J6', 0)}}},1)"
        ),
        'SetLoadWeight': lambda p: f"SetLoadWeight(0,{p.get('force', 0)})",
        'StartJOG': lambda p: (
            f"StartJOG({p.get('coordSystem', 0)},{p.get('jointNum', 0)},{p.get('direction', 0)},"
            f"{p.get('speed', 0)},{p.get('acceleration', 0)},{p.get('max_distance', 0)})"
        ),
        'StopJOG': lambda _: "StopJOG()",
        'PAUSE': lambda _: "PAUSE()",
        'STOP': lambda _: "STOP()",
        'GetSoftwareVersion': lambda _: "GetSoftwareVersion()",
        'AccSmoothEnd': lambda _: "AccSmoothEnd()",
        'RESUME': lambda _: "RESUME()",
        'RESETALLERROR': lambda _: "RESETALLERROR()",
        'Mode': lambda p: f"Mode({p.get('mode', 0)})",
        'GetActualJointPosDegree': lambda _: "GetActualJointPosDegree()",
        'GetActualTCPPose': lambda _: "GetActualTCPPose()",
        # 添加SetDO的生成器
        'SetDO': lambda p: f"SetDO({p.get('port_number', 0)},{p.get('output_value', 0)},{p.get('param3', 0)},{p.get('param4', 0)})",
        'GetDI': lambda p: f"GetDI({p.get('port_number', 0)})",
        'MoveJ': lambda p: (
            f"MoveJ({p.get('J1', 0):.3f},{p.get('J2', 0):.3f},{p.get('J3', 0):.3f},"
            f"{p.get('J4', 0):.3f},{p.get('J5', 0):.3f},{p.get('J6', 0):.3f},{p.get('x', 0):.3f},{p.get('y', 0):.3f},{p.get('z', 0):.3f},"
            f"{p.get('rx', 0):.3f},{p.get('ry', 0):.3f},{p.get('rz', 0):.3f},{p.get('toolNum', 0)},{p.get('workPieceNum', 0)},"
            f"{p.get('speed', 0)},{p.get('acc', Acc_)},{p.get('ovl', Ovl_)},{p.get('exaxisPos1', 0)},{p.get('exaxisPos2', 0)},"
            f"{p.get('exaxisPos3', 0)},{p.get('exaxisPos4', 0)},{p.get('blendT', -1)},{check_all_zero()},"
            f"{gv.OFFSET_COMPENSATIONX_},{gv.OFFSET_COMPENSATIONY_},{gv.OFFSET_COMPENSATIONZ_},"
            f"{gv.OFFSET_COMPENSATIONRX_},{gv.OFFSET_COMPENSATIONRY_},{gv.OFFSET_COMPENSATIONRZ_})"
        ),
        'MoveL': lambda p: (
            f"MoveL({p.get('J1', 0):.3f},{p.get('J2', 0):.3f},{p.get('J3', 0):.3f},"
            f"{p.get('J4', 0):.3f},{p.get('J5', 0):.3f},{p.get('J6', 0):.3f},{p.get('x', 0):.3f},{p.get('y', 0):.3f},{p.get('z', 0):.3f},"
            f"{p.get('rx', 0):.3f},{p.get('ry', 0):.3f},{p.get('rz', 0):.3f},{p.get('toolNum', 0)},{p.get('workPieceNum', 0)},"
            f"{p.get('speed', 0)},{p.get('acc', Acc_)},{p.get('ovl', Ovl_)},{p.get('blendR', -1)},{p.get('exaxisPos1', 0)},"
            f"{p.get('exaxisPos2', 0)},{p.get('exaxisPos3', 0)},{p.get('exaxisPos4', 0)},{p.get('search_flag', 0)},"
            f"{check_all_zero()},"
            f"{gv.OFFSET_COMPENSATIONX_},{gv.OFFSET_COMPENSATIONY_},{gv.OFFSET_COMPENSATIONZ_},"
            f"{gv.OFFSET_COMPENSATIONRX_},{gv.OFFSET_COMPENSATIONRY_},{gv.OFFSET_COMPENSATIONRZ_})"
        ),
        'MoveC': lambda p: (
            f"MoveC({p.get('J1', 0)},{p.get('J2', 0)},{p.get('J3', 0)},"
            f"{p.get('J4', 0)},{p.get('J5', 0)},{p.get('J6', 0)},{p.get('x', 0)},{p.get('y', 0)},{p.get('z', 0)},"
            f"{p.get('rx', 0)},{p.get('ry', 0)},{p.get('rz', 0)},{p.get('toolNum', 0)},{p.get('workPieceNum', 0)},"
            f"{p.get('speed', 0)},{p.get('acc', Acc_)},{p.get('exaxisPos1', 0)},{p.get('exaxisPos2', 0)},"
            f"{p.get('exaxisPos3', 0)},{p.get('exaxisPos4', 0)},"
            f"{check_all_zero_movec()},"
            f"{gv.OFFSET_COMPENSATIONX_},{gv.OFFSET_COMPENSATIONY_},{gv.OFFSET_COMPENSATIONZ_},"
            f"{gv.OFFSET_COMPENSATIONRX_},{gv.OFFSET_COMPENSATIONRY_},{gv.OFFSET_COMPENSATIONRZ_})"
            f"{p.get('J1_2', 0)},{p.get('J2_2', 0)},{p.get('J3_2', 0)},"
            f"{p.get('J4_2', 0)},{p.get('J5_2', 0)},{p.get('J6_2', 0)},{p.get('x_2', 0)},{p.get('y_2', 0)},{p.get('z_2', 0)},"
            f"{p.get('rx_2', 0)},{p.get('ry_2', 0)},{p.get('rz_2', 0)},{p.get('toolNum_2', 0)},{p.get('workPieceNum_2', 0)},"
            f"{p.get('speed_2', 0)},{p.get('acc_2', Acc_)},{p.get('exaxisPos1_2', 0)},{p.get('exaxisPos2_2', 0)},"
            f"{p.get('exaxisPos3_2', 0)},{p.get('exaxisPos4_2', 0)},"            
            f"{check_all_zero_movec()},"
            f"{gv.OFFSET_COMPENSATIONX_},{gv.OFFSET_COMPENSATIONY_},{gv.OFFSET_COMPENSATIONZ_},"
            f"{gv.OFFSET_COMPENSATIONRX_},{gv.OFFSET_COMPENSATIONRY_},{gv.OFFSET_COMPENSATIONRZ_})"
            f"{p.get('ovl', Ovl_)},{p.get('blendR', -1)})"
        ),
        'WaitMs': lambda p: (
            f"WaitMs({int(float(p.get('delay', 0)) * 1000)})"
            if p.get('delay') and float(p.get('delay', 0)) > 0
            else ""
        ),
        'GetInverseKin': lambda p: (
            f"GetInverseKin({p.get('flag',0)},{p.get('x',0):.3f},{p.get('y',0):.3f},{p.get('z',0):.3f},"
            f"{p.get('a',0):.3f},{p.get('b',0):.3f},{p.get('c',0):.3f},{p.get('config',0)})"
        ),
    }

    @classmethod
    def get_command_str(cls, cmd, params=None,request_id=None):
        params = params or {}
        if cmd not in cls.CMD_MAP:
            raise ValueError(f"未定义的命令: {cmd}")
        if cmd not in cls.CMD_GENERATOR:
            raise ValueError(f"未实现该命令的构造器: {cmd}")

        cmd_id = cls.CMD_MAP[cmd]
        data_func = cls.CMD_GENERATOR.get(cmd)
        data = data_func(params)
        if request_id is None:
            request_id = cmd_id
        if data == "":
            return ''
        return f"/f/bIII{request_id}III{cmd_id}III{len(data)}III{data}III/b/f".replace(" ", "")


