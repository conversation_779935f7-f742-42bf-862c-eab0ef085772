from PySide6.QtCore import Signal, QTimer
from PySide6.QtWidgets import QWidget
from ..ui.Ui_io import Ui_IOWidget
from ..common.sql_lite_tool import SqliteTool
from ..common import global_vars as gv
from ..core.palletizer_program import command_manager
from ..core.robot_tcp_controller import RobotTcpController
from ..core.cmd_tool import RobotCommand
from ..ui.custom_dialogs import CustomDialog, StatusChangeDialog, CustomStartDialog, ResumeWorkDialog
from datetime import datetime


class IOLogic(QWidget, Ui_IOWidget):
    # 定义信号
    io_status_updated = Signal(dict)  # IO状态更新信号

    def __init__(self):
        super().__init__()
        self.setupUi(self)

        self.db_tool = SqliteTool.get_instance()
        self.robot_controller = RobotTcpController.get_instance()  # 机器人控制器

        # IO功能名称定义
        self.di_function_names = [
            '启动', '暂停', '停止', '抓取信号', '气压检测',
            '真空检测', '左托盘检测', '右托盘检测'
        ]

        self.do_function_names = [
            '启动灯', '暂停灯', '停止灯', '抓取', '释放',
            '左垛运行中', '左托盘满垛', '右垛运行中', '右托盘满垛'
        ]

        # IO功能映射缓存
        self.function_to_port_mapping = {}  # 功能名 -> (io_type, port_number, active_level)
        self.port_to_function_mapping = {}  # (io_type, port_number) -> 功能名

        # 当前IO状态缓存
        self.current_di_status = {f'DI{i}': 0 for i in range(16)}
        self.current_do_status = {f'DO{i}': 0 for i in range(16)}

        # 初始化
        self.setup_ui_connections()

        # 从数据库加载IO映射关系
        self.load_io_mappings()

        # 定时更新IO状态显示
        self.status_update_timer = QTimer()
        self.status_update_timer.timeout.connect(self.update_status_indicators)
        self.status_update_timer.start(100)  # 100ms更新一次

        # 获取8083socket实例用于IO状态获取
        self.robot_8083_socket = None

    def setup_ui_connections(self):
        """设置UI连接"""
        # 连接DI保存按钮
        if hasattr(self, 'diSaveButton'):
            self.diSaveButton.clicked.connect(self.save_di_mappings_to_database)

        # 连接DO保存按钮
        if hasattr(self, 'doSaveButton'):
            self.doSaveButton.clicked.connect(self.save_do_mappings_to_database)

        # 连接下拉框变化事件
        di_comboboxes = [
            self.diStartComboBox, self.diPauseComboBox, self.diStopComboBox,
            self.diGrabSignalComboBox, self.diPressureDetectComboBox,
            self.diVacuumDetectComboBox, self.diLeftTrayDetectComboBox,
            self.diRightTrayDetectComboBox
        ]

        do_comboboxes = [
            self.doStartLightComboBox, self.doPauseLightComboBox, self.doStopLightComboBox,
            self.doGrabComboBox, self.doReleaseComboBox, self.doLeftRunningComboBox,
            self.doLeftTrayCleanComboBox, self.doRightRunningComboBox, self.doRightTrayCleanComboBox
        ]

        for combo in di_comboboxes + do_comboboxes:
            combo.currentTextChanged.connect(self.on_mapping_changed)

    def update_status_indicators(self):
        """更新状态指示器"""
        try:
            # DI状态指示器映射
            di_indicator_mapping = {
                self.diStartComboBox: self.diStartStatusIndicator,
                self.diPauseComboBox: self.diPauseStatusIndicator,
                self.diStopComboBox: self.diStopStatusIndicator,
                self.diGrabSignalComboBox: self.diGrabSignalStatusIndicator,
                self.diPressureDetectComboBox: self.diPressureDetectStatusIndicator,
                self.diVacuumDetectComboBox: self.diVacuumDetectStatusIndicator,
                self.diLeftTrayDetectComboBox: self.diLeftTrayDetectStatusIndicator,
                self.diRightTrayDetectComboBox: self.diRightTrayDetectStatusIndicator
            }

            # DO状态指示器映射
            do_indicator_mapping = {
                self.doStartLightComboBox: self.doStartLightStatusIndicator,
                self.doPauseLightComboBox: self.doPauseLightStatusIndicator,
                self.doStopLightComboBox: self.doStopLightStatusIndicator,
                self.doGrabComboBox: self.doGrabStatusIndicator,
                self.doReleaseComboBox: self.doReleaseStatusIndicator,
                self.doLeftRunningComboBox: self.doLeftRunningStatusIndicator,
                self.doLeftTrayCleanComboBox: self.doLeftTrayCleanStatusIndicator,
                self.doRightRunningComboBox: self.doRightRunningStatusIndicator,
                self.doRightTrayCleanComboBox: self.doRightTrayCleanStatusIndicator
            }

            # 更新DI状态指示器
            for combo, indicator in di_indicator_mapping.items():
                if hasattr(self, indicator.objectName()):
                    selected_text = combo.currentText()
                    if selected_text == "空":
                        # 选择为空时设置为灰色圆形
                        indicator.setStyleSheet(
                            "background-color: gray; "
                            "border-radius: 10px; "
                            "width: 20px; "
                            "height: 20px; "
                            "border: 1px solid #666; "
                            "min-width: 20px; "
                            "min-height: 20px; "
                            "max-width: 20px; "
                            "max-height: 20px;"
                        )
                    else:
                        # 检查对应DI端口的状态 - 使用新的get_di_status方法
                        port_number = int(selected_text)
                        di_value = self.get_di_status(port_number)
                        if di_value == 1:  # DI开启
                            indicator.setStyleSheet(
                                "background-color: green; "
                                "border-radius: 10px; "
                                "width: 20px; "
                                "height: 20px; "
                                "border: 1px solid #4CAF50; "
                                "min-width: 20px; "
                                "min-height: 20px; "
                                "max-width: 20px; "
                                "max-height: 20px;"
                            )
                        else:  # DI关闭
                            indicator.setStyleSheet(
                                "background-color: gray; "
                                "border-radius: 10px; "
                                "width: 20px; "
                                "height: 20px; "
                                "border: 1px solid #666; "
                                "min-width: 20px; "
                                "min-height: 20px; "
                                "max-width: 20px; "
                                "max-height: 20px;"
                            )

            # DO状态指示器 - 修改为使用实际DO状态
            for combo, indicator in do_indicator_mapping.items():
                if hasattr(self, indicator.objectName()):
                    selected_text = combo.currentText()
                    if selected_text == "空":
                        indicator.setStyleSheet(
                            "background-color: gray; "
                            "border-radius: 10px; "
                            "width: 20px; "
                            "height: 20px; "
                            "border: 1px solid #666; "
                            "min-width: 20px; "
                            "min-height: 20px; "
                            "max-width: 20px; "
                            "max-height: 20px;"
                        )
                    else:
                        # 检查对应DO端口的状态 - 使用get_do_status方法
                        port_number = int(selected_text)
                        do_value = self.get_do_status(port_number)
                        if do_value == 1:  # DO开启
                            indicator.setStyleSheet(
                                "background-color: green; "
                                "border-radius: 10px; "
                                "width: 20px; "
                                "height: 20px; "
                                "border: 1px solid #4CAF50; "
                                "min-width: 20px; "
                                "min-height: 20px; "
                                "max-width: 20px; "
                                "max-height: 20px;"
                            )
                        else:  # DO关闭
                            indicator.setStyleSheet(
                                "background-color: gray; "
                                "border-radius: 10px; "
                                "width: 20px; "
                                "height: 20px; "
                                "border: 1px solid #666; "
                                "min-width: 20px; "
                                "min-height: 20px; "
                                "max-width: 20px; "
                                "max-height: 20px;"
                            )

        except Exception as e:
            print(f"更新状态指示器失败: {e}")

    def save_di_mappings_to_database(self):
        """保存DI映射关系到数据库"""
        try:
            # 清空现有DI映射
            self.db_tool.execute_sql("DELETE FROM io_port_mapping WHERE io_type = 'DI'")

            # 保存DI映射
            di_comboboxes = [
                self.diStartComboBox, self.diPauseComboBox, self.diStopComboBox,
                self.diGrabSignalComboBox, self.diPressureDetectComboBox,
                self.diVacuumDetectComboBox, self.diLeftTrayDetectComboBox,
                self.diRightTrayDetectComboBox
            ]

            for i, combo in enumerate(di_comboboxes):
                function_name = self.di_function_names[i]
                selected_text = combo.currentText()

                if selected_text == "空":
                    port_number = -1
                else:
                    port_number = int(selected_text)

                self.db_tool.insert("io_port_mapping", {
                    "function_name": function_name,
                    "io_type": "DI",
                    "port_number": port_number,
                    "active_level": "low",
                    "updated_at": datetime.now().isoformat()
                })

            print("DI映射关系已保存到数据库")
            self.load_io_mappings()  # 重新加载映射
            CustomDialog.show_success(
                self,
                "保存成功",
                "DI配置已保存"
            )

        except Exception as e:
            print(f"保存DI映射关系失败: {e}")
            CustomDialog.show_error(
                self,
                "保存失败",
                f"DI保存失败：{str(e)}"
            )

    def save_do_mappings_to_database(self):
        """保存DO映射关系到数据库"""
        try:
            # 清空现有DO映射
            self.db_tool.execute_sql("DELETE FROM io_port_mapping WHERE io_type = 'DO'")

            # 保存DO映射
            do_comboboxes = [
                self.doStartLightComboBox, self.doPauseLightComboBox, self.doStopLightComboBox,
                self.doGrabComboBox, self.doReleaseComboBox, self.doLeftRunningComboBox,
                self.doLeftTrayCleanComboBox, self.doRightRunningComboBox, self.doRightTrayCleanComboBox
            ]

            for i, combo in enumerate(do_comboboxes):
                function_name = self.do_function_names[i]
                selected_text = combo.currentText()

                if selected_text == "空":
                    port_number = -1
                else:
                    port_number = int(selected_text)

                self.db_tool.insert("io_port_mapping", {
                    "function_name": function_name,
                    "io_type": "DO",
                    "port_number": port_number,
                    "active_level": "high",
                    "updated_at": datetime.now().isoformat()
                })

            print("DO映射关系已保存到数据库")
            self.load_io_mappings()  # 重新加载映射
            CustomDialog.show_success(
                self,
                "保存成功",
                "DI配置已保存"
            )

        except Exception as e:
            print(f"保存DO映射关系失败: {e}")
            CustomDialog.show_error(
                self,
                "保存失败",
                f"DI保存失败：{str(e)}"
            )

    def on_mapping_changed(self):
        """映射变化时的处理"""
        # 立即更新状态指示器
        self.update_status_indicators()

    def set_robot_socket(self, robot_socket):
        """设置机器人socket实例"""
        self.robot_8083_socket = robot_socket
        if robot_socket:
            robot_socket.data_signal.connect(self.update_io_data_from_robot)

    def update_io_data_from_robot(self, data):
        """从机器人数据更新IO状态 - 同步全局状态到本地缓存"""
        try:
            # 同步全局状态到本地缓存
            if hasattr(gv, 'IO_STATUS'):
                # 同步DI状态
                for i in range(16):
                    di_key = f'DI{i}'
                    if di_key in gv.IO_STATUS['DI']:
                        self.current_di_status[di_key] = gv.IO_STATUS['DI'][di_key]

                # 同步DO状态
                for i in range(16):
                    do_key = f'DO{i}'
                    if do_key in gv.IO_STATUS['DO']:
                        self.current_do_status[do_key] = gv.IO_STATUS['DO'][do_key]

        except Exception as e:
            print(f"同步IO数据失败: {e}")

    def get_di_status(self, di_number):
        """获取指定DI状态 - 优先使用全局状态"""
        try:
            if 0 <= di_number <= 15:
                # 优先从全局状态获取
                if hasattr(gv, 'IO_STATUS'):
                    return gv.IO_STATUS['DI'].get(f'DI{di_number}', 0)
                # 备用：从本地缓存获取
                return self.current_di_status.get(f'DI{di_number}', 0)
            return 0
        except:
            return 0

    def get_do_status(self, do_number):
        """获取指定DO状态 - 优先使用全局状态"""
        try:
            if 0 <= do_number <= 15:
                # 优先从全局状态获取
                if hasattr(gv, 'IO_STATUS'):
                    return gv.IO_STATUS['DO'].get(f'DO{do_number}', 0)
                # 备用：从本地缓存获取
                return self.current_do_status.get(f'DO{do_number}', 0)
            return 0
        except:
            return 0

    def get_function_status(self, function_name):
        """获取功能状态 - 使用全局状态"""
        if function_name not in self.function_to_port_mapping:
            return False

        io_type, port_number, active_level = self.function_to_port_mapping[function_name]

        if io_type == 'DI' and port_number >= 0:  # 排除空状态
            # 使用新的get_di_status方法（会自动使用全局状态）
            current_value = self.get_di_status(port_number)
            # 根据有效电平判断
            if active_level == 'low':
                return current_value == 0
            else:
                return current_value == 1

        return False

    def update_status_indicators(self):
        """更新状态指示器 - 使用全局IO状态"""
        try:
            # DI状态指示器映射
            di_indicator_mapping = {
                self.diStartComboBox: self.diStartStatusIndicator,
                self.diPauseComboBox: self.diPauseStatusIndicator,
                self.diStopComboBox: self.diStopStatusIndicator,
                self.diGrabSignalComboBox: self.diGrabSignalStatusIndicator,
                self.diPressureDetectComboBox: self.diPressureDetectStatusIndicator,
                self.diVacuumDetectComboBox: self.diVacuumDetectStatusIndicator,
                self.diLeftTrayDetectComboBox: self.diLeftTrayDetectStatusIndicator,
                self.diRightTrayDetectComboBox: self.diRightTrayDetectStatusIndicator
            }

            # DO状态指示器映射
            do_indicator_mapping = {
                self.doStartLightComboBox: self.doStartLightStatusIndicator,
                self.doPauseLightComboBox: self.doPauseLightStatusIndicator,
                self.doStopLightComboBox: self.doStopLightStatusIndicator,
                self.doGrabComboBox: self.doGrabStatusIndicator,
                self.doReleaseComboBox: self.doReleaseStatusIndicator,
                self.doLeftRunningComboBox: self.doLeftRunningStatusIndicator,
                self.doLeftTrayCleanComboBox: self.doLeftTrayCleanStatusIndicator,
                self.doRightRunningComboBox: self.doRightRunningStatusIndicator,
                self.doRightTrayCleanComboBox: self.doRightTrayCleanStatusIndicator
            }

            # 更新DI状态指示器
            for combo, indicator in di_indicator_mapping.items():
                if hasattr(self, indicator.objectName()):
                    selected_text = combo.currentText()
                    if selected_text == "空":
                        # 选择为空时设置为灰色圆形
                        indicator.setStyleSheet(
                            "background-color: gray; "
                            "border-radius: 10px; "
                            "width: 20px; "
                            "height: 20px; "
                            "border: 1px solid #666; "
                            "min-width: 20px; "
                            "min-height: 20px; "
                            "max-width: 20px; "
                            "max-height: 20px;"
                        )
                    else:
                        # 检查对应DI端口的状态 - 使用新的get_di_status方法
                        port_number = int(selected_text)
                        di_value = self.get_di_status(port_number)
                        if di_value == 1:  # DI开启
                            indicator.setStyleSheet(
                                "background-color: green; "
                                "border-radius: 10px; "
                                "width: 20px; "
                                "height: 20px; "
                                "border: 1px solid #4CAF50; "
                                "min-width: 20px; "
                                "min-height: 20px; "
                                "max-width: 20px; "
                                "max-height: 20px;"
                            )
                        else:  # DI关闭
                            indicator.setStyleSheet(
                                "background-color: gray; "
                                "border-radius: 10px; "
                                "width: 20px; "
                                "height: 20px; "
                                "border: 1px solid #666; "
                                "min-width: 20px; "
                                "min-height: 20px; "
                                "max-width: 20px; "
                                "max-height: 20px;"
                            )

            # DO状态指示器 - 修改为使用实际DO状态
            for combo, indicator in do_indicator_mapping.items():
                if hasattr(self, indicator.objectName()):
                    selected_text = combo.currentText()
                    if selected_text == "空":
                        indicator.setStyleSheet(
                            "background-color: gray; "
                            "border-radius: 10px; "
                            "width: 20px; "
                            "height: 20px; "
                            "border: 1px solid #666; "
                            "min-width: 20px; "
                            "min-height: 20px; "
                            "max-width: 20px; "
                            "max-height: 20px;"
                        )
                    else:
                        # 检查对应DO端口的状态 - 使用get_do_status方法
                        port_number = int(selected_text)
                        do_value = self.get_do_status(port_number)
                        if do_value == 1:  # DO开启
                            indicator.setStyleSheet(
                                "background-color: green; "
                                "border-radius: 10px; "
                                "width: 20px; "
                                "height: 20px; "
                                "border: 1px solid #4CAF50; "
                                "min-width: 20px; "
                                "min-height: 20px; "
                                "max-width: 20px; "
                                "max-height: 20px;"
                            )
                        else:  # DO关闭
                            indicator.setStyleSheet(
                                "background-color: gray; "
                                "border-radius: 10px; "
                                "width: 20px; "
                                "height: 20px; "
                                "border: 1px solid #666; "
                                "min-width: 20px; "
                                "min-height: 20px; "
                                "max-width: 20px; "
                                "max-height: 20px;"
                            )

        except Exception as e:
            print(f"更新状态指示器失败: {e}")

    def load_io_mappings(self):
        """从数据库加载IO映射关系"""
        try:
            # 使用正确的查询方法
            mappings = self.db_tool.select(
                "io_port_mapping",
                fields=["function_name", "io_type", "port_number", "active_level"]
            )

            if mappings:
                # 清空现有映射
                self.function_to_port_mapping.clear()
                self.port_to_function_mapping.clear()

                # 重新构建映射关系
                for mapping in mappings:
                    function_name = mapping["function_name"]
                    io_type = mapping["io_type"]
                    port_number = mapping["port_number"]
                    active_level = mapping["active_level"]

                    # 功能名称到端口的映射
                    self.function_to_port_mapping[function_name] = {
                        "io_type": io_type,
                        "port_number": port_number,
                        "active_level": active_level
                    }

                    # 端口到功能名称的映射
                    port_key = f"{io_type}{port_number}"
                    self.port_to_function_mapping[port_key] = function_name

                # 新增：将映射关系应用到UI界面
                self.apply_mappings_to_ui()

                print(f"已加载 {len(mappings)} 个IO映射关系")
            else:
                print("未找到IO映射关系")

        except Exception as e:
            print(f"加载IO映射关系失败: {e}")

    def apply_mappings_to_ui(self):
        """将映射关系应用到UI界面"""
        try:
            # DI下拉框列表
            di_comboboxes = [
                self.diStartComboBox, self.diPauseComboBox, self.diStopComboBox,
                self.diGrabSignalComboBox, self.diPressureDetectComboBox,
                self.diVacuumDetectComboBox, self.diLeftTrayDetectComboBox,
                self.diRightTrayDetectComboBox
            ]

            # 设置DI下拉框的选中项
            for i, combo in enumerate(di_comboboxes):
                function_name = self.di_function_names[i]
                if function_name in self.function_to_port_mapping:
                    mapping = self.function_to_port_mapping[function_name]
                    port_number = mapping["port_number"]

                    if port_number == -1:
                        combo.setCurrentText("空")
                    else:
                        combo.setCurrentText(str(port_number))
                else:
                    combo.setCurrentText("空")

            # DO下拉框列表
            do_comboboxes = [
                self.doStartLightComboBox, self.doPauseLightComboBox, self.doStopLightComboBox,
                self.doGrabComboBox, self.doReleaseComboBox, self.doLeftRunningComboBox,
                self.doLeftTrayCleanComboBox, self.doRightRunningComboBox, self.doRightTrayCleanComboBox
            ]

            # 设置DO下拉框的选中项
            for i, combo in enumerate(do_comboboxes):
                function_name = self.do_function_names[i]
                if function_name in self.function_to_port_mapping:
                    mapping = self.function_to_port_mapping[function_name]
                    port_number = mapping["port_number"]

                    if port_number == -1:
                        combo.setCurrentText("空")
                    else:
                        combo.setCurrentText(str(port_number))
                else:
                    combo.setCurrentText("空")

            print("映射关系已应用到UI界面")

        except Exception as e:
            print(f"应用映射关系到UI失败: {e}")

    def get_function_status(self, function_name):
        """获取功能状态"""
        if function_name not in self.function_to_port_mapping:
            return False

        io_type, port_number, active_level = self.function_to_port_mapping[function_name]

        if io_type == 'DI' and port_number >= 0:  # 排除空状态
            current_value = self.current_di_status.get(f'DI{port_number}', 0)
            # 根据有效电平判断
            if active_level == 'low':
                return current_value == 0
            else:
                return current_value == 1

        return False

    def set_function_output(self, function_name, state):
        """设置功能输出状态"""
        if function_name not in self.function_to_port_mapping:
            print(f"功能 {function_name} 未配置IO端口")
            return False

        # 修复：从字典中获取值而不是解包
        mapping_info = self.function_to_port_mapping[function_name]
        io_type = mapping_info['io_type']
        port_number = mapping_info['port_number']
        active_level = mapping_info['active_level']

        if io_type == 'DO' and port_number >= 0:  # 排除空状态
            # 根据有效电平计算实际输出值
            if active_level == 'low':  # 低电平有效
                output_value = 0 if state else 1  # state=True时输出0（低电平开启）
            else:  # 高电平有效
                output_value = 1 if state else 0  # state=True时输出1（高电平开启）

            params = {
                'port_number': port_number,
                'output_value': output_value,
                'param3': 0,
                'param4': 0
            }
            cmd = RobotCommand.get_command_str('SetDO', params, 204)
            result = command_manager.send_and_wait(cmd, command_type="DO设置")
            if result.get("success"):
                print(f"DO{port_number}设置成功: {output_value}")
            else:
                print(f"DO{port_number}设置失败: {result.get('error', '未知错误')}")

            # 更新本地状态
            self.current_do_status[f'DO{port_number}'] = output_value
            if hasattr(gv, 'IO_STATUS'):
                gv.IO_STATUS['DO'][f'DO{port_number}'] = output_value

            return True
        return False

    def get_di_status(self, di_number):
        """获取指定DI状态"""
        if 0 <= di_number <= 7:
            return self.current_di_status.get(f'DI{di_number}', 0)
        return 0

    def set_do_output(self, do_number, state):
        """设置指定DO输出"""
        if 0 <= do_number <= 7:
            output_value = 1 if state else 0
            params = {
                'port_number': do_number,
                'output_value': output_value,
                'param3': 0,
                'param4': 0
            }
            cmd = RobotCommand.get_command_str('SetDO', params, 204)

            success = command_manager.send_command(cmd, "DO直接设置")
            if success:
                self.current_do_status[f'DO{do_number}'] = output_value
                return True
            else:
                print(f"设置DO{do_number}失败")
                return False
        return False

    def activate_suction_cup(self):
        """启动吸盘"""
        try:
            result = self.set_function_output('抓取', True)
            if result:
                print("吸盘启动成功")
                return True
            else:
                print("吸盘启动失败")
                return False
        except Exception as e:
            print(f"启动吸盘异常: {e}")
            return False

    def deactivate_suction_cup(self):
        """关闭吸盘"""
        try:
            result = self.set_function_output('抓取', False)
            if result:
                print("吸盘关闭成功")
                return True
            else:
                print("吸盘关闭失败")
                return False
        except Exception as e:
            print(f"关闭吸盘异常: {e}")
            return False

    def check_suction_cup_vacuum(self):
        """检查吸盘真空状态"""
        try:
            vacuum_status = self.get_function_status('真空检测')
            print(f"真空检测状态: {vacuum_status}")
            return vacuum_status
        except Exception as e:
            print(f"检查真空状态异常: {e}")
            return False

    def check_pressure_status(self):
        """检查气压状态"""
        try:
            pressure_status = self.get_function_status('气压检测')
            print(f"气压检测状态: {pressure_status}")
            return pressure_status
        except Exception as e:
            print(f"检查气压状态异常: {e}")
            return False

    def activate_release(self):
        """启动释放"""
        try:
            result = self.set_function_output('释放', True)
            if result:
                print("释放启动成功")
                return True
            else:
                print("释放启动失败")
                return False
        except Exception as e:
            print(f"启动释放异常: {e}")
            return False

    def deactivate_release(self):
        """关闭释放"""
        try:
            result = self.set_function_output('释放', False)
            if result:
                print("释放关闭成功")
                return True
            else:
                print("释放关闭失败")
                return False
        except Exception as e:
            print(f"关闭释放异常: {e}")
            return False

    def check_grab_signal(self):
        """检查抓取信号"""
        try:
            grab_signal = self.get_function_status('抓取信号')
            print(f"抓取信号状态: {grab_signal}")
            return grab_signal
        except Exception as e:
            print(f"检查抓取信号异常: {e}")
            return False

    def check_left_tray_detection(self):
        """检查左托盘检测"""
        try:
            left_tray_status = self.get_function_status('左托盘检测')
            print(f"左托盘检测状态: {left_tray_status}")
            return left_tray_status
        except Exception as e:
            print(f"检查左托盘检测异常: {e}")
            return False

    def check_right_tray_detection(self):
        """检查右托盘检测"""
        try:
            right_tray_status = self.get_function_status('右托盘检测')
            print(f"右托盘检测状态: {right_tray_status}")
            return right_tray_status
        except Exception as e:
            print(f"检查右托盘检测异常: {e}")
            return False

    def set_start_light(self, state):
        """设置启动灯状态"""
        try:
            result = self.set_function_output('启动灯', state)
            if result:
                print(f"启动灯设置成功: {'开启' if state else '关闭'}")
                return True
            else:
                print(f"启动灯设置失败")
                return False
        except Exception as e:
            print(f"设置启动灯异常: {e}")
            return False

    def set_pause_light(self, state):
        """设置暂停灯状态"""
        try:
            result = self.set_function_output('暂停灯', state)
            if result:
                print(f"暂停灯设置成功: {'开启' if state else '关闭'}")
                return True
            else:
                print(f"暂停灯设置失败")
                return False
        except Exception as e:
            print(f"设置暂停灯异常: {e}")
            return False

    def set_stop_light(self, state):
        """设置停止灯状态"""
        try:
            result = self.set_function_output('停止灯', state)
            if result:
                print(f"停止灯设置成功: {'开启' if state else '关闭'}")
                return True
            else:
                print(f"停止灯设置失败")
                return False
        except Exception as e:
            print(f"设置停止灯异常: {e}")
            return False

    def set_left_running_indicator(self, state):
        """设置左垛运行中指示"""
        try:
            result = self.set_function_output('左垛运行中', state)
            if result:
                print(f"左垛运行中指示设置成功: {'开启' if state else '关闭'}")
                return True
            else:
                print(f"左垛运行中指示设置失败")
                return False
        except Exception as e:
            print(f"设置左垛运行中指示异常: {e}")
            return False

    def set_right_running_indicator(self, state):
        """设置右垛运行中指示"""
        try:
            result = self.set_function_output('右垛运行中', state)
            if result:
                print(f"右垛运行中指示设置成功: {'开启' if state else '关闭'}")
                return True
            else:
                print(f"右垛运行中指示设置失败")
                return False
        except Exception as e:
            print(f"设置右垛运行中指示异常: {e}")
            return False

    def set_left_tray_full_indicator(self, state):
        """设置左托盘满垛指示"""
        try:
            result = self.set_function_output('左托盘满垛', state)
            if result:
                print(f"左托盘满垛指示设置成功: {'开启' if state else '关闭'}")
                return True
            else:
                print(f"左托盘满垛指示设置失败")
                return False
        except Exception as e:
            print(f"设置左托盘满垛指示异常: {e}")
            return False

    def set_right_tray_full_indicator(self, state):
        """设置右托盘满垛指示"""
        try:
            result = self.set_function_output('右托盘满垛', state)
            if result:
                print(f"右托盘满垛指示设置成功: {'开启' if state else '关闭'}")
                return True
            else:
                print(f"右托盘满垛指示设置失败")
                return False
        except Exception as e:
            print(f"设置右托盘满垛指示异常: {e}")
            return False

    def check_start_signal(self):
        """检查启动信号"""
        try:
            start_signal = self.get_function_status('启动')
            print(f"启动信号状态: {start_signal}")
            return start_signal
        except Exception as e:
            print(f"检查启动信号异常: {e}")
            return False

    def check_pause_signal(self):
        """检查暂停信号"""
        try:
            pause_signal = self.get_function_status('暂停')
            print(f"暂停信号状态: {pause_signal}")
            return pause_signal
        except Exception as e:
            print(f"检查暂停信号异常: {e}")
            return False

    def check_stop_signal(self):
        """检查停止信号"""
        try:
            stop_signal = self.get_function_status('停止')
            print(f"停止信号状态: {stop_signal}")
            return stop_signal
        except Exception as e:
            print(f"检查停止信号异常: {e}")
            return False

    # ==================== 组合操作方法 ====================

    def execute_grab_operation(self):
        """执行完整的抓取操作"""
        try:
            # 启动吸盘
            if not self.activate_suction_cup():
                print("启动吸盘失败")
                return False

            # 等待吸盘建立真空（这里可以根据需要调整等待时间）
            import time
            time.sleep(0.5)

            # 检查吸盘状态
            if not self.check_suction_cup_vacuum():
                print("吸盘未建立真空")
                self.deactivate_suction_cup()
                return False

            print("抓取操作成功")
            return True

        except Exception as e:
            print(f"抓取操作异常: {e}")
            return False

    def execute_release_operation(self):
        """执行完整的释放操作"""
        try:
            # 关闭吸盘
            if not self.deactivate_suction_cup():
                print("关闭吸盘失败")
                return False

            # 启动释放
            if not self.activate_release():
                print("启动释放失败")
                return False

            # 等待释放完成
            import time
            time.sleep(0.3)

            # 关闭释放
            self.deactivate_release()

            print("释放操作成功")
            return True

        except Exception as e:
            print(f"释放操作异常: {e}")
            return False

    def reset_all_outputs(self):
        """重置所有输出"""
        try:
            # 关闭所有DO输出
            self.deactivate_suction_cup()
            self.deactivate_release()
            self.set_start_light(False)
            self.set_pause_light(False)
            self.set_stop_light(False)
            self.set_left_running_indicator(False)
            self.set_right_running_indicator(False)
            self.set_left_tray_full_indicator(False)
            self.set_right_tray_full_indicator(False)

            print("所有输出已重置")
            return True

        except Exception as e:
            print(f"重置输出异常: {e}")
            return False


# 全局IO操作函数
def get_function_status(function_name):
    """获取功能状态（全局函数）"""
    if hasattr(gv, 'io_logic_instance') and gv.io_logic_instance:
        return gv.io_logic_instance.get_function_status(function_name)
    return False


def set_function_output(function_name, state):
    """设置功能输出（全局函数）"""
    if hasattr(gv, 'io_logic_instance') and gv.io_logic_instance:
        return gv.io_logic_instance.set_function_output(function_name, state)
    return False


def get_di_status(di_number):
    """获取DI状态（全局函数）"""
    if hasattr(gv, 'io_logic_instance') and gv.io_logic_instance:
        return gv.io_logic_instance.get_di_status(di_number)
    return 0


def set_do_output(do_number, state):
    """设置DO输出（全局函数）"""
    if hasattr(gv, 'io_logic_instance') and gv.io_logic_instance:
        return gv.io_logic_instance.set_do_output(do_number, state)
    return False
