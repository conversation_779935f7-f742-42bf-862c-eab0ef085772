# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'formula.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, Q<PERSON>ursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QHBoxLayout, QHeaderView,
    QPushButton, QSizePolicy, QSpacerItem, QTableWidget,
    QTableWidgetItem, QWidget)
from ..resources.formula import formula_rc

class Ui_formula(object):
    def setupUi(self, formula):
        if not formula.objectName():
            formula.setObjectName(u"formula")
        formula.resize(857, 510)
        formula.setMinimumSize(QSize(840, 510))
        self.frame = QFrame(formula)
        self.frame.setObjectName(u"frame")
        self.frame.setGeometry(QRect(12, 40, 815, 64))
        self.frame.setMinimumSize(QSize(815, 64))
        self.frame.setMaximumSize(QSize(815, 64))
        self.frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame.setFrameShadow(QFrame.Shadow.Raised)
        self.layoutWidget = QWidget(self.frame)
        self.layoutWidget.setObjectName(u"layoutWidget")
        self.layoutWidget.setGeometry(QRect(0, 10, 383, 43))
        self.horizontalLayout_2 = QHBoxLayout(self.layoutWidget)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer = QSpacerItem(13, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer)

        self.input_btn = QPushButton(self.layoutWidget)
        self.input_btn.setObjectName(u"input_btn")
        self.input_btn.setMinimumSize(QSize(104, 40))
        self.input_btn.setMaximumSize(QSize(104, 40))
        font = QFont()
        font.setPointSize(12)
        self.input_btn.setFont(font)
        self.input_btn.setStyleSheet(u"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/formula/formula/input.png);\n"
"")

        self.horizontalLayout_2.addWidget(self.input_btn)

        self.horizontalSpacer_2 = QSpacerItem(13, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_2)

        self.export_btn = QPushButton(self.layoutWidget)
        self.export_btn.setObjectName(u"export_btn")
        self.export_btn.setMinimumSize(QSize(104, 40))
        self.export_btn.setMaximumSize(QSize(104, 40))
        self.export_btn.setFont(font)
        self.export_btn.setStyleSheet(u"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/formula/formula/output.png);")

        self.horizontalLayout_2.addWidget(self.export_btn)

        self.horizontalSpacer_3 = QSpacerItem(13, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_3)

        self.add_btn = QPushButton(self.layoutWidget)
        self.add_btn.setObjectName(u"add_btn")
        self.add_btn.setMinimumSize(QSize(104, 39))
        self.add_btn.setMaximumSize(QSize(104, 40))
        self.add_btn.setFont(font)
        self.add_btn.setStyleSheet(u"background-color: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"background-image: url(:/formula/formula/add.png);")

        self.horizontalLayout_2.addWidget(self.add_btn)

        self.frame_2 = QFrame(formula)
        self.frame_2.setObjectName(u"frame_2")
        self.frame_2.setGeometry(QRect(12, 110, 815, 397))
        self.frame_2.setMinimumSize(QSize(815, 397))
        self.frame_2.setMaximumSize(QSize(815, 397))
        self.frame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.formula_table = QTableWidget(self.frame_2)
        if (self.formula_table.columnCount() < 11):
            self.formula_table.setColumnCount(11)
        __qtablewidgetitem = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(4, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(5, __qtablewidgetitem5)
        __qtablewidgetitem6 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(6, __qtablewidgetitem6)
        __qtablewidgetitem7 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(7, __qtablewidgetitem7)
        __qtablewidgetitem8 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(8, __qtablewidgetitem8)
        __qtablewidgetitem9 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(9, __qtablewidgetitem9)
        __qtablewidgetitem10 = QTableWidgetItem()
        self.formula_table.setHorizontalHeaderItem(10, __qtablewidgetitem10)
        self.formula_table.setObjectName(u"formula_table")
        self.formula_table.setGeometry(QRect(10, 10, 791, 381))
        self.formula_table.setMinimumSize(QSize(785, 0))
        self.formula_table.setMaximumSize(QSize(100000, 100000))
        self.formula_table.setStyleSheet(u"")
        self.formula_table.setRowCount(0)
        self.formula_table.horizontalHeader().setMinimumSectionSize(50)
        self.formula_table.horizontalHeader().setDefaultSectionSize(80)
        self.formula_table.horizontalHeader().setProperty(u"showSortIndicator", False)
        self.formula_table.horizontalHeader().setStretchLastSection(False)
        self.formula_table.verticalHeader().setVisible(False)
        self.formula_table.verticalHeader().setDefaultSectionSize(50)

        self.retranslateUi(formula)

        QMetaObject.connectSlotsByName(formula)
    # setupUi

    def retranslateUi(self, formula):
        formula.setWindowTitle(QCoreApplication.translate("formula", u"Form", None))
        self.input_btn.setText("")
        self.export_btn.setText("")
        self.add_btn.setText("")
        ___qtablewidgetitem = self.formula_table.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("formula", u"\u5e8f\u53f7", None));
        ___qtablewidgetitem1 = self.formula_table.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("formula", u"\u914d\u65b9\u540d\u79f0", None));
        ___qtablewidgetitem2 = self.formula_table.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("formula", u"\u7bb1\u5b50\u957f\u5ea6", None));
        ___qtablewidgetitem3 = self.formula_table.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("formula", u"\u7bb1\u5b50\u5bbd\u5ea6", None));
        ___qtablewidgetitem4 = self.formula_table.horizontalHeaderItem(4)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("formula", u"\u7bb1\u5b50\u9ad8\u5ea6", None));
        ___qtablewidgetitem5 = self.formula_table.horizontalHeaderItem(5)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("formula", u"\u6258\u76d8X\u65b9\u5411\u957f\u5ea6", None));
        ___qtablewidgetitem6 = self.formula_table.horizontalHeaderItem(6)
        ___qtablewidgetitem6.setText(QCoreApplication.translate("formula", u"\u6258\u76d8Y\u65b9\u5411\u957f\u5ea6", None));
        ___qtablewidgetitem7 = self.formula_table.horizontalHeaderItem(7)
        ___qtablewidgetitem7.setText(QCoreApplication.translate("formula", u"\u6258\u76d8\u9ad8\u5ea6", None));
        ___qtablewidgetitem8 = self.formula_table.horizontalHeaderItem(8)
        ___qtablewidgetitem8.setText(QCoreApplication.translate("formula", u"\u5de6\u579b\u5c42\u6570", None));
        ___qtablewidgetitem9 = self.formula_table.horizontalHeaderItem(9)
        ___qtablewidgetitem9.setText(QCoreApplication.translate("formula", u"\u53f3\u579b\u5c42\u6570", None));
        ___qtablewidgetitem10 = self.formula_table.horizontalHeaderItem(10)
        ___qtablewidgetitem10.setText(QCoreApplication.translate("formula", u"\u64cd\u4f5c", None));
    # retranslateUi

