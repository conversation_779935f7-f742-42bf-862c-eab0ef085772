<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US">
<context>
    <name></name>
    <message>
        <source>None</source>
        <translation>None</translation>
    </message>
</context>
<context>
    <name>Alarm</name>
    <message>
        <source>警告</source>
        <translation>Warning</translation>
    </message>
</context>
<context>
    <name>AlarmInfoForm</name>
    <message>
        <source>报警信息</source>
        <translation>Alarm Information</translation>
    </message>
    <message>
        <source>清除错误</source>
        <translation>Clear Alarms</translation>
    </message>
    <message>
        <source>导出日志</source>
        <translation>Export Logs</translation>
    </message>
    <message>
        <source>序号</source>
        <translation>ID</translation>
    </message>
    <message>
        <source>日期</source>
        <translation>Date</translation>
    </message>
    <message>
        <source>报警值</source>
        <translation>Alarm Value</translation>
    </message>
    <message>
        <source>报警描述</source>
        <translation>Alarm Description</translation>
    </message>
    <message>
        <source>状态</source>
        <translation>Status</translation>
    </message>
    <message>
        <source>通用设置</source>
        <translation>General Settings</translation>
    </message>
    <message>
        <source>机器人设置</source>
        <translation>Robot Settings</translation>
    </message>
    <message>
        <source>机器人碰撞等级设置</source>
        <translation>Robot Collision Level</translation>
    </message>
    <message>
        <source>应用</source>
        <translation>Apply</translation>
    </message>
    <message>
        <source>碰撞等级：</source>
        <translation>Collision Level</translation>
    </message>
    <message>
        <source>标准等级</source>
        <translation>Standard Level</translation>
    </message>
    <message>
        <source>自定义百分比</source>
        <translation>Custom Percentage</translation>
    </message>
    <message>
        <source>机器人应用设置</source>
        <translation>Robot Application Settings</translation>
    </message>
    <message>
        <source>       全局速度：</source>
        <translation>       Global Speed:</translation>
    </message>
    <message>
        <source>末端负载重量：</source>
        <translation>End Effector Payload</translation>
    </message>
    <message>
        <source>mm/s</source>
        <translation>mm/s</translation>
    </message>
    <message>
        <source>%</source>
        <translation>%</translation>
    </message>
    <message>
        <source>&lt;--100%速度时              </source>
        <translation>&lt;--100% Speed</translation>
    </message>
    <message>
        <source>KG</source>
        <translation>KG</translation>
    </message>
    <message>
        <source>       偏移补偿:</source>
        <translation>Offset Compensation</translation>
    </message>
    <message>
        <source>   X:</source>
        <translation>   X:</translation>
    </message>
    <message>
        <source>    Y:</source>
        <translation>   Y:</translation>
    </message>
    <message>
        <source>    Z:</source>
        <translation>   Z:</translation>
    </message>
    <message>
        <source>(单位：mm)</source>
        <translation>(Unit: mm)</translation>
    </message>
    <message>
        <source>                       RX:</source>
        <translation>                       RX:</translation>
    </message>
    <message>
        <source>  RY:</source>
        <translation>  RY:</translation>
    </message>
    <message>
        <source>  RZ:</source>
        <translation>  RZ:</translation>
    </message>
    <message>
        <source>(单位：°)</source>
        <translation>(Unit: °)</translation>
    </message>
    <message>
        <source>焊机设置</source>
        <translation>Welder Setup</translation>
    </message>
    <message>
        <source>点焊延迟：</source>
        <translation>Spot Welding Delay:</translation>
    </message>
    <message>
        <source>S</source>
        <translation>S</translation>
    </message>
    <message>
        <source>电流电压控制：</source>
        <translation>Voltage &amp; Current Control</translation>
    </message>
    <message>
        <source>机器人控制</source>
        <translation>Robot Control</translation>
    </message>
    <message>
        <source>焊机控制</source>
        <translation>Welder Control</translation>
    </message>
    <message>
        <source>起弧延迟：</source>
        <translation>Arc Start Delay:</translation>
    </message>
    <message>
        <source>收弧延迟：</source>
        <translation>Arc End Delay:</translation>
    </message>
    <message>
        <source>系统设置</source>
        <translation>System Settings</translation>
    </message>
    <message>
        <source>语言设置：</source>
        <translation>LANG:</translation>
    </message>
    <message>
        <source>中文</source>
        <translation>Chinese</translation>
    </message>
    <message>
        <source>English</source>
        <translation>English</translation>
    </message>
    <message>
        <source>机器人IP：</source>
        <translation>Robot IP:</translation>
    </message>
</context>
<context>
    <name>Buttons</name>
    <message>
        <location filename="../logic/home_logic.py" line="1975"/>
        <source>暂停</source>
        <translation>Pause</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1977"/>
        <source>启动</source>
        <translation>Start</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1978"/>
        <source>模拟运行</source>
        <translation>Simulation</translation>
    </message>
</context>
<context>
    <name>CommandEditFailure</name>
    <message>
        <source>指令编辑后程序序列无效：
{reason}</source>
        <translation>Invalid program sequence after command editing：
{reason}</translation>
    </message>
</context>
<context>
    <name>CommandValidation</name>
    <message>
        <location filename="../logic/home_logic.py" line="538"/>
        <source>请为指令选择一个类型。</source>
        <translation>Please select a type for the command</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="555"/>
        <source>速度不可为负数</source>
        <translation>Speed cannot be negative</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="575"/>
        <source>电流不可为负数</source>
        <translation>Current cannot be negative</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="587"/>
        <source>电压不可为负数</source>
        <translation>Voltage cannot be negative</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="624"/>
        <source>段焊焊接、非焊接长度应大于40mm</source>
        <translation>For skip welding, the unwelded portion shall exceed 40mm in length</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="642"/>
        <source>速度、电流、电压、长度必须是有效的数字。</source>
        <translation>Speed, current, voltage and length must be valid numeric values</translation>
    </message>
</context>
<context>
    <name>ConnectionStatus</name>
    <message>
        <source>已连接...</source>
        <translation>connected</translation>
    </message>
    <message>
        <source>连接中...</source>
        <translation>Connecting</translation>
    </message>
</context>
<context>
    <name>Delet</name>
    <message>
        <location filename="../logic/home_logic.py" line="2334"/>
        <source>删除失败</source>
        <translation>Delet faild</translation>
    </message>
    <message>
        <source>删除成功</source>
        <translation>Delet success</translation>
    </message>
</context>
<context>
    <name>Delete</name>
    <message>
        <location filename="../logic/home_logic.py" line="2384"/>
        <source>删除成功</source>
        <translation>Delet success</translation>
    </message>
</context>
<context>
    <name>Dialog</name>
    <message>
        <source>工艺参数编辑</source>
        <translation>Process Parameter Editing</translation>
    </message>
    <message>
        <source>工艺名称</source>
        <translation>Process Name</translation>
    </message>
    <message>
        <source>焊接参数</source>
        <translation>Welding Parameters</translation>
    </message>
    <message>
        <source>电流:</source>
        <translation>Current:</translation>
    </message>
    <message>
        <source>A</source>
        <translation>A</translation>
    </message>
    <message>
        <source>电压:</source>
        <translation>Voltage</translation>
    </message>
    <message>
        <source>V</source>
        <translation>V</translation>
    </message>
    <message>
        <source>速度:</source>
        <translation>Speed:</translation>
    </message>
    <message>
        <source>mm/s</source>
        <translation>mm/s</translation>
    </message>
    <message>
        <source>摆动参数</source>
        <translation>Oscillation Parameters</translation>
    </message>
    <message>
        <source>摆动类型:</source>
        <translation>Oscillation Mode</translation>
    </message>
    <message>
        <source>三角波摆动</source>
        <translation>Triangle Wave Weaving</translation>
    </message>
    <message>
        <source>垂直L型三角波</source>
        <translation>Vertical L-Triangle Weave</translation>
    </message>
    <message>
        <source>圆型（顺时针）</source>
        <translation>Clockwise Circular Weave</translation>
    </message>
    <message>
        <source>圆型（逆时针）</source>
        <translation>Counter-Clockwise Circular Weave</translation>
    </message>
    <message>
        <source>正弦波摆动</source>
        <translation>Sine Wave Weaving</translation>
    </message>
    <message>
        <source>垂直L型正弦波</source>
        <translation>Vertical L-Sine Weave</translation>
    </message>
    <message>
        <source>立焊三角形摆动</source>
        <translation>Vertical-Up Triangle Weave</translation>
    </message>
    <message>
        <source>周期时间:</source>
        <translation>Cycle Time</translation>
    </message>
    <message>
        <source>周期不包含等待时间</source>
        <translation>Excluding Dwell Time</translation>
    </message>
    <message>
        <source>周期包含等待时间</source>
        <translation>Including Dwell Time</translation>
    </message>
    <message>
        <source>等待控制:</source>
        <translation>Dwell Control:</translation>
    </message>
    <message>
        <source>等待时间内继续移动</source>
        <translation>Continuous Motion During Dwell</translation>
    </message>
    <message>
        <source>等待时间内静止</source>
        <translation>Pause During Dwell</translation>
    </message>
    <message>
        <source>         频率:</source>
        <translation>Frequency</translation>
    </message>
    <message>
        <source>  Hz</source>
        <translation>  Hz</translation>
    </message>
    <message>
        <source>         幅度:</source>
        <translation>Amplitude:</translation>
    </message>
    <message>
        <source>mm</source>
        <translation>mm</translation>
    </message>
    <message>
        <source>左停留时间:</source>
        <translation>Dwell Time (Left)</translation>
    </message>
    <message>
        <source>  ms</source>
        <translation>  ms</translation>
    </message>
    <message>
        <source>右停留时间:</source>
        <translation>Dwell Time (Right)</translation>
    </message>
    <message>
        <source>     s</source>
        <translation>     s</translation>
    </message>
    <message>
        <source>   起弧延迟:</source>
        <translation>Arc Start Delay</translation>
    </message>
    <message>
        <source>   收弧延迟:</source>
        <translation>Arc End Delay</translation>
    </message>
    <message>
        <source>保存</source>
        <translation>Save</translation>
    </message>
</context>
<context>
    <name>EditValidation</name>
    <message>
        <location filename="../logic/home_logic.py" line="321"/>
        <source>请先选择要编辑的行</source>
        <translation>Please select the row to edit</translation>
    </message>
</context>
<context>
    <name>Error</name>
    <message>
        <source>错误</source>
        <translation>Error</translation>
    </message>
</context>
<context>
    <name>ErrorCode</name>
    <message>
        <source>无故障</source>
        <translation>No Fault</translation>
    </message>
    <message>
        <source>驱动器故障</source>
        <translation>Driver Fault</translation>
    </message>
    <message>
        <source>超出软限位故障</source>
        <translation>Soft Limit Exceeded</translation>
    </message>
    <message>
        <source>碰撞故障</source>
        <translation>Collision Fault</translation>
    </message>
    <message>
        <source>奇异位姿</source>
        <translation>Singular Pose</translation>
    </message>
    <message>
        <source>从站错误</source>
        <translation>Slave Error</translation>
    </message>
    <message>
        <source>指令点错误</source>
        <translation>Command Point Error</translation>
    </message>
    <message>
        <source>IO错误</source>
        <translation>I/O Error</translation>
    </message>
    <message>
        <source>夹爪错误</source>
        <translation>Gripper Error</translation>
    </message>
    <message>
        <source>文件错误</source>
        <translation>File Error</translation>
    </message>
    <message>
        <source>参数错误</source>
        <translation>Parameter Error</translation>
    </message>
    <message>
        <source>扩展轴超出软限位错误</source>
        <translation>Extended Axis Limit Error</translation>
    </message>
    <message>
        <source>关节配置警告</source>
        <translation>Joint Configuration Warning</translation>
    </message>
</context>
<context>
    <name>Form</name>
    <message>
        <source>点位编辑</source>
        <translation>Position Edit</translation>
    </message>
    <message>
        <source>指令类型:</source>
        <translation>Command Type</translation>
    </message>
    <message>
        <source>起始安全点</source>
        <translation>Approach Safe Position</translation>
    </message>
    <message>
        <source>起弧点</source>
        <translation>Arc Start Point</translation>
    </message>
    <message>
        <source>直线</source>
        <translation>Linear Path</translation>
    </message>
    <message>
        <source>圆弧中间点</source>
        <translation>Arc Intermediate Waypoint</translation>
    </message>
    <message>
        <source>圆弧末点</source>
        <translation>Arc End Point</translation>
    </message>
    <message>
        <source>终点安全点</source>
        <translation>Retract Safe Position</translation>
    </message>
    <message>
        <source>工艺选择:</source>
        <translation>Process Select:</translation>
    </message>
    <message>
        <source>速度:</source>
        <translation>Speed:</translation>
    </message>
    <message>
        <source>mm/s</source>
        <translation>mm/s</translation>
    </message>
    <message>
        <source>电流:</source>
        <translation>Current:</translation>
    </message>
    <message>
        <source>A</source>
        <translation>A</translation>
    </message>
    <message>
        <source>电压:</source>
        <translation>Voltage:</translation>
    </message>
    <message>
        <source>V</source>
        <translation>V</translation>
    </message>
    <message>
        <source>摆动:</source>
        <translation>Wave:</translation>
    </message>
    <message>
        <source>是</source>
        <translation>Yes</translation>
    </message>
    <message>
        <source>否</source>
        <translation>No</translation>
    </message>
    <message>
        <source>段焊:</source>
        <translation>Segment Welding</translation>
    </message>
    <message>
        <source>段焊焊接、非焊接长度应大于40mm</source>
        <translation>For skip welding, the unwelded portion shall exceed 40mm in length</translation>
    </message>
    <message>
        <source>执行长度:</source>
        <translation>&quot;Execution Length</translation>
    </message>
    <message>
        <source>mm</source>
        <translation>mm</translation>
    </message>
    <message>
        <source>非执行长度:</source>
        <translation>Non-Processed Length</translation>
    </message>
    <message>
        <source>起弧:</source>
        <translation>Arc start：</translation>
    </message>
    <message>
        <source>应用</source>
        <translation>Apply</translation>
    </message>
    <message>
        <source>另存为工艺</source>
        <translation>Save as process</translation>
    </message>
    <message>
        <source>Form</source>
        <translation>Form</translation>
    </message>
    <message>
        <source>打开</source>
        <translation>Open</translation>
    </message>
    <message>
        <source>新建</source>
        <translation>New</translation>
    </message>
    <message>
        <source>保存</source>
        <translation>Save</translation>
    </message>
    <message>
        <source>另存为</source>
        <translation>Save as</translation>
    </message>
    <message>
        <source>删除程序</source>
        <translation>Delete Program</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;当前程序：&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>Current Program</translation>
    </message>
    <message>
        <source>一键编程区</source>
        <translation>Programming Zone</translation>
    </message>
    <message>
        <source>起始
安全点</source>
        <translation>Start Safety Point</translation>
    </message>
    <message>
        <source>圆弧
中间点</source>
        <translation>Arc Mid Point</translation>
    </message>
    <message>
        <source>终点
安全点</source>
        <translation type="unfinished">End safety Point</translation>
    </message>
    <message>
        <source>行号</source>
        <translation>ID</translation>
    </message>
    <message>
        <source>指令类型</source>
        <translation>Command Type</translation>
    </message>
    <message>
        <source>点位</source>
        <translation>Position Position</translation>
    </message>
    <message>
        <source>速度</source>
        <translation>Speed</translation>
    </message>
    <message>
        <source>编辑行</source>
        <translation>Edit Line</translation>
    </message>
    <message>
        <source>删除行</source>
        <translation>Delete Line</translation>
    </message>
    <message>
        <source>上移</source>
        <translation>Move Up</translation>
    </message>
    <message>
        <source>下移</source>
        <translation>Move Down</translation>
    </message>
    <message>
        <source>重定位</source>
        <translation>Relocate</translation>
    </message>
    <message>
        <source>移动</source>
        <translation>Move</translation>
    </message>
    <message>
        <source>实时点位</source>
        <translation>Real-Time Position</translation>
    </message>
    <message>
        <source>Y:</source>
        <translation>Y:</translation>
    </message>
    <message>
        <source>RY:</source>
        <translation>RY:</translation>
    </message>
    <message>
        <source>Z:</source>
        <translation>Z:</translation>
    </message>
    <message>
        <source>RZ:</source>
        <translation>RZ:</translation>
    </message>
    <message>
        <source>X:</source>
        <translation>X:</translation>
    </message>
    <message>
        <source>RX:</source>
        <translation>RX:</translation>
    </message>
    <message>
        <source>模拟运行</source>
        <translation>Simulation</translation>
    </message>
    <message>
        <source>  启  动  </source>
        <translation>Start</translation>
    </message>
    <message>
        <source>停止</source>
        <translation>Stop</translation>
    </message>
    <message>
        <source>暂停</source>
        <translation>Pause</translation>
    </message>
    <message>
        <source>设置</source>
        <translation>Setting</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;智 能 焊 接&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>Smart Welding</translation>
    </message>
    <message>
        <source>主页</source>
        <translation>Home</translation>
    </message>
    <message>
        <source>工艺管理</source>
        <translation>Process</translation>
    </message>
    <message>
        <source>通用设置</source>
        <translation>Settings</translation>
    </message>
    <message>
        <source>报警管理</source>
        <translation>Alarm</translation>
    </message>
    <message>
        <source>手动控制</source>
        <translation>Manual</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:12pt;&quot;&gt;一键编程区&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:12pt;&quot;&gt;Programming Panel&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>X：</source>
        <translation>X:</translation>
    </message>
    <message>
        <source>RX：</source>
        <translation>RX：</translation>
    </message>
    <message>
        <source>RY：</source>
        <translation>RY：</translation>
    </message>
    <message>
        <source>RZ：</source>
        <translation>RZ：</translation>
    </message>
    <message>
        <source>z：</source>
        <translation>z：</translation>
    </message>
    <message>
        <source>Y：</source>
        <translation>Y：</translation>
    </message>
    <message>
        <source>111</source>
        <translation>111</translation>
    </message>
    <message>
        <source>121.02</source>
        <translation>121.02</translation>
    </message>
    <message>
        <source>23.00</source>
        <translation>23.00</translation>
    </message>
    <message>
        <source>1212</source>
        <translation>1212</translation>
    </message>
    <message>
        <source>23</source>
        <translation>23</translation>
    </message>
    <message>
        <source>56</source>
        <translation>56</translation>
    </message>
    <message>
        <source>PushButton</source>
        <translation>PushButton</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:14pt;&quot;&gt;当前程序:&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>当前程序</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:14pt;&quot;&gt;工艺选择:&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>Process Selection</translation>
    </message>
    <message>
        <source>工艺包1</source>
        <translation>Process Package1</translation>
    </message>
    <message>
        <source>工艺包2</source>
        <translation>Process Package2</translation>
    </message>
    <message>
        <source>工艺包3</source>
        <translation>Process Package3</translation>
    </message>
    <message>
        <source>工艺包4</source>
        <translation>Process Package4</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:18pt; font-weight:700;&quot;&gt;这是工艺管理&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:18pt; font-weight:700;&quot;&gt;This is process management&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Microsoft YaHei UI&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:24pt;&quot;&gt;手动控制&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:&apos;Microsoft YaHei UI&apos;; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:24pt;&quot;&gt;Manual Control&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>机器人控制</source>
        <translation>Robot Control</translation>
    </message>
    <message>
        <source>30</source>
        <translation>30</translation>
    </message>
    <message>
        <source>%</source>
        <translation>%</translation>
    </message>
    <message>
        <source>单次长按阈值:</source>
        <translation>Long-press Time Threshold</translation>
    </message>
    <message>
        <source>J1:</source>
        <translation>J1:</translation>
    </message>
    <message>
        <source>-</source>
        <translation>-</translation>
    </message>
    <message>
        <source>+</source>
        <translation>+</translation>
    </message>
    <message>
        <source>J2:</source>
        <translation>J2:</translation>
    </message>
    <message>
        <source>J3:</source>
        <translation>J3:</translation>
    </message>
    <message>
        <source>J4:</source>
        <translation>J4:</translation>
    </message>
    <message>
        <source>J5:</source>
        <translation>J5:</translation>
    </message>
    <message>
        <source>J6:</source>
        <translation>J6:</translation>
    </message>
    <message>
        <source>焊机控制</source>
        <translation>Welding Machine Control</translation>
    </message>
    <message>
        <source>起弧</source>
        <translation>ARCStart</translation>
    </message>
    <message>
        <source>收弧</source>
        <translation>ARCEnd</translation>
    </message>
    <message>
        <source>送气</source>
        <translation>Gas On</translation>
    </message>
    <message>
        <source>关气</source>
        <translation>Gas Off</translation>
    </message>
    <message>
        <source>正向送丝</source>
        <translation>WF</translation>
    </message>
    <message>
        <source>反向送丝</source>
        <translation>WR</translation>
    </message>
    <message>
        <source>点焊</source>
        <translation>Spot Welding</translation>
    </message>
    <message>
        <source>安全点</source>
        <translation>Safety Point</translation>
    </message>
    <message>
        <source>J6：</source>
        <translation>J6：</translation>
    </message>
    <message>
        <source>J1：</source>
        <translation>J1：</translation>
    </message>
    <message>
        <source>J2：</source>
        <translation>J2：</translation>
    </message>
    <message>
        <source>J5：</source>
        <translation>J5：</translation>
    </message>
    <message>
        <source>J4：</source>
        <translation>J4：</translation>
    </message>
    <message>
        <source>J3：</source>
        <translation>J3：</translation>
    </message>
    <message>
        <source>1(25N)</source>
        <translation>1(25N)</translation>
    </message>
    <message>
        <source>2(33N)</source>
        <translation>2(33N)</translation>
    </message>
    <message>
        <source>3(41N)</source>
        <translation>3(41N)</translation>
    </message>
    <message>
        <source>4(50N)</source>
        <translation>4(50N)</translation>
    </message>
    <message>
        <source>5(58N)</source>
        <translation>5(58N)</translation>
    </message>
    <message>
        <source>6(66N)</source>
        <translation>6(66N)</translation>
    </message>
    <message>
        <source>7(75N)</source>
        <translation>7(75N)</translation>
    </message>
    <message>
        <source>8(83N)</source>
        <translation>8(83N)</translation>
    </message>
    <message>
        <source>9(91N)</source>
        <translation>9(91N)</translation>
    </message>
    <message>
        <source>10(100N)</source>
        <translation>10(100N)</translation>
    </message>
</context>
<context>
    <name>HomeLogic</name>
    <message>
        <source>另存为成功</source>
        <translation>Save as successfully</translation>
    </message>
    <message>
        <source>程序已成功另存为 &apos;%1&apos;.</source>
        <translation>Program successfully saved as &apos;%1&apos;.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="658"/>
        <source>编辑失败</source>
        <translation>Edit Faild</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="659"/>
        <source>指令编辑后程序序列无效：
{reason}</source>
        <translation>Invalid program sequence after command editing：
{reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="708"/>
        <source>名称重复</source>
        <translation>Name already exists</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="709"/>
        <source>工艺名称 &apos;{new_process_name}&apos; 已存在，请使用其他名称</source>
        <translation>Process name &apos;{new_process_name}&apos; is exist，please use other name</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="753"/>
        <source>保存成功</source>
        <translation>Save successfully</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="754"/>
        <source>工艺 &apos;{new_process_name}&apos; 已成功另存为。</source>
        <translation>Process &apos;{new_process_name}&apos; has sucessed save as。</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="779"/>
        <location filename="../logic/home_logic.py" line="2140"/>
        <location filename="../logic/home_logic.py" line="2151"/>
        <location filename="../logic/home_logic.py" line="2213"/>
        <source>保存失败</source>
        <translation>Save faild</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="780"/>
        <source>另存为工艺时发生错误：{e}
操作已回滚。</source>
        <translation>An error occurred while saving the process: {e}
The operation has been rolled back.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="911"/>
        <location filename="../logic/home_logic.py" line="958"/>
        <location filename="../logic/home_logic.py" line="2408"/>
        <source>操作提示</source>
        <translation>Operation Hint</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="912"/>
        <source>请先创建或打开一个程序。</source>
        <translation>Please create or open a program first.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="951"/>
        <source>已添加指令: {instruction_type}</source>
        <translation>Command added: {instruction_type}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="959"/>
        <source>请先创建或打开一个程序，才能自动添加指令。</source>
        <translation>Please create or open a program first to enable auto-adding commands.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="964"/>
        <source>尝试自动添加指令时，当前无打开的程序。</source>
        <translation>When trying to automatically add commands, there are currently no open programs.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="983"/>
        <location filename="../logic/home_logic.py" line="2420"/>
        <location filename="../logic/home_logic.py" line="2509"/>
        <source>操作受限</source>
        <translation>Operation restricted</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="984"/>
        <source>程序已以&apos;终点安全点&apos;结束，无法在末尾添加新指令。</source>
        <translation>The program has already ended with a &apos;Safety End Point&apos;. Cannot append new instructions at the end.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1035"/>
        <source>自动添加了指令: {msg_text} (类型: {target_instruction_type})</source>
        <translation>Automatically added instruction: {msg_text} (Type: {target_instruction_type})</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1061"/>
        <location filename="../logic/home_logic.py" line="2395"/>
        <source>删除失败</source>
        <translation>Delet faild</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1062"/>
        <source>请选择要删除的行。</source>
        <translation>Please select the row to delete.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1077"/>
        <source>Operation Restricted</source>
        <translation>Operation Restricted</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1078"/>
        <source>Operation not allowed: {reason}</source>
        <translation>Operation not allowed: {reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1104"/>
        <location filename="../logic/home_logic.py" line="1114"/>
        <location filename="../logic/home_logic.py" line="1176"/>
        <location filename="../logic/home_logic.py" line="1186"/>
        <location filename="../logic/home_logic.py" line="2497"/>
        <source>移动失败</source>
        <translation>Move faild</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1105"/>
        <location filename="../logic/home_logic.py" line="1177"/>
        <location filename="../logic/home_logic.py" line="2488"/>
        <source>请选择要移动的行。</source>
        <translation>Please select the row to move</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1115"/>
        <location filename="../logic/home_logic.py" line="2498"/>
        <source>暂不支持多点移动，请选择单行。</source>
        <translation>Multi-row selection is not supported. Please select a single row.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1165"/>
        <source>指令已从 {row_index} 上移到 {row_index - 1}</source>
        <translation>Command moved up from row {row_index} to row {row_index - 1}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1187"/>
        <source>暂不支持多行移动，请选择单行。</source>
        <translation>Multi-row movement is not supported. Please select a single row</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1226"/>
        <source>指令已从 {row_index} 跳跃下移到 {row_index + 2}，越过圆弧点对。</source>
        <translation>Command jumped from {row_index} to {row_index+2} (arc bypass ΔR=${radius})</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1236"/>
        <source>指令已从 {row_index} 下移到 {row_index + 1}</source>
        <translation>Command moved from {row_index} to {row_index+1}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1247"/>
        <location filename="../logic/home_logic.py" line="1385"/>
        <source>程序中存在‘待指定类型’的指令，请先编辑并指定其具体类型。</source>
        <translation>There are instructions marked as &apos;type pending&apos; in the program. Please edit and specify their concrete types first.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1253"/>
        <source>列表为空时，第一条数据必须是起始安全点。</source>
        <translation>When the list is empty, the first data entry must be a safety origin point.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1259"/>
        <source>列表不为空时，不能添加起始安全点。</source>
        <translation>When the list is not empty, adding a safety origin point is prohibited.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1263"/>
        <source>上一条指令是&apos;{self.INSTR_END_SAFE}&apos;，不允许在其后添加任何指令。</source>
        <translation>If the previous command was &apos;{self.INSTR_END_SAFE}&apos;, no subsequent commands are permitted.</translation>
    </message>
    <message>
        <source>上一条指令是 is &apos;{last_instruction_type}&apos;, 不允许在其后添加&apos;{instruction_type}允许的指令类型:{allowed_types}</source>
        <translation>The previous directive is &apos;{last_instruction_type}&apos;, and it is not allowed to add &apos;{instruction_type} allowed instruction type after it: {allowed_types}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1290"/>
        <source>行索引无效。</source>
        <translation>Row index is invalid.</translation>
    </message>
    <message>
        <source>删除该指令会破坏程序结构：{reason}</source>
        <translation>Deleting this instruction would damage the program structure: {reason}</translation>
    </message>
    <message>
        <source>删除该指令会破坏程序序列：{reason}</source>
        <translation>Removing this instruction would corrupt the program sequence: {reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1337"/>
        <source>无法移动到列表范围之外。</source>
        <translation>Cannot move outside the list bounds.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1394"/>
        <source>程序的第一条指令必须是&apos;起始安全点&apos;。</source>
        <translation>The first instruction of the program must be a &apos;safe-point start&apos;.</translation>
    </message>
    <message>
        <source>No subsequent instructions allowed after &apos;%1&apos;</source>
        <translation>No subsequent instructions allowed after &apos;%1&apos;</translation>
    </message>
    <message>
        <source>, </source>
        <translation>, </translation>
    </message>
    <message>
        <source>Invalid instruction sequence: &apos;{current}&apos; cannot be followed by &apos;{next}&apos;. Allowed types: {allowed}</source>
        <translation>Invalid instruction sequence: &apos;{current}&apos; cannot be followed by &apos;{next}&apos;. Allowed types: {allowed}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1654"/>
        <source>没有可加载的程序指令。</source>
        <translation>There are no program instructions that can be loaded</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1730"/>
        <source>解析点位数据失败：{record[&apos;point_position&apos;]} - 错误: {e}</source>
        <translation>Failed to parse point data: {record[&apos;point_position&apos;]} - Error: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1731"/>
        <source>点位解析失败! (原始: {record[&apos;point_position&apos;]})</source>
        <translation>Point analysis failed! (Original: {record[&apos;point_position&apos;]})</translation>
    </message>
    <message>
        <source>     工艺: {process_name};  </source>
        <translation>     Process: {process_name};  </translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1779"/>
        <source>       起弧:</source>
        <translation>Arc start</translation>
    </message>
    <message>
        <source>摆动: {swing_types.get(swing_type_int)} {frequency:.1f}HZ {amplitude:.1f}mm;</source>
        <translation>Wave: {swing_types.get(swing_type_int)} {frequency:.1f}HZ {amplitude:.1f}mm;</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1299"/>
        <source>删除该指令会破坏程序结构：</source>
        <translation>Deleting this directive will break the program structure:</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1313"/>
        <source>删除该指令会破坏程序序列：{}</source>
        <translation>Deleting the directive will break the program sequence: {}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1402"/>
        <source>程序中只能有一个&apos;起始安全点&apos;指令。</source>
        <translation>There can be only one &apos;safe start point&apos; instruction in the program.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1409"/>
        <source>The &apos;endpoint safety point&apos; instruction must be the last instruction in the program</source>
        <translation>The &apos;endpoint safety point&apos; instruction must be the last instruction in the program</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1413"/>
        <source>程序中只能有一个&apos;终点安全点&apos;指令。</source>
        <translation>There can be only one &apos;end safety point&apos; instruction in the program.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1426"/>
        <source>No instructions allowed after &apos;{current_type}&apos;</source>
        <translation>No instructions allowed after &apos;{current_type}&apos;</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1434"/>
        <source>Invalid instruction sequence: &apos;{current_type}&apos; cannot be followed by &apos;{next_type}&apos;. Allowed types: {allowed_types}</source>
        <translation>Invalid instruction sequence: &apos;{current_type}&apos; cannot be followed by &apos;{next_type}&apos;. Allowed types: {allowed_types}</translation>
    </message>
    <message>
        <source>Invalid instruction sequence: {current_type} cannot be followed by {next_type}. Allowed types: {allowed_types}</source>
        <translation>Invalid instruction sequence: {current_type} cannot be followed by {next_type}. Allowed types: {allowed_types}</translation>
    </message>
    <message>
        <source>Invalid instruction sequence: &apos;%1&apos; cannot be followed by &apos;%2&apos;. Allowed types: %3</source>
        <translation>Invalid instruction sequence: &apos;%1&apos; cannot be followed by &apos;%2&apos;. Allowed types: %3</translation>
    </message>
    <message>
        <source>指令序列无效：&apos;{current_type}&apos;后不允许是&apos;{next_type}&apos;。允许的指令类型：{&apos;, &apos;.join(self.ALLOWED_NEXT_INSTRUCTIONS.get(current_type, {&apos;无&apos;}))}</source>
        <translation>Invalid command sequence: &apos;{current_type}&apos; cannot be followed by &apos;{next_type}&apos;. Allowed command types: {&apos;, &apos;.join(self.ALLOWED_NEXT_INSTRUCTIONS.get(current_type, {&apos;none&apos;}))}</translation>
    </message>
    <message>
        <source>LANGUAGE</source>
        <translation>LANGUAGE</translation>
    </message>
    <message>
        <source>Invalid sequence: &apos;%1&apos; cannot be followed by &apos;%2&apos;. Allowed types: %3</source>
        <translation>Invalid sequence: &apos;%1&apos; cannot be followed by &apos;%2&apos;. Allowed types: %3</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2377"/>
        <source>未命名程序</source>
        <translation>Unnamed program</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1841"/>
        <location filename="../logic/home_logic.py" line="1853"/>
        <source>程序启动失败</source>
        <translation>Program startup failed</translation>
    </message>
    <message>
        <source>起始安全点</source>
        <translation>Start safety point</translation>
    </message>
    <message>
        <source>起弧点</source>
        <translation>Arc Start Point</translation>
    </message>
    <message>
        <source>直线</source>
        <translation>Linear</translation>
    </message>
    <message>
        <source>圆弧中间点</source>
        <translation>Arc Mid point</translation>
    </message>
    <message>
        <source>圆弧末点</source>
        <translation>Arc End Point</translation>
    </message>
    <message>
        <source>终点安全点</source>
        <translation>End safety point</translation>
    </message>
    <message>
        <source>待指定类型</source>
        <translation>Unspecified Type</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1268"/>
        <source>Instruction sequence violation. %1 cannot follow %2. Allowed: %3</source>
        <translatorcomment>不允许</translatorcomment>
        <translation>结构序列混乱</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1272"/>
        <source>none</source>
        <translation>none</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1842"/>
        <source>机器人处于【自动模式停止状态】下才可启动程序。</source>
        <translation>Program startup permitted only when robot in [Auto mode ready state]</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1854"/>
        <source>程序序列不符合规则，无法启动！
原因: {reason}</source>
        <translation>The program sequence is invalid and cannot start!

Reason: {reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1876"/>
        <source>确认运行</source>
        <translation>Operation confirmed.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1877"/>
        <source>确定要运行当前程序吗？</source>
        <translation>Confirm running the current program?</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1892"/>
        <location filename="../logic/home_logic.py" line="2028"/>
        <source>暂停</source>
        <translation>Pause</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1906"/>
        <location filename="../logic/home_logic.py" line="1916"/>
        <source>运行中</source>
        <translation>Runing</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1925"/>
        <source>启动</source>
        <translation>Start</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1926"/>
        <source>模拟运行</source>
        <translation>Simulation</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2041"/>
        <source>继续</source>
        <translation>Continue</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2061"/>
        <source>正在执行圆弧指令 (行 {data_index + 1})，暂停功能已禁用。</source>
        <translation>Currently executing arc command (line {data_index + 1}), pause functionality has been disabled.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2065"/>
        <source>正在执行指令 (行 {data_index + 1})，暂停功能已启用。</source>
        <translation>Executing command (line {data_index + 1}), pause feature has been enabled.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2070"/>
        <source>更新暂停按钮状态时出错: {e}</source>
        <translation>Error occurred while updating the pause button status: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2090"/>
        <source>创建失败</source>
        <translation>Failed to create</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2091"/>
        <source>程序名称 &apos;{program_name}&apos; 已存在，请使用其他名称。</source>
        <translation>The program name &apos;{program_name}&apos; already exists. Please use a different name.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2113"/>
        <source>新程序创建成功，名称：{self._current_program_name}</source>
        <translation>New program created successfully. Name: {self._current_program_name}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2117"/>
        <source>错误</source>
        <translation>Error</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2118"/>
        <source>创建程序失败，请检查数据库。</source>
        <translation> Failed to create program. Please check the database.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2141"/>
        <source>程序序列不符合规则，无法保存！
原因: {reason}</source>
        <translation>Invalid program sequence. Save failed!

Reason: {reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2152"/>
        <source>当前没有选定的程序，请先通过 &apos;新建&apos; 按钮创建一个程序。</source>
        <translation>No program selected. Please create a new one via the &apos;New&apos; button first.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2167"/>
        <source>已删除程序 &apos;{program_name_to_save}&apos; 的所有旧指令详情。</source>
        <translation>Cleared all old instructions from program: &apos;{program_name_to_save}&apos;.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2191"/>
        <source>Failed to insert instruction {instruction.get(&apos;instruction_type&apos;)}</source>
        <translation>Failed to insert instruction {instruction.get(&apos;instruction_type&apos;)}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2193"/>
        <source>插入指令: {instruction.get(&apos;instruction_type&apos;)} 到程序 &apos;{program_name_to_save}&apos;</source>
        <translation>插入指令: {instruction.get(&apos;instruction_type&apos;)} 到程序 &apos;{program_name_to_save}&apos;</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2207"/>
        <source>程序 &apos;{self._current_program_name}&apos; 所有指令已成功保存到数据库。</source>
        <translation>All commands of program &apos;{self._current_program_name}&apos; have been successfully saved to the database.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2214"/>
        <source>保存程序时发生错误：{e}
操作已回滚。</source>
        <translation>Error saving program: {e}

Operation has been rolled back.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2219"/>
        <source>保存程序 &apos;{self._current_program_name}&apos; 失败: {e}</source>
        <translation>Failed to save program &apos;{self._current_program_name}&apos;: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2251"/>
        <location filename="../logic/home_logic.py" line="2263"/>
        <location filename="../logic/home_logic.py" line="2321"/>
        <source>另存为失败</source>
        <translation>Save as failed</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2252"/>
        <source>程序名称 &apos;{new_program_name}&apos; 已存在，请使用其他名称。</source>
        <translation>The program name &apos;{new_program_name}&apos; already exists. Please use a different name.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2264"/>
        <source>程序序列不符合规则，无法另存为！
原因: {reason}</source>
        <translation>he program sequence does not comply with the rules and cannot be saved as a new file!

Reason: {reason}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2322"/>
        <source>另存为程序时发生错误：{e}
操作已回滚。</source>
        <translation>An error occurred while saving the program: {e}

The operation has been rolled back.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2327"/>
        <source>另存为程序 &apos;{new_program_name}&apos; 失败: {e}</source>
        <translation>Failed to save program &apos;{new_program_name}&apos;: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2344"/>
        <source>删除程序</source>
        <translation>Delete Program</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2345"/>
        <source>确定你想要删除程序 &apos;{0}&apos;?</source>
        <translation>Are you sure you want to delete program &apos;{0}&apos;?&quot;</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2364"/>
        <source>删除program_detail表记录结果：{delete_detail_result}</source>
        <translation>Deleted records from &apos;program_detail&apos; table: {delete_detail_result}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2396"/>
        <source>删除过程中发生错误：{e}</source>
        <translation>Error deleting records from &apos;program_detail&apos; table: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2401"/>
        <source>删除程序 &apos;{current_program}&apos; 失败: {e}</source>
        <translation>Failed to delete program &apos;{current_program}&apos;: {e}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2409"/>
        <source>请先选择要更新的行</source>
        <translation>Please select the row to update first</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2421"/>
        <source>请先编辑“待指定类型”的指令，指定其具体类型后再进行点位重定位。</source>
        <translation>Modify the &quot;Unspecified-Type&quot; instruction to define its concrete type prior to coordinate recalibration.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2435"/>
        <source>行 {} 的点位信息已更新。</source>
        <translation>The point information for {} has been updated.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2438"/>
        <source>点位更新</source>
        <translation>Position updated</translation>
    </message>
    <message>
        <source>行 {row_index + 1} 的点位信息已更新。</source>
        <translation>Location information for row {row_index + 1} has been updated.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2447"/>
        <source>更新失败</source>
        <translation>Update Failed</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2448"/>
        <source>选定的行索引超出程序指令范围。</source>
        <translation>Selected row index exceeds valid program instruction range.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2487"/>
        <source>失败</source>
        <translation>Failed</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2510"/>
        <source>请先编辑“待指定类型”的指令，指定其具体类型后再执行移动。</source>
        <translation>Please specify the type of the target element before executing the move command.</translation>
    </message>
</context>
<context>
    <name>InputError</name>
    <message>
        <location filename="../logic/home_logic.py" line="276"/>
        <location filename="../logic/home_logic.py" line="291"/>
        <location filename="../logic/home_logic.py" line="537"/>
        <location filename="../logic/home_logic.py" line="554"/>
        <location filename="../logic/home_logic.py" line="574"/>
        <location filename="../logic/home_logic.py" line="586"/>
        <location filename="../logic/home_logic.py" line="622"/>
        <location filename="../logic/home_logic.py" line="641"/>
        <location filename="../logic/home_logic.py" line="722"/>
        <source>输入错误</source>
        <translation>Input error</translation>
    </message>
</context>
<context>
    <name>InputValidation</name>
    <message>
        <location filename="../logic/home_logic.py" line="277"/>
        <source>电流值必须在0到500之间</source>
        <translation>Current value must be within 0-500 range</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="292"/>
        <source>电压值必须在0到50之间</source>
        <translation>Voltage value must be within 0-50 range</translation>
    </message>
    <message>
        <source>电压值必须在0到30之间</source>
        <translation>Voltage value must be within 0-50 range</translation>
    </message>
</context>
<context>
    <name>InstructionType</name>
    <message>
        <location filename="../logic/home_logic.py" line="1683"/>
        <source>起始安全点</source>
        <translation>Start Safety Point</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1684"/>
        <source>起弧点</source>
        <translation>Arc Start Point</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1685"/>
        <source>直线</source>
        <translation>Linear Path</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1686"/>
        <source>圆弧中间点</source>
        <translation>Arc Mid Point</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1687"/>
        <source>圆弧末点</source>
        <translation>Arc End Point</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1688"/>
        <source>终点安全点</source>
        <translation>End Safety Point</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1691"/>
        <source>待指定类型</source>
        <translation>Unspecified Type</translation>
    </message>
</context>
<context>
    <name>Logging</name>
    <message>
        <location filename="../logic/home_logic.py" line="786"/>
        <source>另存为工艺 &apos;{new_process_name}&apos; 失败: {e}</source>
        <translation>Failed to save as process &apos;{new_process_name}&apos;: {e}</translation>
    </message>
</context>
<context>
    <name>MainWindow</name>
    <message>
        <source>编辑</source>
        <translation>Edit</translation>
    </message>
    <message>
        <source>删除</source>
        <translation>Delet</translation>
    </message>
    <message>
        <source>自动</source>
        <translation>Auto</translation>
    </message>
    <message>
        <source>手动</source>
        <translation>Manual</translation>
    </message>
    <message>
        <source>拖动</source>
        <translation>Drag</translation>
    </message>
    <message>
        <source>操作确认</source>
        <translation>Operation Confirmation</translation>
    </message>
    <message>
        <source>紧急停止已触发！</source>
        <translation>Emergency stop triggered!</translation>
    </message>
    <message>
        <source>紧急停止已触发!</source>
        <translation>Emergency stop triggered!</translation>
    </message>
    <message>
        <source>智焊</source>
        <translation>Intellect Welding</translation>
    </message>
</context>
<context>
    <name>Move Filed</name>
    <message>
        <location filename="../logic/home_logic.py" line="1126"/>
        <location filename="../logic/home_logic.py" line="1198"/>
        <source>移动失败</source>
        <translation>Move faild</translation>
    </message>
</context>
<context>
    <name>MyDialog</name>
    <message>
        <location filename="../logic/home_logic.py" line="1127"/>
        <source>已是第一行，无法上移。</source>
        <translation>Already the first row, cannot move up</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1199"/>
        <source>已是最后一行，无法下移。</source>
        <translation>Already at the last row; cannot move down further.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1554"/>
        <location filename="../logic/home_logic.py" line="1565"/>
        <source>打开程序</source>
        <translation>Open program</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1555"/>
        <source>没有可用的程序。请先创建新程序。</source>
        <translation>No programs available. Please create a new program first.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1566"/>
        <source>选择要打开的程序：</source>
        <translation>Select a program to open:</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1865"/>
        <source>程序列表为空，无法启动。</source>
        <translation>The program list is empty. Cannot start.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2075"/>
        <source>新建程序</source>
        <translation>New Program</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2076"/>
        <source>请输入程序名称：</source>
        <translation>Please inport program name:</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2129"/>
        <source>当前程序列表为空，无需保存。</source>
        <translation>The current program list is empty. No need to save.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2308"/>
        <source>程序已成功另存为『%1』。</source>
        <translation>Program successfully saved as &apos;%1&apos;</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2385"/>
        <source>程序『%s』已成功删除</source>
        <translation>The program &apos;%s&apos; has been successfully deleted.</translation>
    </message>
    <message>
        <source>程序已成功另存为 &apos;{new_program_name}&apos;。</source>
        <translation>The program has been successfully saved as &apos;{new_program_name}&apos;.</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2335"/>
        <source>当前没有打开的程序或程序未命名。</source>
        <translation>No program is currently open, or the program is unnamed.</translation>
    </message>
    <message>
        <source>程序 &apos;{current_program}&apos; 已成功删除</source>
        <translation>The program &apos;{current_program}&apos; has been successfully deleted.</translation>
    </message>
    <message>
        <source>行 {row_index + 1} 的点位信息已更新。</source>
        <translation>Location information for row {row_index + 1} has been updated.</translation>
    </message>
</context>
<context>
    <name>OperationHint</name>
    <message>
        <location filename="../logic/home_logic.py" line="320"/>
        <source>操作提示</source>
        <translation>Operation Hint</translation>
    </message>
</context>
<context>
    <name>ProcessController</name>
    <message>
        <location filename="../logic/home_logic.py" line="844"/>
        <source>未找到工艺包 ID 为 {process_logic.PROCESS_update} 的记录。</source>
        <translation>No record found for process package ID: {process_logic.PROCESS_update}</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="875"/>
        <source>已更新所有使用工艺包 ID 为 {process_logic.PROCESS_update} 的行。</source>
        <translation>Updated all rows using process package ID: {process_logic.PROCESS_update}</translation>
    </message>
</context>
<context>
    <name>ProcessDialogs</name>
    <message>
        <location filename="../logic/home_logic.py" line="693"/>
        <source>另存为工艺</source>
        <translation>Save as process</translation>
    </message>
    <message>
        <source>名称重复</source>
        <translation>Name already exists</translation>
    </message>
</context>
<context>
    <name>ProcessInfo</name>
    <message>
        <location filename="../logic/home_logic.py" line="1742"/>
        <source>      工艺: {0};</source>
        <translation>      Process: {0};</translation>
    </message>
</context>
<context>
    <name>ProcessInfoForm</name>
    <message>
        <source>Form</source>
        <translation>Form</translation>
    </message>
    <message>
        <source>导出</source>
        <translation>EXP</translation>
    </message>
    <message>
        <source>导入</source>
        <translation>IMP</translation>
    </message>
    <message>
        <source>新增</source>
        <translation>ADD</translation>
    </message>
    <message>
        <source>序号</source>
        <translation>ID</translation>
    </message>
    <message>
        <source>工艺名称</source>
        <translation>Process Name</translation>
    </message>
    <message>
        <source>焊接参数</source>
        <translation>Welding Parameters</translation>
    </message>
    <message>
        <source>摆动参数</source>
        <translation>Weaving Parameters</translation>
    </message>
    <message>
        <source>操作</source>
        <translation>Operation</translation>
    </message>
</context>
<context>
    <name>ProcessNaming</name>
    <message>
        <location filename="../logic/home_logic.py" line="694"/>
        <source>请输入新的工艺名称：</source>
        <translation>Please enter a new process name:</translation>
    </message>
    <message>
        <source>工艺名称 &apos;{new_process_name}&apos; 已存在，请使用其他名称</source>
        <translation>Process name &apos;{new_process_name}&apos; is exist，please use other name</translation>
    </message>
</context>
<context>
    <name>ProcessTable</name>
    <message>
        <source>序号</source>
        <translation>ID</translation>
    </message>
    <message>
        <source>工艺名称</source>
        <translation>Process Name</translation>
    </message>
    <message>
        <source>焊接参数</source>
        <translation>Welding Parameters</translation>
    </message>
    <message>
        <source>摆动参数</source>
        <translation>WavePara</translation>
    </message>
    <message>
        <source>操作</source>
        <translation>Operation</translation>
    </message>
</context>
<context>
    <name>Program</name>
    <message>
        <location filename="../logic/home_logic.py" line="64"/>
        <source>Untitled Program</source>
        <translation>Untitled Program</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2199"/>
        <source>程序 {prog_name} 已成功保存！</source>
        <translation>The program &quot;{prog_name}&quot; has been successfully saved!</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2201"/>
        <source>保存成功</source>
        <translation>Save successfully</translation>
    </message>
</context>
<context>
    <name>Reason</name>
    <message>
        <location filename="../logic/home_logic.py" line="723"/>
        <source>速度、电流、电压必须是有效的数字。</source>
        <translation>Speed, current, and voltage must be valid numbers.</translation>
    </message>
    <message>
        <source>工艺 &apos;{new_process_name}&apos; 已成功另存为。</source>
        <translation>Process &apos;{new_process_name}&apos; has sucessed save as。</translation>
    </message>
    <message>
        <source>另存为工艺时发生错误：{e}
操作已回滚。</source>
        <translation>An error occurred while saving the process: {e}
The operation has been rolled back.</translation>
    </message>
    <message>
        <source>未找到该工艺参数！</source>
        <translation>The process parameter was not found！</translation>
    </message>
    <message>
        <source>工艺名称不能为空！</source>
        <translation>Process name cannot be empty!</translation>
    </message>
    <message>
        <source>输入格式错误：{str(e)}</source>
        <translation>Input format error</translation>
    </message>
    <message>
        <source>保存失败：{str(e)}</source>
        <translation>Save faild</translation>
    </message>
    <message>
        <source>未找到ID为 {process_id} 的工艺参数，可能已被删除或不存在。</source>
        <translation>No process parameters found for ID {process_id}. It may have been deleted or does not exist.</translation>
    </message>
    <message>
        <source>工艺 &apos;{process_name}&apos; 正在被程序&gt; {used_in_programs[0][&apos;program_id&apos;]} &lt;使用中，无法删除！</source>
        <translation>Process &apos;{process_name}&apos; cannot be deleted - currently in use by Program &gt;{used_in_programs[0][&apos;program_id&apos;]}&lt;使用中，无法删除！</translation>
    </message>
    <message>
        <source>删除失败：{str(e)}</source>
        <translation>Delet faild：{str(e)}</translation>
    </message>
    <message>
        <source>工艺参数已成功保存至：
{file_path}</source>
        <translation>Process parameters successfully saved to:
{file_path}</translation>
    </message>
    <message>
        <source>错误：
{e}</source>
        <translation>Error：
{e}</translation>
    </message>
    <message>
        <source>标准模板已保存至：
{file_path}</source>
        <translation>Standard template successfully archived at:
{file_path}</translation>
    </message>
    <message>
        <source>已成功导入{imported_count}条工艺参数</source>
        <translation>Successfully imported {imported_count} process parameters</translation>
    </message>
    <message>
        <source>点焊安全点已重定位。{point_position_dict}</source>
        <translation>Spot welding safety point relocated.{point_position_dict}</translation>
    </message>
    <message>
        <source>当前状态不允许切换模式</source>
        <translation>System State Prohibits Mode Transition.</translation>
    </message>
    <message>
        <source>请选择焊机控制方式！</source>
        <translation>Select Welding Control Mode！</translation>
    </message>
    <message>
        <source>焊机延时请输入有效数字！</source>
        <translation>Enter Valid Numerical Value for Welder Timer Delay！</translation>
    </message>
    <message>
        <source>焊机控制设置已成功保存！</source>
        <translation>Welding Control Parameters Successfully Saved！</translation>
    </message>
    <message>
        <source>语言设置已成功保存！</source>
        <translation>Language Setting Successfully Saved！</translation>
    </message>
    <message>
        <source>机器人应用设置已成功保存！</source>
        <translation>Robot application settings successfully saved！</translation>
    </message>
    <message>
        <source>自定义百分比必须是0到100的整数！</source>
        <translation> Custom percentage must be an integer between 0 and 100.</translation>
    </message>
    <message>
        <source>碰撞设置已成功保存！</source>
        <translation>Collision settings is saved successfully!</translation>
    </message>
    <message>
        <source>保存碰撞设置时发生错误: {e}</source>
        <translation>Failed to save collision settings: {e}</translation>
    </message>
</context>
<context>
    <name>SafetyPoint</name>
    <message>
        <source>点焊安全点已重定位。位置: {}</source>
        <translation>Spot welding safety point has been repositioned. Location: {}</translation>
    </message>
    <message>
        <source>安全点定位成功</source>
        <translation>Safety Point Positioning Successfully Completed.</translation>
    </message>
</context>
<context>
    <name>Save</name>
    <message>
        <source>模板保存</source>
        <translation>Template saving</translation>
    </message>
    <message>
        <source>导入成功</source>
        <translation>Import Success</translation>
    </message>
    <message>
        <source>导入失败</source>
        <translation>Import Faill</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2128"/>
        <source>保存失败</source>
        <translation>Save faild</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2310"/>
        <source>另存为成功</source>
        <translation>Save as successfully</translation>
    </message>
</context>
<context>
    <name>SaveDialog</name>
    <message>
        <location filename="../logic/home_logic.py" line="2234"/>
        <source>程序另存为：</source>
        <translation>Process saved as</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2235"/>
        <source>请输入新的程序名称</source>
        <translation>Please input a new process name</translation>
    </message>
</context>
<context>
    <name>SaveError</name>
    <message>
        <location filename="../logic/home_logic.py" line="2225"/>
        <source>另存为失败</source>
        <translation>Save as failed</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="2226"/>
        <source>当前程序列表为空，没有内容可另存为</source>
        <translation>The current program list is empty. There is no content to save.</translation>
    </message>
</context>
<context>
    <name>Savefause</name>
    <message>
        <source>保存失败</source>
        <translation>Save faild</translation>
    </message>
</context>
<context>
    <name>Savesuccess</name>
    <message>
        <source>保存成功</source>
        <translation>Save successfully</translation>
    </message>
</context>
<context>
    <name>SequenceError</name>
    <message>
        <source>编辑失败</source>
        <translation>Edit Faild</translation>
    </message>
</context>
<context>
    <name>Start</name>
    <message>
        <location filename="../logic/home_logic.py" line="1864"/>
        <source>程序启动</source>
        <translation>Program start</translation>
    </message>
</context>
<context>
    <name>Status</name>
    <message>
        <source>未解决</source>
        <translation>Unsolved</translation>
    </message>
    <message>
        <source>已解决</source>
        <translation>Solved</translation>
    </message>
</context>
<context>
    <name>SwingInfo</name>
    <message>
        <location filename="../logic/home_logic.py" line="1765"/>
        <source>摆动: {swing_type} {frequency}Hz {amplitude}mm;</source>
        <translation>Wave: {swing_type} {frequency}Hz {amplitude}mm;</translation>
    </message>
</context>
<context>
    <name>SwingInfoText</name>
    <message>
        <source>{swing_type}--&gt;频率: {frequency}Hz; 幅度: {amplitude}mm; 停留: {left_stop}ms\{right_stop}ms</source>
        <translation>{swing_type}--&gt;Frequency: {frequency}Hz; Amplitude: {amplitude}mm; Dwell: {left_stop}ms\{right_stop}ms</translation>
    </message>
    <message>
        <source>摆动：{swing_type}--&gt;频率: {frequency}Hz; 幅度: {amplitude}mm; 停留: {left_stop}ms\{right_stop}ms</source>
        <translation>Wave：{swing_type}--&gt;Frequency: {frequency}Hz; Amplitude: {amplitude}mm; Dwell: {left_stop}ms\{right_stop}ms</translation>
    </message>
</context>
<context>
    <name>SwingParameters</name>
    <message>
        <source>摆动: {0} {1:.1f}HZ {2:.1f}mm;</source>
        <translation>Wave: {0} {1:.1f}HZ {2:.1f}mm;</translation>
    </message>
</context>
<context>
    <name>SwingType</name>
    <message>
        <location filename="../logic/home_logic.py" line="1749"/>
        <source>三角波摆动</source>
        <translation>Triangle Wave Weaving</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1750"/>
        <source>垂直L型三角波</source>
        <translation>Vertical L-Triangle Weave</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1751"/>
        <source>圆型（顺时针）</source>
        <translation>Circular (Clockwise)</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1752"/>
        <source>圆型（逆时针）</source>
        <translation>(Counter-Clockwise)</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1753"/>
        <source>正弦波摆动</source>
        <translation>Sine Wave Weaving</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1754"/>
        <source>垂直L型正弦波</source>
        <translation>Vertical L-Sine Weave</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1759"/>
        <source>立焊三角形摆动</source>
        <translation>Vertical-Up Triangle Weave</translation>
    </message>
</context>
<context>
    <name>SwingTypes</name>
    <message>
        <source>三角波摆动</source>
        <translation>Triangle Wave Weaving</translation>
    </message>
    <message>
        <source>垂直L型三角波</source>
        <translation>Vertical L-Triangle Weave</translation>
    </message>
    <message>
        <source>圆型（顺时针）</source>
        <translation>Clockwise Circular Weave</translation>
    </message>
    <message>
        <source>圆型（逆时针）</source>
        <translation>Counter-Clockwise Circular Weave</translation>
    </message>
    <message>
        <source>正弦波摆动</source>
        <translation>Sine Wave Weaving</translation>
    </message>
    <message>
        <source>垂直L型正弦波</source>
        <translation>Vertical L-Sine Weave</translation>
    </message>
    <message>
        <source>立焊三角形摆动</source>
        <translation>Vertical-Up Triangle Weave</translation>
    </message>
</context>
<context>
    <name>TableHeader</name>
    <message>
        <location filename="../logic/home_logic.py" line="1665"/>
        <source>序号</source>
        <translation>ID</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1666"/>
        <source>指令类型</source>
        <translation>Command Type</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1667"/>
        <source>点位信息</source>
        <translation>Position Information</translation>
    </message>
    <message>
        <location filename="../logic/home_logic.py" line="1670"/>
        <source>速度</source>
        <translation>Speed</translation>
    </message>
</context>
<context>
    <name>Warning</name>
    <message>
        <source>提示</source>
        <translation>Notification</translation>
    </message>
    <message>
        <source>警告</source>
        <translation>Warning</translation>
    </message>
</context>
<context>
    <name>WeldParams</name>
    <message>
        <source>电流: {current}A; 电压: {voltage}V; 速度: {speed}mm/s</source>
        <translation>Current: {current}A; Voltage: {voltage}V; Speed: {speed}mm/s</translation>
    </message>
</context>
<context>
    <name>data</name>
    <message>
        <source>点位更新</source>
        <translation>Position updated</translation>
    </message>
</context>
<context>
    <name>error</name>
    <message>
        <source>错误</source>
        <translation>Error</translation>
    </message>
    <message>
        <source>输入错误</source>
        <translation>Input error</translation>
    </message>
    <message>
        <source>保存错误</source>
        <translation>Save error</translation>
    </message>
</context>
<context>
    <name>expert</name>
    <message>
        <source>导出成功</source>
        <translation>Export success</translation>
    </message>
    <message>
        <source>导出失败</source>
        <translation>Export Faill</translation>
    </message>
</context>
<context>
    <name>point</name>
    <message>
        <source>安全点定位成功</source>
        <translation>Safety Point Positioning Successfully Completed.</translation>
    </message>
</context>
<context>
    <name>success</name>
    <message>
        <source>成功</source>
        <translation>Success</translation>
    </message>
</context>
<context>
    <name>widget</name>
    <message>
        <source>智焊</source>
        <translation>Intellect Welding</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:20pt; font-weight:700;&quot;&gt;智能焊接&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; font-size:20pt; font-weight:700;&quot;&gt;Smart Welding&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
    <message>
        <source>   主  页   </source>
        <translation>Home</translation>
    </message>
    <message>
        <source>工艺管理</source>
        <translation>Process</translation>
    </message>
    <message>
        <source>手动控制</source>
        <translation>Manual</translation>
    </message>
    <message>
        <source>通用设置</source>
        <translation>Settings</translation>
    </message>
    <message>
        <source>报警管理</source>
        <translation>Alarm</translation>
    </message>
    <message>
        <source>已连接</source>
        <translation>Connected</translation>
    </message>
    <message>
        <source>自动</source>
        <translation>Auto</translation>
    </message>
    <message>
        <source>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; color:#ffffff;&quot;&gt;时间&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</source>
        <translation>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p align=&quot;center&quot;&gt;&lt;span style=&quot; color:#ffffff;&quot;&gt;time&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</translation>
    </message>
</context>
<context>
    <name>断焊</name>
    <message>
        <source> SegWeld: Execute {0:.1f}mm Non-exec {1:.1f}mm;</source>
        <translation>SegWeld: Execute {0:.1f}mm Non-exec {1:.1f}mm;</translation>
    </message>
</context>
</TS>
