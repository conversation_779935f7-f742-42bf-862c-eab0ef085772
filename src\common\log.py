#!/usr/bin/env python3
# -*- coding: utf-8 -*-
'''
usage: log
'''
import functools
import os
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
import platform
from loguru import logger


def get_log_directory():
    """
    根据操作系统返回合适的日志存储目录
    """
    system = platform.system()
    if system == 'Windows':
        return Path(os.path.join(os.environ['PUBLIC'], 'Documents', 'PalletizerLogs'))
    elif system == 'Linux':
        return Path(os.path.expanduser('~')) / 'PalletizerLogs'
    elif system == 'Darwin':  # macOS
        return Path(os.path.expanduser('~')) / 'Library' / 'Logs' / 'Palletizer'
    else:
        raise NotImplementedError(f"Unsupported OS: {system}")


class Logger:
    # 创建固定大小的线程池（例如 2 个线程）
    _executor = ThreadPoolExecutor(max_workers=2)
    # 获取公共文档目录路径
    # log_dir = Path(os.path.join(os.environ['PUBLIC'], 'Documents', 'WeldLinkLogs'))
    # log_dir.mkdir(exist_ok=True)  # 确保目录存在
    log_dir = get_log_directory()
    log_dir.mkdir(parents=True, exist_ok=True)

    log_file_name = str(log_dir / "robot_log_{time:YYYYMMDD}.log")  # 修改为绝对路径
    logger.add(
        log_file_name,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {thread.name} | {message}\n",  # 添加线程名
        level="INFO",
        rotation="1 day",
        retention="30 days"
    )

    @classmethod
    def _log_in_thread(cls, level, message):
        """实际的日志记录逻辑"""
        if level == "INFO":
            logger.info(message)
        elif level == "WARNING":
            logger.warning(message)
        elif level == "ERROR":
            logger.error(message)
        elif level == "DEBUG":
            logger.debug(message)

    @classmethod
    def log_async(cls, level, message):
        """提交任务到线程池"""
        cls._executor.submit(functools.partial(cls._log_in_thread, level, message))

    # 替换原有方法为异步版本
    @classmethod
    def debug(cls, message):
        cls.log_async("DEBUG", message)

    @classmethod
    def info(cls, message):
        cls.log_async("INFO", message)

    @classmethod
    def warning(cls, message):
        cls.log_async("WARNING", message)

    @classmethod
    def error(cls, message):
        cls.log_async("ERROR", message)
