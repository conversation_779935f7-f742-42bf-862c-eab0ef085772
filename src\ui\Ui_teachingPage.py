# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'teachingPage.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, Q<PERSON>olor, Q<PERSON><PERSON>al<PERSON><PERSON><PERSON>, Q<PERSON>ursor,
    <PERSON><PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QHBoxLayout, QPushButton,
    QSizePolicy, QSpacerItem, QVBoxLayout, QWidget)

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(842, 562)
        self.verticalLayout = QVBoxLayout(Form)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.frame = QFrame(Form)
        self.frame.setObjectName(u"frame")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame.sizePolicy().hasHeightForWidth())
        self.frame.setSizePolicy(sizePolicy)
        self.frame.setMinimumSize(QSize(0, 50))
        self.frame.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;")
        self.frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame.setFrameShadow(QFrame.Shadow.Raised)
        self.horizontalLayout = QHBoxLayout(self.frame)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)

        self.fetchingButton = QPushButton(self.frame)
        self.fetchingButton.setObjectName(u"fetchingButton")
        sizePolicy.setHeightForWidth(self.fetchingButton.sizePolicy().hasHeightForWidth())
        self.fetchingButton.setSizePolicy(sizePolicy)
        self.fetchingButton.setMinimumSize(QSize(150, 30))
        font = QFont()
        font.setPointSize(14)
        self.fetchingButton.setFont(font)
        self.fetchingButton.setStyleSheet(u"background: radial-gradient( 1% 0% at 0% 0%, #4D8ED1 0%, #07509F 100%), #0866B6;\n"
"box-shadow: 2px 0px 0px 0px #0A53A1;\n"
"border-radius: 15px 15px 15px 15px;")

        self.horizontalLayout.addWidget(self.fetchingButton)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_3)

        self.ManualButton = QPushButton(self.frame)
        self.ManualButton.setObjectName(u"ManualButton")
        sizePolicy.setHeightForWidth(self.ManualButton.sizePolicy().hasHeightForWidth())
        self.ManualButton.setSizePolicy(sizePolicy)
        self.ManualButton.setMinimumSize(QSize(150, 30))
        self.ManualButton.setFont(font)
        self.ManualButton.setStyleSheet(u"background: radial-gradient(100% 0% at 0% 0%, #4D8ED1 0%, #07509F 100%), #0866B6;\n"
"box-shadow: 2px 0px 0px 0px #0A53A1;\n"
"border-radius: 15px 15px 15px 15px;")

        self.horizontalLayout.addWidget(self.ManualButton)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_2)


        self.verticalLayout.addWidget(self.frame)

        self.widget_qiehuan = QWidget(Form)
        self.widget_qiehuan.setObjectName(u"widget_qiehuan")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.widget_qiehuan.sizePolicy().hasHeightForWidth())
        self.widget_qiehuan.setSizePolicy(sizePolicy1)

        self.verticalLayout.addWidget(self.widget_qiehuan)

        self.verticalLayout.setStretch(0, 1)
        self.verticalLayout.setStretch(1, 10)

        self.retranslateUi(Form)

        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.fetchingButton.setText(QCoreApplication.translate("Form", u"\u6293\u53d6/\u653e\u7f6e", None))
        self.ManualButton.setText(QCoreApplication.translate("Form", u"\u624b\u52a8\u64cd\u4f5c", None))
    # retranslateUi

