# -*- mode: python ; coding: utf-8 -*-
# 打包命令  pyinstaller main.spec
# pyinstaller --clean main.spec

a = Analysis(
    ['main.py'],
    pathex=['src', '.'], # 确保找到所有源文件
    binaries=[],
    datas=[('src/resources', 'src/resources'),('src/translations', 'src/translations')], # 这会把src/resources下的所有内容复制到dist/WeldRobotApp/src/resources
    hiddenimports=[
        'src.common.log',
        'src.core.robot_tcp_controller',
        'src.logic.alarm_logic',
        'src.logic.home_logic',
        'src.logic.main_window',
        'src.logic.manual_logic',
        'src.logic.process_logic',
        'src.logic.settings_logic',
        # 可能还需要添加PySide6的特定隐藏导入，例如：
        # 'PySide6.QtNetwork', # 如果遇到运行时错误，可能需要添加
        # 'PySide6.QtSql',     # 如果使用了PySide6的数据库模块
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

# EXE 定义的是可执行文件本身，它将被 COLLECT 收集到最终的输出文件夹中
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas, # 这些datas会先被打包到exe或pyz中
    [],
    name='WeldRobotApp.exe',  # 是文件名称，包含.exe后缀
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False, # <--- 强烈建议将 upx 设置为 False，尤其是在 --onedir 模式下，避免与PySide6的DLL文件冲突或导致加载变慢。
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False, # <--- 保持 False，因为是GUI应用
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='src/resources/app_icon.ico',
    # onefile=False, # <--- COLLECT 会决定最终的打包模式
)

# COLLECT 将上述的 exe 和所有其他的二进制文件、数据文件收集到一个文件夹中。
coll = COLLECT(
    exe,           # 您的主可执行文件
    a.binaries,    # 分析阶段发现的所有二进制依赖
    a.datas,       # 您通过datas参数指定的所有数据文件 (例如 'src/resources')
    strip=False,
    upx=False,
    name='WeldRobotApp' # <--- 这个 'name' 定义了最终输出文件夹的名称
)