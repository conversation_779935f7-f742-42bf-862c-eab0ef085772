# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'run.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, Q<PERSON>ursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QGridLayout, QHBoxLayout,
    QLabel, QLayout, QPushButton, QSizePolicy,
    QSpacerItem, QVBoxLayout, QWidget)
from ..resources.images_rc import *

class Ui_HomeWidget(object):
    def setupUi(self, HomeWidget):
        if not HomeWidget.objectName():
            HomeWidget.setObjectName(u"HomeWidget")
        HomeWidget.resize(858, 509)
        HomeWidget.setStyleSheet(u"QWidget {\n"
"    background-color: #e8f0ff;\n"
"    font-family: \"Microsoft YaHei\", Arial, sans-serif;\n"
"}\n"
"\n"
"QFrame {\n"
"    background-color: white;\n"
"    border-radius: 12px;\n"
"    border: 1px solid #d0d7de;\n"
"}\n"
"\n"
"QPushButton {\n"
"    border: none;\n"
"    border-radius: 8px;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"    color: white;\n"
"}\n"
"\n"
"QLabel {\n"
"    color: #2d3748;\n"
"    font-size: 12px;\n"
"}\n"
"\n"
".status-ready {\n"
"    background-color: #22c55e;\n"
"    color: white;\n"
"    border-radius: 12px;\n"
"    padding: 4px 12px;\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
".status-maintenance {\n"
"    background-color: #f59e0b;\n"
"    color: white;\n"
"    border-radius: 12px;\n"
"    padding: 4px 12px;\n"
"    font-size: 11px;\n"
"    font-weight: bold;\n"
"}\n"
"\n"
".count-large {\n"
"    font-size: 24px;\n"
"    font-weight: bold;\n"
"    color: #3b82f6;\n"
"}\n"
"\n"
".count-medium {\n"
"    font-size: 18px;\n"
"    font"
                        "-weight: bold;\n"
"    color: #3b82f6;\n"
"}\n"
"\n"
".count-small {\n"
"    font-size: 14px;\n"
"    color: #6b7280;\n"
"}\n"
"\n"
".btn-ready {\n"
"    background-color: #22c55e;\n"
"    min-height: 32px;\n"
"}\n"
"\n"
".btn-maintenance {\n"
"    background-color: #f59e0b;\n"
"    min-height: 32px;\n"
"}\n"
"\n"
".btn-reset {\n"
"    background-color: #3b82f6;\n"
"    min-height: 32px;\n"
"}\n"
"\n"
".btn-start {\n"
"    background-color: #3b82f6;\n"
"    min-height: 36px;\n"
"}\n"
"\n"
".btn-stop {\n"
"    background-color: #ef4444;\n"
"    min-height: 36px;\n"
"}\n"
"\n"
".btn-pause {\n"
"    background-color: #f59e0b;\n"
"    min-height: 36px;\n"
"}\n"
"\n"
".btn-custom {\n"
"    background-color: #06b6d4;\n"
"    min-height: 36px;\n"
"}\n"
"\n"
".btn-system-reset {\n"
"    background-color: #6b7280;\n"
"    min-height: 36px;\n"
"}\n"
"\n"
".toggle-on {\n"
"    background-color: #22c55e;\n"
"    border-radius: 12px;\n"
"    min-height: 24px;\n"
"    min-width: 48px;\n"
"}\n"
"\n"
".toggle-off {\n"
"    ba"
                        "ckground-color: #d1d5db;\n"
"    border-radius: 12px;\n"
"    min-height: 24px;\n"
"    min-width: 48px;\n"
"}\n"
"\n"
".robot-workspace {\n"
"    border: 2px dashed #9ca3af;\n"
"    background-color: #f9fafb;\n"
"    border-radius: 8px;\n"
"}")
        self.mainVerticalLayout = QVBoxLayout(HomeWidget)
        self.mainVerticalLayout.setSpacing(12)
        self.mainVerticalLayout.setObjectName(u"mainVerticalLayout")
        self.mainVerticalLayout.setContentsMargins(12, 12, 12, 12)
        self.topControlLayout = QHBoxLayout()
        self.topControlLayout.setSpacing(12)
        self.topControlLayout.setObjectName(u"topControlLayout")
        self.topControlLayout.setSizeConstraint(QLayout.SizeConstraint.SetDefaultConstraint)
        self.leftConveyorFrame = QFrame(HomeWidget)
        self.leftConveyorFrame.setObjectName(u"leftConveyorFrame")
        self.leftConveyorFrame.setMinimumSize(QSize(260, 230))
        self.leftConveyorFrame.setMaximumSize(QSize(260, 300))
        self.leftConveyorMainLayout = QVBoxLayout(self.leftConveyorFrame)
        self.leftConveyorMainLayout.setSpacing(8)
        self.leftConveyorMainLayout.setObjectName(u"leftConveyorMainLayout")
        self.leftConveyorMainLayout.setContentsMargins(12, 12, 12, 12)
        self.leftTitleStatusLayout = QHBoxLayout()
        self.leftTitleStatusLayout.setObjectName(u"leftTitleStatusLayout")
        self.leftConveyorTitleLabel = QLabel(self.leftConveyorFrame)
        self.leftConveyorTitleLabel.setObjectName(u"leftConveyorTitleLabel")
        self.leftConveyorTitleLabel.setStyleSheet(u"font-size: 15px; font-weight: bold; color: #1f2937;")

        self.leftTitleStatusLayout.addWidget(self.leftConveyorTitleLabel)

        self.leftTitleSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.leftTitleStatusLayout.addItem(self.leftTitleSpacer)

        self.leftStatusButton = QPushButton(self.leftConveyorFrame)
        self.leftStatusButton.setObjectName(u"leftStatusButton")
        self.leftStatusButton.setStyleSheet(u"background-color: #22c55e; color: white; border-radius: 13px; padding: 4px 12px; font-size: 11px; font-weight: bold; min-width: 40px;")

        self.leftTitleStatusLayout.addWidget(self.leftStatusButton)


        self.leftConveyorMainLayout.addLayout(self.leftTitleStatusLayout)

        self.leftCurrentStatsLayout = QHBoxLayout()
        self.leftCurrentStatsLayout.setObjectName(u"leftCurrentStatsLayout")
        self.leftCurrentLayerLayout = QVBoxLayout()
        self.leftCurrentLayerLayout.setObjectName(u"leftCurrentLayerLayout")
        self.leftCurrentLayerLabel = QLabel(self.leftConveyorFrame)
        self.leftCurrentLayerLabel.setObjectName(u"leftCurrentLayerLabel")
        self.leftCurrentLayerLabel.setStyleSheet(u"font-size: 12px; color: #6b7280;\n"
"border:0px;")

        self.leftCurrentLayerLayout.addWidget(self.leftCurrentLayerLabel)

        self.leftCurrentLayerValueLabel = QLabel(self.leftConveyorFrame)
        self.leftCurrentLayerValueLabel.setObjectName(u"leftCurrentLayerValueLabel")
        self.leftCurrentLayerValueLabel.setStyleSheet(u"font-size: 20px; font-weight: bold; color: #3b82f6;\n"
"border:0px;")

        self.leftCurrentLayerLayout.addWidget(self.leftCurrentLayerValueLabel)


        self.leftCurrentStatsLayout.addLayout(self.leftCurrentLayerLayout)

        self.leftCurrentBoxLayout = QVBoxLayout()
        self.leftCurrentBoxLayout.setObjectName(u"leftCurrentBoxLayout")
        self.leftCurrentBoxLabel = QLabel(self.leftConveyorFrame)
        self.leftCurrentBoxLabel.setObjectName(u"leftCurrentBoxLabel")
        self.leftCurrentBoxLabel.setStyleSheet(u"font-size: 12px; color: #6b7280;\n"
"border:0px;")

        self.leftCurrentBoxLayout.addWidget(self.leftCurrentBoxLabel)

        self.leftCurrentBoxValueLabel = QLabel(self.leftConveyorFrame)
        self.leftCurrentBoxValueLabel.setObjectName(u"leftCurrentBoxValueLabel")
        self.leftCurrentBoxValueLabel.setStyleSheet(u"font-size: 20px; font-weight: bold; color: #3b82f6;\n"
"border:0px;")

        self.leftCurrentBoxLayout.addWidget(self.leftCurrentBoxValueLabel)


        self.leftCurrentStatsLayout.addLayout(self.leftCurrentBoxLayout)


        self.leftConveyorMainLayout.addLayout(self.leftCurrentStatsLayout)

        self.leftTotalStatsLayout = QHBoxLayout()
        self.leftTotalStatsLayout.setObjectName(u"leftTotalStatsLayout")
        self.leftTotalBoxLayout = QVBoxLayout()
        self.leftTotalBoxLayout.setObjectName(u"leftTotalBoxLayout")
        self.leftTotalBoxLabel = QLabel(self.leftConveyorFrame)
        self.leftTotalBoxLabel.setObjectName(u"leftTotalBoxLabel")
        self.leftTotalBoxLabel.setStyleSheet(u"font-size: 12px; color: #6b7280;\n"
"border:0px;")

        self.leftTotalBoxLayout.addWidget(self.leftTotalBoxLabel)

        self.leftTotalBoxValueLabel = QLabel(self.leftConveyorFrame)
        self.leftTotalBoxValueLabel.setObjectName(u"leftTotalBoxValueLabel")
        self.leftTotalBoxValueLabel.setStyleSheet(u"font-size: 14px; font-weight: bold; color: #3b82f6;\n"
"border:0px;")

        self.leftTotalBoxLayout.addWidget(self.leftTotalBoxValueLabel)


        self.leftTotalStatsLayout.addLayout(self.leftTotalBoxLayout)

        self.leftClearanceLayout = QVBoxLayout()
        self.leftClearanceLayout.setObjectName(u"leftClearanceLayout")
        self.leftClearanceLabel = QLabel(self.leftConveyorFrame)
        self.leftClearanceLabel.setObjectName(u"leftClearanceLabel")
        self.leftClearanceLabel.setStyleSheet(u"font-size: 12px; color: #6b7280;\n"
"border:0px;")

        self.leftClearanceLayout.addWidget(self.leftClearanceLabel)

        self.leftClearanceToggle = QPushButton(self.leftConveyorFrame)
        self.leftClearanceToggle.setObjectName(u"leftClearanceToggle")
        self.leftClearanceToggle.setMinimumSize(QSize(48, 24))
        self.leftClearanceToggle.setStyleSheet(u"background-color: #d1d5db; border-radius: 12px; min-height: 24px; min-width: 48px;\n"
"background:url(:/run/run/OFF.png);\n"
"background-repeat: no-repeat;")

        self.leftClearanceLayout.addWidget(self.leftClearanceToggle)


        self.leftTotalStatsLayout.addLayout(self.leftClearanceLayout)


        self.leftConveyorMainLayout.addLayout(self.leftTotalStatsLayout)

        self.leftControlButtonsLayout = QHBoxLayout()
        self.leftControlButtonsLayout.setSpacing(6)
        self.leftControlButtonsLayout.setObjectName(u"leftControlButtonsLayout")
        self.leftReadyButton = QPushButton(self.leftConveyorFrame)
        self.leftReadyButton.setObjectName(u"leftReadyButton")
        self.leftReadyButton.setStyleSheet(u"background-color: #22c55e; min-height: 28px; font-size: 13px;")

        self.leftControlButtonsLayout.addWidget(self.leftReadyButton)

        self.leftMaintenanceButton = QPushButton(self.leftConveyorFrame)
        self.leftMaintenanceButton.setObjectName(u"leftMaintenanceButton")
        self.leftMaintenanceButton.setStyleSheet(u"background-color: #f59e0b; min-height: 28px; font-size: 13px;")

        self.leftControlButtonsLayout.addWidget(self.leftMaintenanceButton)

        self.leftResetButton = QPushButton(self.leftConveyorFrame)
        self.leftResetButton.setObjectName(u"leftResetButton")
        self.leftResetButton.setStyleSheet(u"background-color: #3b82f6; min-height: 28px; font-size: 13px;")

        self.leftControlButtonsLayout.addWidget(self.leftResetButton)


        self.leftConveyorMainLayout.addLayout(self.leftControlButtonsLayout)


        self.topControlLayout.addWidget(self.leftConveyorFrame)

        self.controlCenterFrame = QFrame(HomeWidget)
        self.controlCenterFrame.setObjectName(u"controlCenterFrame")
        self.controlCenterFrame.setMinimumSize(QSize(280, 230))
        self.controlCenterFrame.setMaximumSize(QSize(280, 300))
        self.controlCenterMainLayout = QVBoxLayout(self.controlCenterFrame)
        self.controlCenterMainLayout.setSpacing(8)
        self.controlCenterMainLayout.setObjectName(u"controlCenterMainLayout")
        self.controlCenterMainLayout.setContentsMargins(12, 12, 12, 12)
        self.controlCenterTitleLabel = QLabel(self.controlCenterFrame)
        self.controlCenterTitleLabel.setObjectName(u"controlCenterTitleLabel")
        self.controlCenterTitleLabel.setStyleSheet(u"font-size: 16px; font-weight: bold; color: #1f2937;")
        self.controlCenterTitleLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.controlCenterMainLayout.addWidget(self.controlCenterTitleLabel)

        self.mainControlButtonsLayout = QGridLayout()
        self.mainControlButtonsLayout.setSpacing(6)
        self.mainControlButtonsLayout.setObjectName(u"mainControlButtonsLayout")
        self.startButton = QPushButton(self.controlCenterFrame)
        self.startButton.setObjectName(u"startButton")
        self.startButton.setStyleSheet(u"min-height: 32px; font-size: 12px;\n"
"background-color: #3b82f6;")

        self.mainControlButtonsLayout.addWidget(self.startButton, 0, 0, 1, 1)

        self.stopButton = QPushButton(self.controlCenterFrame)
        self.stopButton.setObjectName(u"stopButton")
        self.stopButton.setStyleSheet(u"background-color: #ef4444; min-height: 32px; font-size: 12px;")

        self.mainControlButtonsLayout.addWidget(self.stopButton, 0, 1, 1, 1)

        self.pauseButton = QPushButton(self.controlCenterFrame)
        self.pauseButton.setObjectName(u"pauseButton")
        self.pauseButton.setStyleSheet(u"background-color: #f59e0b; min-height: 32px; font-size: 12px;")

        self.mainControlButtonsLayout.addWidget(self.pauseButton, 0, 2, 1, 1)

        self.customAutoButton = QPushButton(self.controlCenterFrame)
        self.customAutoButton.setObjectName(u"customAutoButton")
        self.customAutoButton.setStyleSheet(u"background-color: #06b6d4; min-height: 32px; font-size: 12px;")

        self.mainControlButtonsLayout.addWidget(self.customAutoButton, 1, 0, 1, 2)

        self.systemResetButton = QPushButton(self.controlCenterFrame)
        self.systemResetButton.setObjectName(u"systemResetButton")
        self.systemResetButton.setStyleSheet(u"background-color: #6b7280; min-height: 32px; font-size: 12px;")

        self.mainControlButtonsLayout.addWidget(self.systemResetButton, 1, 2, 1, 1)


        self.controlCenterMainLayout.addLayout(self.mainControlButtonsLayout)

        self.parametersDisplayLayout = QGridLayout()
        self.parametersDisplayLayout.setSpacing(8)
        self.parametersDisplayLayout.setObjectName(u"parametersDisplayLayout")
        self.leftSpeedLabel = QLabel(self.controlCenterFrame)
        self.leftSpeedLabel.setObjectName(u"leftSpeedLabel")
        self.leftSpeedLabel.setStyleSheet(u"font-size: 13px; font-weight: bold; color: #374151;\n"
"border:0px;")

        self.parametersDisplayLayout.addWidget(self.leftSpeedLabel, 0, 0, 1, 1)

        self.rightSpeedLabel = QLabel(self.controlCenterFrame)
        self.rightSpeedLabel.setObjectName(u"rightSpeedLabel")
        self.rightSpeedLabel.setStyleSheet(u"font-size: 13px; font-weight: bold; color: #374151;\n"
"border:0px;")

        self.parametersDisplayLayout.addWidget(self.rightSpeedLabel, 0, 1, 1, 1)

        self.totalSpeedLabel = QLabel(self.controlCenterFrame)
        self.totalSpeedLabel.setObjectName(u"totalSpeedLabel")
        self.totalSpeedLabel.setStyleSheet(u"font-size: 13px; font-weight: bold; color: #374151;\n"
"border:0px;")

        self.parametersDisplayLayout.addWidget(self.totalSpeedLabel, 0, 2, 1, 1)

        self.leftTotalLabel = QLabel(self.controlCenterFrame)
        self.leftTotalLabel.setObjectName(u"leftTotalLabel")
        self.leftTotalLabel.setStyleSheet(u"font-size: 13px; font-weight: bold; color: #374151;\n"
"border:0px;")

        self.parametersDisplayLayout.addWidget(self.leftTotalLabel, 1, 0, 1, 1)

        self.rightTotalLabel = QLabel(self.controlCenterFrame)
        self.rightTotalLabel.setObjectName(u"rightTotalLabel")
        self.rightTotalLabel.setStyleSheet(u"font-size: 13px; font-weight: bold; color: #374151;\n"
"border:0px;")

        self.parametersDisplayLayout.addWidget(self.rightTotalLabel, 1, 1, 1, 1)

        self.totalBoxCountLabel = QLabel(self.controlCenterFrame)
        self.totalBoxCountLabel.setObjectName(u"totalBoxCountLabel")
        self.totalBoxCountLabel.setStyleSheet(u"font-size: 13px; font-weight: bold; color: #374151;\n"
"border:0px;")

        self.parametersDisplayLayout.addWidget(self.totalBoxCountLabel, 1, 2, 1, 1)


        self.controlCenterMainLayout.addLayout(self.parametersDisplayLayout)


        self.topControlLayout.addWidget(self.controlCenterFrame)

        self.rightConveyorFrame = QFrame(HomeWidget)
        self.rightConveyorFrame.setObjectName(u"rightConveyorFrame")
        self.rightConveyorFrame.setMinimumSize(QSize(260, 230))
        self.rightConveyorFrame.setMaximumSize(QSize(260, 300))
        self.rightConveyorMainLayout = QVBoxLayout(self.rightConveyorFrame)
        self.rightConveyorMainLayout.setSpacing(8)
        self.rightConveyorMainLayout.setObjectName(u"rightConveyorMainLayout")
        self.rightConveyorMainLayout.setContentsMargins(12, 12, 12, 12)
        self.rightTitleStatusLayout = QHBoxLayout()
        self.rightTitleStatusLayout.setObjectName(u"rightTitleStatusLayout")
        self.rightConveyorTitleLabel = QLabel(self.rightConveyorFrame)
        self.rightConveyorTitleLabel.setObjectName(u"rightConveyorTitleLabel")
        self.rightConveyorTitleLabel.setStyleSheet(u"font-size: 15px; font-weight: bold; color: #1f2937;")

        self.rightTitleStatusLayout.addWidget(self.rightConveyorTitleLabel)

        self.rightTitleSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.rightTitleStatusLayout.addItem(self.rightTitleSpacer)

        self.rightStatusButton = QPushButton(self.rightConveyorFrame)
        self.rightStatusButton.setObjectName(u"rightStatusButton")
        self.rightStatusButton.setStyleSheet(u"background-color: #f59e0b; color: white; border-radius: 13px; padding: 4px 12px; font-size: 11px; font-weight: bold; min-width: 40px;")

        self.rightTitleStatusLayout.addWidget(self.rightStatusButton)


        self.rightConveyorMainLayout.addLayout(self.rightTitleStatusLayout)

        self.rightCurrentStatsLayout = QHBoxLayout()
        self.rightCurrentStatsLayout.setObjectName(u"rightCurrentStatsLayout")
        self.rightCurrentLayerLayout = QVBoxLayout()
        self.rightCurrentLayerLayout.setObjectName(u"rightCurrentLayerLayout")
        self.rightCurrentLayerLabel = QLabel(self.rightConveyorFrame)
        self.rightCurrentLayerLabel.setObjectName(u"rightCurrentLayerLabel")
        self.rightCurrentLayerLabel.setStyleSheet(u"font-size: 12px; color: #6b7280;\n"
"border:0px;")

        self.rightCurrentLayerLayout.addWidget(self.rightCurrentLayerLabel)

        self.rightCurrentLayerValueLabel = QLabel(self.rightConveyorFrame)
        self.rightCurrentLayerValueLabel.setObjectName(u"rightCurrentLayerValueLabel")
        self.rightCurrentLayerValueLabel.setStyleSheet(u"font-size: 20px; font-weight: bold; color: #3b82f6;\n"
"border:0px;")

        self.rightCurrentLayerLayout.addWidget(self.rightCurrentLayerValueLabel)


        self.rightCurrentStatsLayout.addLayout(self.rightCurrentLayerLayout)

        self.rightCurrentBoxLayout = QVBoxLayout()
        self.rightCurrentBoxLayout.setObjectName(u"rightCurrentBoxLayout")
        self.rightCurrentBoxLabel = QLabel(self.rightConveyorFrame)
        self.rightCurrentBoxLabel.setObjectName(u"rightCurrentBoxLabel")
        self.rightCurrentBoxLabel.setStyleSheet(u"font-size: 12px; color: #6b7280;\n"
"border:0px;")

        self.rightCurrentBoxLayout.addWidget(self.rightCurrentBoxLabel)

        self.rightCurrentBoxValueLabel = QLabel(self.rightConveyorFrame)
        self.rightCurrentBoxValueLabel.setObjectName(u"rightCurrentBoxValueLabel")
        self.rightCurrentBoxValueLabel.setStyleSheet(u"font-size: 20px; font-weight: bold; color: #3b82f6;\n"
"border:0px;")

        self.rightCurrentBoxLayout.addWidget(self.rightCurrentBoxValueLabel)


        self.rightCurrentStatsLayout.addLayout(self.rightCurrentBoxLayout)


        self.rightConveyorMainLayout.addLayout(self.rightCurrentStatsLayout)

        self.rightTotalStatsLayout = QHBoxLayout()
        self.rightTotalStatsLayout.setObjectName(u"rightTotalStatsLayout")
        self.rightTotalBoxLayout = QVBoxLayout()
        self.rightTotalBoxLayout.setObjectName(u"rightTotalBoxLayout")
        self.rightTotalBoxLabel = QLabel(self.rightConveyorFrame)
        self.rightTotalBoxLabel.setObjectName(u"rightTotalBoxLabel")
        self.rightTotalBoxLabel.setStyleSheet(u"font-size: 12px; color: #6b7280;\n"
"border:0px;")

        self.rightTotalBoxLayout.addWidget(self.rightTotalBoxLabel)

        self.rightTotalBoxValueLabel = QLabel(self.rightConveyorFrame)
        self.rightTotalBoxValueLabel.setObjectName(u"rightTotalBoxValueLabel")
        self.rightTotalBoxValueLabel.setStyleSheet(u"font-size: 14px; font-weight: bold; color: #3b82f6;\n"
"border:0px;")

        self.rightTotalBoxLayout.addWidget(self.rightTotalBoxValueLabel)


        self.rightTotalStatsLayout.addLayout(self.rightTotalBoxLayout)

        self.rightClearanceLayout = QVBoxLayout()
        self.rightClearanceLayout.setObjectName(u"rightClearanceLayout")
        self.rightClearanceLabel = QLabel(self.rightConveyorFrame)
        self.rightClearanceLabel.setObjectName(u"rightClearanceLabel")
        self.rightClearanceLabel.setStyleSheet(u"font-size: 12px; color: #6b7280;\n"
"border:0px;")

        self.rightClearanceLayout.addWidget(self.rightClearanceLabel)

        self.rightClearanceToggle = QPushButton(self.rightConveyorFrame)
        self.rightClearanceToggle.setObjectName(u"rightClearanceToggle")
        self.rightClearanceToggle.setStyleSheet(u"background-color: #22c55e; border-radius: 12px; min-height: 24px; min-width: 48px;\n"
"background:url(:/run/run/ON.png);\n"
"background-repeat: no-repeat;")

        self.rightClearanceLayout.addWidget(self.rightClearanceToggle)


        self.rightTotalStatsLayout.addLayout(self.rightClearanceLayout)


        self.rightConveyorMainLayout.addLayout(self.rightTotalStatsLayout)

        self.rightControlButtonsLayout = QHBoxLayout()
        self.rightControlButtonsLayout.setSpacing(6)
        self.rightControlButtonsLayout.setObjectName(u"rightControlButtonsLayout")
        self.rightReadyButton = QPushButton(self.rightConveyorFrame)
        self.rightReadyButton.setObjectName(u"rightReadyButton")
        self.rightReadyButton.setStyleSheet(u"background-color: #22c55e; min-height: 28px; font-size: 13px;")

        self.rightControlButtonsLayout.addWidget(self.rightReadyButton)

        self.rightMaintenanceButton = QPushButton(self.rightConveyorFrame)
        self.rightMaintenanceButton.setObjectName(u"rightMaintenanceButton")
        self.rightMaintenanceButton.setStyleSheet(u"background-color: #f59e0b; min-height: 28px; font-size: 13px;")

        self.rightControlButtonsLayout.addWidget(self.rightMaintenanceButton)

        self.rightResetButton = QPushButton(self.rightConveyorFrame)
        self.rightResetButton.setObjectName(u"rightResetButton")
        self.rightResetButton.setStyleSheet(u"background-color: #3b82f6; min-height: 28px; font-size: 13px;")

        self.rightControlButtonsLayout.addWidget(self.rightResetButton)


        self.rightConveyorMainLayout.addLayout(self.rightControlButtonsLayout)


        self.topControlLayout.addWidget(self.rightConveyorFrame)


        self.mainVerticalLayout.addLayout(self.topControlLayout)

        self.robotWorkspaceLayout = QHBoxLayout()
        self.robotWorkspaceLayout.setSpacing(12)
        self.robotWorkspaceLayout.setObjectName(u"robotWorkspaceLayout")
        self.leftRobotWorkspaceLabel = QLabel(HomeWidget)
        self.leftRobotWorkspaceLabel.setObjectName(u"leftRobotWorkspaceLabel")
        self.leftRobotWorkspaceLabel.setMinimumSize(QSize(260, 120))
        self.leftRobotWorkspaceLabel.setMaximumSize(QSize(260, 270))
        self.leftRobotWorkspaceLabel.setStyleSheet(u"border: 2px dashed #9ca3af; background-color: #f9fafb; border-radius: 8px; color: #6b7280; font-size: 12px;")
        self.leftRobotWorkspaceLabel.setScaledContents(True)
        self.leftRobotWorkspaceLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.robotWorkspaceLayout.addWidget(self.leftRobotWorkspaceLabel)

        self.centerRobotWorkspaceLabel = QLabel(HomeWidget)
        self.centerRobotWorkspaceLabel.setObjectName(u"centerRobotWorkspaceLabel")
        self.centerRobotWorkspaceLabel.setMinimumSize(QSize(280, 120))
        self.centerRobotWorkspaceLabel.setMaximumSize(QSize(280, 270))
        self.centerRobotWorkspaceLabel.setStyleSheet(u"background-color: white; border-radius: 8px; color: #6b7280; font-size: 12px; border: 1px solid #d1d5db;")
        self.centerRobotWorkspaceLabel.setScaledContents(True)
        self.centerRobotWorkspaceLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.robotWorkspaceLayout.addWidget(self.centerRobotWorkspaceLabel)

        self.rightRobotWorkspaceLabel = QLabel(HomeWidget)
        self.rightRobotWorkspaceLabel.setObjectName(u"rightRobotWorkspaceLabel")
        self.rightRobotWorkspaceLabel.setMinimumSize(QSize(260, 120))
        self.rightRobotWorkspaceLabel.setMaximumSize(QSize(260, 270))
        self.rightRobotWorkspaceLabel.setStyleSheet(u"border: 2px dashed #9ca3af; background-color: #f9fafb; border-radius: 8px; color: #6b7280; font-size: 12px;")
        self.rightRobotWorkspaceLabel.setScaledContents(True)
        self.rightRobotWorkspaceLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.robotWorkspaceLayout.addWidget(self.rightRobotWorkspaceLabel)


        self.mainVerticalLayout.addLayout(self.robotWorkspaceLayout)


        self.retranslateUi(HomeWidget)

        QMetaObject.connectSlotsByName(HomeWidget)
    # setupUi

    def retranslateUi(self, HomeWidget):
        HomeWidget.setWindowTitle(QCoreApplication.translate("HomeWidget", u"\u667a\u80fd\u5206\u62e3\u7cfb\u7edf - \u4e3b\u9875", None))
        self.leftConveyorTitleLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5de6\u579b\u76d8", None))
        self.leftStatusButton.setText(QCoreApplication.translate("HomeWidget", u"\u5c31\u7eea", None))
        self.leftCurrentLayerLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5f53\u524d\u5c42\u6570", None))
        self.leftCurrentLayerValueLabel.setText(QCoreApplication.translate("HomeWidget", u"1/12", None))
        self.leftCurrentBoxLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5f53\u524d\u5c42\u7bb1\u5b50\u6570", None))
        self.leftCurrentBoxValueLabel.setText(QCoreApplication.translate("HomeWidget", u"5", None))
        self.leftTotalBoxLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5f53\u524d\u603b\u7bb1\u5b50\u6570", None))
        self.leftTotalBoxValueLabel.setText(QCoreApplication.translate("HomeWidget", u"5/120", None))
        self.leftClearanceLabel.setText(QCoreApplication.translate("HomeWidget", u"\u53d6\u6d88\u7801\u579b", None))
        self.leftClearanceToggle.setText("")
        self.leftReadyButton.setText(QCoreApplication.translate("HomeWidget", u"\u5c31\u7eea", None))
        self.leftMaintenanceButton.setText(QCoreApplication.translate("HomeWidget", u"\u5f3a\u5236\u6ee1\u579b", None))
        self.leftResetButton.setText(QCoreApplication.translate("HomeWidget", u"\u91cd\u7f6e", None))
        self.controlCenterTitleLabel.setText(QCoreApplication.translate("HomeWidget", u"\u63a7\u5236\u4e2d\u5fc3", None))
        self.startButton.setText(QCoreApplication.translate("HomeWidget", u"\u542f\u52a8", None))
        self.stopButton.setText(QCoreApplication.translate("HomeWidget", u"\u505c\u6b62", None))
        self.pauseButton.setText(QCoreApplication.translate("HomeWidget", u"\u6682\u505c", None))
        self.customAutoButton.setText(QCoreApplication.translate("HomeWidget", u"\u81ea\u5b9a\u4e49\u542f\u52a8", None))
        self.systemResetButton.setText(QCoreApplication.translate("HomeWidget", u"\u590d\u4f4d", None))
        self.leftSpeedLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5de6\u579b: 10", None))
        self.rightSpeedLabel.setText(QCoreApplication.translate("HomeWidget", u"\u53f3\u579b: 10", None))
        self.totalSpeedLabel.setText(QCoreApplication.translate("HomeWidget", u"\u603b\u579b\u6570: 20", None))
        self.leftTotalLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5de6\u579b: 500", None))
        self.rightTotalLabel.setText(QCoreApplication.translate("HomeWidget", u"\u53f3\u579b: 500", None))
        self.totalBoxCountLabel.setText(QCoreApplication.translate("HomeWidget", u"\u603b\u7bb1\u6570: 1000", None))
        self.rightConveyorTitleLabel.setText(QCoreApplication.translate("HomeWidget", u"\u53f3\u579b\u76d8", None))
        self.rightStatusButton.setText(QCoreApplication.translate("HomeWidget", u"\u6ee1\u579b", None))
        self.rightCurrentLayerLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5f53\u524d\u5c42\u6570", None))
        self.rightCurrentLayerValueLabel.setText(QCoreApplication.translate("HomeWidget", u"12/12", None))
        self.rightCurrentBoxLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5f53\u524d\u5c42\u7bb1\u5b50\u6570", None))
        self.rightCurrentBoxValueLabel.setText(QCoreApplication.translate("HomeWidget", u"5", None))
        self.rightTotalBoxLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5f53\u524d\u603b\u7bb1\u5b50\u6570", None))
        self.rightTotalBoxValueLabel.setText(QCoreApplication.translate("HomeWidget", u"120/120", None))
        self.rightClearanceLabel.setText(QCoreApplication.translate("HomeWidget", u"\u53d6\u6d88\u7801\u579b", None))
        self.rightClearanceToggle.setText("")
        self.rightReadyButton.setText(QCoreApplication.translate("HomeWidget", u"\u5c31\u7eea", None))
        self.rightMaintenanceButton.setText(QCoreApplication.translate("HomeWidget", u"\u5f3a\u5236\u6ee1\u579b", None))
        self.rightResetButton.setText(QCoreApplication.translate("HomeWidget", u"\u91cd\u7f6e", None))
        self.leftRobotWorkspaceLabel.setText(QCoreApplication.translate("HomeWidget", u"\u5de6\u4fa7\u673a\u5668\u4eba\u5de5\u4f5c\u533a", None))
        self.centerRobotWorkspaceLabel.setText(QCoreApplication.translate("HomeWidget", u"\u4e2d\u592e\u673a\u5668\u4eba\u5de5\u4f5c\u533a", None))
        self.rightRobotWorkspaceLabel.setText(QCoreApplication.translate("HomeWidget", u"\u53f3\u4fa7\u673a\u5668\u4eba\u5de5\u4f5c\u533a", None))
    # retranslateUi

