from PySide6.QtWidgets import QWidget, QVBoxLayout, QMessageBox, QApplication
from PySide6.QtCore import Qt, Slot, Signal, QTimer, QCoreApplication
from PySide6.QtGui import QDoubleValidator
from ..ui.Ui_teachingPage import Ui_Form
from ..ui.Ui_teachingPage_fetching import Ui_Form_1
from ..ui.Ui_teachingPage_manual import Ui_Form_2
from ..common.sql_lite_tool import SqliteTool
from ..common.robot_status import RobotStatus
import logging
from loguru import logger
from ..core.cmd_tool import RobotCommand
from ..core.robot_tcp_controller import RobotTcpController
from ..core.palletizer_program import command_manager
from ..common import global_vars as gv
from ..core.robot_8083_socket import Robot8083Socket

fetching_point_ = ""
stop_sign = 0  # 六个轴做移动的时候需要停止位


class TeachingLogic(QWidget, Ui_Form):
    send_cmd_to_robot = Signal(str)  # 下发指令的信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)

        # 初始化基础组件
        self.db_tool = SqliteTool.get_instance()
        self.robot_status = RobotStatus()
        self.robot = RobotTcpController.get_instance()  # 提前初始化robot

        # 机器人模式状态
        self.current_robot_mode = None  # 0=自动, 1=手动, 2=拖动

        # 设置UI
        self.setupUi(self)
        self._init_ui_components()
        self._setup_connections()
        self.send_cmd_to_robot.connect(self.robot.send_command)

        # 当前操作的轴和方向
        self.current_axis = 0
        self.current_direction = -1  # 0-反转，负方向，1-正转，正方向

        # # 加载已保存的点位数据
        # self._load_saved_points()

        # 初始化默认状态
        self.fetchingButton.setChecked(True)
        self._switch_interface()

        # 确保初始样式正确应用
        QTimer.singleShot(100, self._update_button_styles)

        # 监听窗口尺寸变化
        self._setup_resize_handler()

        # 使用QTimer延迟加载数据库数据，避免影响界面响应
        QTimer.singleShot(500, self._delayed_load_points)

        # 连接8083端口数据信号以获取机器人模式
        try:
            robot_8083 = Robot8083Socket.get_instance()
            if robot_8083:
                robot_8083.data_signal.connect(self._update_robot_mode)
        except Exception as e:
            self.logger.warning(f"无法连接8083端口获取机器人模式: {e}")

    def _update_robot_mode(self, data):
        """更新机器人模式状态"""
        try:
            self.current_robot_mode = data.get("robot_mode", None)
            self.logger.debug(f"机器人模式更新: {self.current_robot_mode}")
        except Exception as e:
            self.logger.error(f"更新机器人模式失败: {e}")

    def _check_manual_mode(self):
        """检查机器人是否处于手动模式"""
        if self.current_robot_mode is None:
            QMessageBox.warning(self, "模式检查", "机器人未连接，无法进行操作")
            return False

        if self.current_robot_mode != 1:  # 1 = 手动模式
            mode_text = {0: "自动", 1: "手动", 2: "拖动"}.get(self.current_robot_mode, "未知")
            QMessageBox.warning(
                self,
                "模式限制",
                f"当前机器人处于{mode_text}模式，只有在手动模式下才能进行示教操作。\n\n请先将机器人切换到手动模式。"
            )
            return False

        return True

    # 新增方法：加载所有已保存的点位数据

    def _delayed_load_points(self):
        """延迟加载点位数据"""
        try:
            # 加载各个点位数据
            point_types = ['fetching', 'fetching_interim', 'put_interim', 'left_tray_zorn', 'right_tray_zorn']

            for point_type in point_types:
                try:
                    point_data = self._load_point(point_type)
                    if point_data:
                        self._update_point_data(point_type, point_data)
                    # 每加载一个点位后让UI有机会响应
                    QApplication.processEvents()
                except Exception as e:
                    self.logger.error(f"加载点位 {point_type} 时出错: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"延迟加载点位数据失败: {e}")
    def _update_point_data(self, point_type, point_data):
        """更新点位数据和UI"""
        try:
            if point_type == 'fetching':
                self.fetching_point_data = point_data
                global fetching_point_
                fetching_point_ = RobotCommand.get_command_str('MoveL', point_data, 203)
                self.update_fetching_ui_with_point_data(point_data)
            elif point_type == 'fetching_interim':
                self.fetching_interim_data = point_data
                self.fetching_interim_point_cmd = RobotCommand.get_command_str('MoveL', point_data, 203)
                self.update_fetching_interim_ui_with_point_data(point_data)
            elif point_type == 'put_interim':
                self.put_interim_data = point_data
                self.put_interim_point_cmd = RobotCommand.get_command_str('MoveL', point_data, 203)
                self.update_put_interim_ui_with_point_data(point_data)
            elif point_type == 'left_tray_zorn':
                self.left_tray_zorn_data = point_data
                self.left_tray_zorn_cmd = RobotCommand.get_command_str('MoveL', point_data, 203)
                self.update_left_tray_zorn_ui_with_point_data(point_data)
            elif point_type == 'right_tray_zorn':
                self.right_tray_zorn_data = point_data
                self.right_tray_zorn_cmd = RobotCommand.get_command_str('MoveL', point_data, 203)
                self.update_right_tray_zorn_ui_with_point_data(point_data)
        except Exception as e:
            self.logger.error(f"更新点位数据失败 {point_type}: {e}")

    def _safe_float_format(self, value, default=0.0):
        """安全地将值转换为浮点数并格式化"""
        try:
            if value is None:
                return f"{default:.2f}"
            if isinstance(value, str):
                # 如果是字符串，尝试转换为浮点数
                float_value = float(value)
                return f"{float_value:.2f}"
            elif isinstance(value, (int, float)):
                # 如果已经是数值类型，直接格式化
                return f"{float(value):.2f}"
            else:
                # 其他类型，使用默认值
                return f"{default:.2f}"
        except (ValueError, TypeError):
            # 转换失败，使用默认值
            return f"{default:.2f}"

    # 新增方法：从数据库加载特定类型的点位数据
    def _load_point(self, point_type):
        """从数据库加载特定类型的点位数据"""
        try:
            records = self.db_tool.select("teaching_points", condition=f"point_type = '{point_type}'")
            if records and len(records) > 0:
                record = records[0]
                # 确保所有数值都转换为正确的类型
                return {
                    'x': self._safe_convert_to_float(record['x']),
                    'y': self._safe_convert_to_float(record['y']),
                    'z': self._safe_convert_to_float(record['z']),
                    'rx': self._safe_convert_to_float(record['rx']),
                    'ry': self._safe_convert_to_float(record['ry']),
                    'rz': self._safe_convert_to_float(record['rz']),
                    'J1': self._safe_convert_to_float(record['j1']),
                    'J2': self._safe_convert_to_float(record['j2']),
                    'J3': self._safe_convert_to_float(record['j3']),
                    'J4': self._safe_convert_to_float(record['j4']),
                    'J5': self._safe_convert_to_float(record['j5']),
                    'J6': self._safe_convert_to_float(record['j6']),
                    'speed': self._safe_convert_to_int(record['speed']),
                    'toolNum': self._safe_convert_to_int(record['toolNum']),
                    'workPieceNum': self._safe_convert_to_int(record['workPieceNum'])
                }
            return None
        except Exception as e:
            self.logger.error(f"加载点位 {point_type} 失败: {e}")
            return None

    def _safe_convert_to_float(self, value, default=0.0):
        """安全地将值转换为浮点数"""
        try:
            if value is None:
                return default
            return float(value)
        except (ValueError, TypeError):
            return default

    def _safe_convert_to_int(self, value, default=0):
        """安全地将值转换为整数"""
        try:
            if value is None:
                return default
            return int(float(value))  # 先转float再转int，处理"1.0"这样的字符串
        except (ValueError, TypeError):
            return default

    # 新增方法：保存点位数据到数据库
    def _save_point(self, point_type, point_data):
        """保存点位数据到数据库"""
        try:
            # 添加点位类型，确保数据类型正确
            data = {
                'point_type': point_type,
                'x': self._safe_convert_to_float(point_data['x']),
                'y': self._safe_convert_to_float(point_data['y']),
                'z': self._safe_convert_to_float(point_data['z']),
                'rx': self._safe_convert_to_float(point_data['rx']),
                'ry': self._safe_convert_to_float(point_data['ry']),
                'rz': self._safe_convert_to_float(point_data['rz']),
                'j1': self._safe_convert_to_float(point_data['J1']),
                'j2': self._safe_convert_to_float(point_data['J2']),
                'j3': self._safe_convert_to_float(point_data['J3']),
                'j4': self._safe_convert_to_float(point_data['J4']),
                'j5': self._safe_convert_to_float(point_data['J5']),
                'j6': self._safe_convert_to_float(point_data['J6']),
                'speed': self._safe_convert_to_int(point_data['speed']),
                'toolNum': self._safe_convert_to_int(point_data['toolNum']),
                'workPieceNum': self._safe_convert_to_int(point_data['workPieceNum'])
            }

            # 检查是否已存在该点位
            records = self.db_tool.select("teaching_points", condition=f"point_type = '{point_type}'")

            if records and len(records) > 0:
                # 更新已有记录
                self.db_tool.update("teaching_points", data, f"point_type = '{point_type}'")

            else:
                # 插入新记录
                self.db_tool.insert("teaching_points", data)

            return True
        except Exception as e:
            self.logger.error(f"保存点位 {point_type} 失败: {e}")
            return False

    def _setup_connections(self):
        """安全设置信号连接"""
        # 设置按钮属性
        self.fetchingButton.setProperty("buttonType", "fetching")
        self.ManualButton.setProperty("buttonType", "manual")

        for button in [self.fetchingButton, self.ManualButton]:
            button.setCheckable(True)
            button.setAutoExclusive(True)
            button.clicked.connect(self._handle_button_click)

        # 设置初始按钮样式
        self._update_button_styles()

    def _update_button_styles(self):
        """更新按钮背景颜色样式 - 响应式设计"""
        # 获取当前窗口尺寸来调整样式
        try:
            # 获取父窗口尺寸
            parent_width = self.parent().width() if self.parent() else 800
            parent_height = self.parent().height() if self.parent() else 600

            # 根据窗口尺寸调整padding和字体
            if parent_width < 600 or parent_height < 400:
                # 小尺寸窗口
                padding = "2px 4px"
                border_radius = "3px"
                min_height = "18px"
                max_height = "30px"
            else:
                # 正常尺寸窗口
                padding = "4px 8px"
                border_radius = "4px"
                min_height = "22px"
                max_height = "35px"

        except:
            # 默认值
            padding = "4px 6px"
            border_radius = "4px"
            min_height = "20px"
            max_height = "35px"

        # 定义按钮样式 - 无边框
        selected_style = f"""
            QPushButton {{
                background-color: #4F94CD;
                color: white;
                border: none;
                border-radius: {border_radius};
                padding: {padding};
                font-weight: bold;
                min-height: {min_height};
                max-height: {max_height};
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: #63B8FF;
            }}
        """

        unselected_style = f"""
            QPushButton {{
                background-color: white;
                color: #333333;
                border: none;
                border-radius: {border_radius};
                padding: {padding};
                font-weight: bold;
                min-height: {min_height};
                max-height: {max_height};
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: #F0F0F0;
            }}
        """

        # 应用样式
        if self.fetchingButton.isChecked():
            # 抓取放置按钮选中 - 蓝色背景
            self.fetchingButton.setStyleSheet(selected_style)
            # 手动操作按钮未选中 - 白色背景
            self.ManualButton.setStyleSheet(unselected_style)
        else:
            # 手动操作按钮选中 - 蓝色背景
            self.ManualButton.setStyleSheet(selected_style)
            # 抓取放置按钮未选中 - 白色背景
            self.fetchingButton.setStyleSheet(unselected_style)

    def _setup_resize_handler(self):
        """设置窗口尺寸变化处理"""
        try:
            # 创建一个定时器来检测尺寸变化
            self.resize_timer = QTimer()
            self.resize_timer.timeout.connect(self._on_resize_check)
            self.resize_timer.start(500)  # 每500ms检查一次

            # 记录当前尺寸
            self.last_size = None
        except Exception as e:
            self.logger.warning(f"设置窗口尺寸监听失败: {e}")

    def _on_resize_check(self):
        """检查窗口尺寸是否变化"""
        try:
            if self.parent():
                current_size = (self.parent().width(), self.parent().height())
                if self.last_size != current_size:
                    self.last_size = current_size
                    # 延迟更新样式，避免频繁更新
                    QTimer.singleShot(100, self._update_button_styles)
        except Exception:
            pass  # 忽略错误，避免影响正常功能

    @Slot()
    def _handle_button_click(self):
        """统一处理按钮点击事件"""
        sender = self.sender()
        if not sender.isChecked():
            sender.setChecked(True)
            return

        self.logger.debug(f"按钮点击: {sender.objectName()}")
        self._update_button_styles()  # 更新按钮样式
        self._switch_interface()

    def _switch_interface(self):
        """执行界面切换操作"""

        self._adjust_interface_size()
        # 确保布局存在
        if not self.widget_qiehuan.layout():
            layout = QVBoxLayout()
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(0)
            self.widget_qiehuan.setLayout(layout)
            self.logger.debug("为widget_qiehuan创建了新布局")

        # 获取当前布局
        layout = self.widget_qiehuan.layout()

        # 安全清理旧控件
        self._clear_layout(layout)

        # 添加新界面
        if self.fetchingButton.isChecked():
            target_widget = self.teaching_widget
        elif self.ManualButton.isChecked():
            target_widget = self.manual_widget

        layout.addWidget(target_widget)
        target_widget.show()

        # 刷新布局
        layout.activate()
        self.widget_qiehuan.updateGeometry()

        # 在界面切换后重新加载点位数据到UI
        if self.fetchingButton.isChecked():
            # 重新加载设置
            self._load_and_apply_settings()

            if hasattr(self, 'fetching_point_data'):
                self.update_fetching_ui_with_point_data(self.fetching_point_data)
            if hasattr(self, 'fetching_interim_data'):
                self.update_fetching_interim_ui_with_point_data(self.fetching_interim_data)
            if hasattr(self, 'put_interim_data'):
                self.update_put_interim_ui_with_point_data(self.put_interim_data)
            if hasattr(self, 'left_tray_zorn_data'):
                self.update_left_tray_zorn_ui_with_point_data(self.left_tray_zorn_data)
            if hasattr(self, 'right_tray_zorn_data'):
                self.update_right_tray_zorn_ui_with_point_data(self.right_tray_zorn_data)

    def _clear_layout(self, layout):
        """安全清除布局中的所有控件"""
        while layout.count():
            item = layout.takeAt(0)
            if item and item.widget():
                w = item.widget()
                w.hide()
                layout.removeWidget(w)
                self.logger.debug(f"移除控件: {w.objectName()}")

    def _adjust_interface_size(self):
        """动态调整界面尺寸"""
        # 获取当前活动界面
        current_widget = self.teaching_widget if self.fetchingButton.isChecked() else self.manual_widget

        # 设置最小尺寸（根据实际需要调整数值）
        current_widget.setMinimumSize(800, 500)

        # 对齐方式（居中显示）
        if layout := self.widget_qiehuan.layout():
            layout.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 需要 from PySide6.QtCore import Qt

        # 强制更新布局
        current_widget.updateGeometry()
        self.widget_qiehuan.update()

    def clean_up(self):
        """清理资源"""
        for button in [self.fetchingButton, self.ManualButton]:
            try:
                button.clicked.disconnect()
            except RuntimeError:
                pass

        if layout := self.widget_qiehuan.layout():
            self._clear_layout(layout)
            self.widget_qiehuan.setLayout(None)

    def _init_ui_components(self):
        """初始化所有子界面组件"""
        # 示教界面
        self.teaching_widget = QWidget()
        self.teaching_ui = Ui_Form_1()
        self.teaching_ui.setupUi(self.teaching_widget)

        # 手动界面
        self.manual_widget = QWidget()
        self.manual_ui = Ui_Form_2()
        self.manual_ui.setupUi(self.manual_widget)

        # 初始化抓取/放置界面按钮连接
        self._init_fetch_place_buttons()
        self._init_manual_place_buttons()

    def _init_fetch_place_buttons(self):
        """初始化抓取/放置界面的按钮连接"""
        try:
            # 连接示教点位按钮
            self.teaching_ui.pushButton_2.clicked.connect(self.on_teach_point_clicked)

            # 连接返回示教点按钮
            self.teaching_ui.pushButton.clicked.connect(self.on_return_to_point_clicked)

            # 连接示教抓取过渡点按钮
            if hasattr(self.teaching_ui, "pushButton_5"):
                self.teaching_ui.pushButton_5.clicked.connect(self.on_fetching_interim_point_clicked)

            # 连接返回抓取过渡点按钮
            if hasattr(self.teaching_ui, "pushButton_6"):
                self.teaching_ui.pushButton_6.clicked.connect(self.on_return_to_fetching_interim_point_clicked)

            # 连接返回放置过渡点按钮
            if hasattr(self.teaching_ui, "pushButton_10"):
                self.teaching_ui.pushButton_10.clicked.connect(self.on_return_to_put_interim_point_clicked)


            # 连接示教放置过渡点按钮
            if hasattr(self.teaching_ui, "pushButton_7"):
                self.teaching_ui.pushButton_7.clicked.connect(self.on_put_interim_point_clicked)

            if hasattr(self.teaching_ui, "pushButton_11"):
                self.teaching_ui.pushButton_11.clicked.connect(self.on_left_tray_zorn_clicked)

            if hasattr(self.teaching_ui, "pushButton_12"):
                self.teaching_ui.pushButton_12.clicked.connect(self.on_right_tray_zorn_clicked)

            self.teaching_ui.lineEdit_11.setText("0")
            self.teaching_ui.lineEdit_5.setText("0")
            self.teaching_ui.lineEdit_16.setText("0")

            # 加载保存的设置
            self._load_and_apply_settings()

            if hasattr(self.teaching_ui, "pushButton_8"):
                self.teaching_ui.pushButton_8.clicked.connect(self.save_safety_settings)

            if hasattr(self.teaching_ui, "pushButton_9"):
                self.teaching_ui.pushButton_9.clicked.connect(self.save_put_point_settings)

            if hasattr(self.teaching_ui, "pushButton_clear_11"):
                self.teaching_ui.pushButton_clear_11.clicked.connect(self.on_clear_fetching_interim_point_clicked)

            if hasattr(self.teaching_ui, "pushButton_clear_14"):
                self.teaching_ui.pushButton_clear_14.clicked.connect(self.on_clear_put_interim_point_clicked)

            if hasattr(self.teaching_ui, "pushButton_clear_12"):
                self.teaching_ui.pushButton_clear_12.clicked.connect(self.on_clear_left_tray_zorn_point_clicked)

            if hasattr(self.teaching_ui, "pushButton_clear_13"):
                self.teaching_ui.pushButton_clear_13.clicked.connect(self.on_clear_right_tray_zorn_point_clicked)

                # 确认信号连接状态 - 移除有问题的receivers检查
                # 注意：PySide6的Signal对象没有receivers方法
            self.logger.debug("send_cmd_to_robot 信号已设置")

        except Exception as e:
            self.logger.error(f"初始化抓取/放置界面时出错: {e}")

    def on_clear_left_tray_zorn_point_clicked(self):
        """清除抓取过渡点位信息"""
        try:
            # 确认用户操作
            reply = QMessageBox.question(
                self,
                "确认清除",
                "确定要清除左垛盘零点数据吗？此操作不可恢复！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 1. 从数据库中删除记录
            success = self.db_tool.delete(
                "teaching_points",
                f"point_type = 'left_tray_zorn'"
            )

            if not success:
                self.logger.warning("从数据库删除左垛盘记录失败")
                QMessageBox.warning(self, "清除失败", "删除数据库记录时出错")
                return

            # 2. 清除内存中的数据
            if hasattr(self, 'left_tray_zorn_data'):
                del self.left_tray_zorn_data

            if hasattr(self, 'left_tray_zorn_cmd'):
                del self.left_tray_zorn_cmd

            # 3. 清空UI显示
            self.clear_left_tray_zorn_ui()

            # 记录日志
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="clear_point",
                operation_content="清除左垛盘零点",
                operation_details={"point_type": "left_tray_zorn"},
                result="success"
            )

            QMessageBox.information(
                self,
                "清除成功",
                "抓取左垛盘数据已清除"
            )

        except Exception as e:
            self.logger.error(f"清除左垛盘零点点位时出错: {e}")
            QMessageBox.critical(
                self,
                "清除失败",
                f"清除点位时出错: {e}"
            )

    def clear_left_tray_zorn_ui(self):
        """清空抓取过渡点UI显示"""
        try:
            if hasattr(self.teaching_ui, "lineEdit_26"):
                self.teaching_ui.lineEdit_26.setText("")

            if hasattr(self.teaching_ui, "lineEdit_27"):
                self.teaching_ui.lineEdit_27.setText("")

            if hasattr(self.teaching_ui, "lineEdit_28"):
                self.teaching_ui.lineEdit_28.setText("")

            if hasattr(self.teaching_ui, "lineEdit_29"):
                self.teaching_ui.lineEdit_29.setText("")

        except Exception as e:
            self.logger.error(f"清空左垛盘零点UI时出错: {e}")

    def on_clear_right_tray_zorn_point_clicked(self):
        """清除右垛盘位信息"""
        try:
            # 确认用户操作
            reply = QMessageBox.question(
                self,
                "确认清除",
                "确定要清除右垛盘零点数据吗？此操作不可恢复！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 1. 从数据库中删除记录
            success = self.db_tool.delete(
                "teaching_points",
                f"point_type = 'right_tray_zorn'"
            )

            if not success:
                self.logger.warning("从数据库删除右垛盘记录失败")
                QMessageBox.warning(self, "清除失败", "删除数据库记录时出错")
                return

            # 2. 清除内存中的数据
            if hasattr(self, 'right_tray_zorn_data'):
                del self.right_tray_zorn_data

            if hasattr(self, 'right_tray_zorn_cmd'):
                del self.right_tray_zorn_cmd

            # 3. 清空UI显示
            self.clear_right_tray_zorn_ui()

            # 记录日志
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="clear_point",
                operation_content="清除右垛盘零点",
                operation_details={"point_type": "right_tray_zorn"},
                result="success"
            )

            QMessageBox.information(
                self,
                "清除成功",
                "右垛盘数据已清除"
            )

        except Exception as e:
            self.logger.error(f"清除右垛盘零点点位时出错: {e}")
            QMessageBox.critical(
                self,
                "清除失败",
                f"清除点位时出错: {e}"
            )

    def clear_right_tray_zorn_ui(self):
        """清空抓取过渡点UI显示"""
        try:
            if hasattr(self.teaching_ui, "lineEdit_33"):
                self.teaching_ui.lineEdit_33.setText("")

            if hasattr(self.teaching_ui, "lineEdit_32"):
                self.teaching_ui.lineEdit_32.setText("")

            if hasattr(self.teaching_ui, "lineEdit_30"):
                self.teaching_ui.lineEdit_30.setText("")

            if hasattr(self.teaching_ui, "lineEdit_31"):
                self.teaching_ui.lineEdit_31.setText("")

        except Exception as e:
            self.logger.error(f"清空左垛盘零点UI时出错: {e}")


    def on_clear_put_interim_point_clicked(self):
        """清除抓取过渡点位信息"""
        try:
            # 确认用户操作
            reply = QMessageBox.question(
                self,
                "确认清除",
                "确定要清除放置过渡点位数据吗？此操作不可恢复！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 1. 从数据库中删除记录
            success = self.db_tool.delete(
                "teaching_points",
                f"point_type = 'put_interim'"
            )

            if not success:
                self.logger.warning("从数据库删除放置过渡点记录失败")
                QMessageBox.warning(self, "清除失败", "删除数据库记录时出错")
                return

            # 2. 清除内存中的数据
            if hasattr(self, 'put_interim_data'):
                del self.put_interim_data

            if hasattr(self, 'put_interim_point_cmd'):
                del self.put_interim_point_cmd

            # 3. 清空UI显示
            self.clear_put_interim_ui()

            # 记录日志
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="clear_point",
                operation_content="清除抓取过渡点",
                operation_details={"point_type": "put_interim"},
                result="success"
            )

            QMessageBox.information(
                self,
                "清除成功",
                "放置过渡点位数据已清除"
            )

        except Exception as e:
            self.logger.error(f"清除放置过渡点位时出错: {e}")
            QMessageBox.critical(
                self,
                "清除失败",
                f"清除点位时出错: {e}"
            )
    def clear_put_interim_ui(self):
        """清空抓取过渡点UI显示"""
        try:
            if hasattr(self.teaching_ui, "lineEdit_17"):
                self.teaching_ui.lineEdit_17.setText("")

            if hasattr(self.teaching_ui, "lineEdit_18"):
                self.teaching_ui.lineEdit_18.setText("")

            if hasattr(self.teaching_ui, "lineEdit_19"):
                self.teaching_ui.lineEdit_19.setText("")

            if hasattr(self.teaching_ui, "lineEdit_20"):
                self.teaching_ui.lineEdit_20.setText("")

        except Exception as e:
            self.logger.error(f"清空放置过渡点UI时出错: {e}")


    def on_clear_fetching_interim_point_clicked(self):
        """清除抓取过渡点位信息"""
        try:
            # 确认用户操作
            reply = QMessageBox.question(
                self,
                "确认清除",
                "确定要清除抓取过渡点位数据吗？此操作不可恢复！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 1. 从数据库中删除记录
            success = self.db_tool.delete(
                "teaching_points",
                f"point_type = 'fetching_interim'"
            )

            if not success:
                self.logger.warning("从数据库删除抓取过渡点记录失败")
                QMessageBox.warning(self, "清除失败", "删除数据库记录时出错")
                return

            # 2. 清除内存中的数据
            if hasattr(self, 'fetching_interim_data'):
                del self.fetching_interim_data

            if hasattr(self, 'fetching_interim_point_cmd'):
                del self.fetching_interim_point_cmd

            # 3. 清空UI显示
            self.clear_fetching_interim_ui()

            # 记录日志
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="clear_point",
                operation_content="清除抓取过渡点",
                operation_details={"point_type": "fetching_interim"},
                result="success"
            )

            QMessageBox.information(
                self,
                "清除成功",
                "抓取过渡点位数据已清除"
            )

        except Exception as e:
            self.logger.error(f"清除抓取过渡点位时出错: {e}")
            QMessageBox.critical(
                self,
                "清除失败",
                f"清除点位时出错: {e}"
            )
    def clear_fetching_interim_ui(self):
        """清空抓取过渡点UI显示"""
        try:
            if hasattr(self.teaching_ui, "lineEdit_15"):
                self.teaching_ui.lineEdit_15.setText("")

            if hasattr(self.teaching_ui, "lineEdit_14"):
                self.teaching_ui.lineEdit_14.setText("")

            if hasattr(self.teaching_ui, "lineEdit_13"):
                self.teaching_ui.lineEdit_13.setText("")

            if hasattr(self.teaching_ui, "lineEdit_12"):
                self.teaching_ui.lineEdit_12.setText("")

        except Exception as e:
            self.logger.error(f"清空抓取过渡点UI时出错: {e}")


    def save_safety_settings(self):
        self.logger.info("=== save_safety_settings 被调用 ===")
        try:
            # 获取当前输入框的值
            text_21 = self.teaching_ui.lineEdit_21.text()
            text_22 = self.teaching_ui.lineEdit_22.text()
            text_23 = self.teaching_ui.lineEdit_23.text()

            self.logger.info(f"输入框值: lineEdit_21='{text_21}', lineEdit_22='{text_22}', lineEdit_23='{text_23}'")

            # Process lineEdit_21 - 抓取等待高度
            value_21 = float(text_21.split()[0]) if text_21.strip() else gv.SAFETY_HEIGHT
            gv.SAFETY_HEIGHT = value_21

            # Process lineEdit_22 - 爬取抬升高度
            value_22 = float(text_22.split()[0]) if text_22.strip() else getattr(gv, 'LIFT_HEIGHT', 50)
            if not hasattr(gv, 'LIFT_HEIGHT'):
                gv.LIFT_HEIGHT = 50
            gv.LIFT_HEIGHT = value_22

            # Process lineEdit_23 - 抬升后等待时间
            value_23 = int(text_23.split()[0]) if text_23.strip() else gv.WAIT_TIME
            gv.WAIT_TIME = value_23

            self.logger.info(f"解析后的值: safety_height={value_21}, lift_height={value_22}, wait_time={value_23}")

            # 保存到数据库
            settings_data = {
                'safety_height': value_21,
                'lift_height': value_22,
                'wait_time': value_23
            }

            success = self._save_settings_to_db('safety_settings', settings_data)

            if success:
                # 显示保存成功消息
                QMessageBox.information(self, "保存成功", "安全设置已保存")
                self.logger.info(f"安全设置已保存: {settings_data}")
            else:
                QMessageBox.warning(self, "保存失败", "数据库保存失败")

        except ValueError as e:
            self.logger.error(f"保存安全设置时数值转换错误: {e}")
            QMessageBox.warning(self, "保存失败", f"请输入有效的数值: {e}")
            # Reset to previous values
            self.teaching_ui.lineEdit_21.setText(f"{gv.SAFETY_HEIGHT} mm")
            self.teaching_ui.lineEdit_22.setText(f"{getattr(gv, 'LIFT_HEIGHT', 50)} mm")
            self.teaching_ui.lineEdit_23.setText(f"{gv.WAIT_TIME} ms")
        except Exception as e:
            self.logger.error(f"保存安全设置时发生未知错误: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            QMessageBox.critical(self, "保存失败", f"发生未知错误: {e}")

    def save_put_point_settings(self):
        self.logger.info("=== save_put_point_settings 被调用 ===")
        try:
            # 获取当前输入框的值
            text_24 = self.teaching_ui.lineEdit_24.text()
            text_25 = self.teaching_ui.lineEdit_25.text()

            self.logger.info(f"输入框值: lineEdit_24='{text_24}', lineEdit_25='{text_25}'")

            # Process lineEdit_24 - 放置前悬停高度
            value_24 = float(text_24.split()[0]) if text_24.strip() else gv.PUT_HEIGHT
            gv.PUT_HEIGHT = value_24

            # Process lineEdit_25 - 放置后抬升高度
            value_25 = float(text_25.split()[0]) if text_25.strip() else getattr(gv, 'PUT_LIFT_HEIGHT', 50)
            if not hasattr(gv, 'PUT_LIFT_HEIGHT'):
                gv.PUT_LIFT_HEIGHT = 50
            gv.PUT_LIFT_HEIGHT = value_25

            self.logger.info(f"解析后的值: put_height={value_24}, put_lift_height={value_25}")

            # 保存到数据库
            settings_data = {
                'put_height': value_24,
                'put_lift_height': value_25
            }

            success = self._save_settings_to_db('put_settings', settings_data)

            if success:
                # 显示保存成功消息
                QMessageBox.information(self, "保存成功", "放置点设置已保存")
                self.logger.info(f"放置点设置已保存: {settings_data}")
            else:
                QMessageBox.warning(self, "保存失败", "数据库保存失败")

        except ValueError as e:
            self.logger.error(f"保存放置点设置时数值转换错误: {e}")
            QMessageBox.warning(self, "保存失败", f"请输入有效的数值: {e}")
            # Reset to previous values
            self.teaching_ui.lineEdit_24.setText(f"{gv.PUT_HEIGHT} mm")
            self.teaching_ui.lineEdit_25.setText(f"{getattr(gv, 'PUT_LIFT_HEIGHT', 50)} mm")
        except Exception as e:
            self.logger.error(f"保存放置点设置时发生未知错误: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            QMessageBox.critical(self, "保存失败", f"发生未知错误: {e}")

    def _save_settings_to_db(self, setting_type, settings_data):
        """保存设置到数据库"""
        try:
            self.logger.info(f"开始保存设置: {setting_type} = {settings_data}")

            # 检查是否已存在该设置类型
            records = self.db_tool.select("app_settings", condition=f"setting_type = '{setting_type}'")
            self.logger.info(f"查询到 {len(records) if records else 0} 条现有记录")

            data = {
                'setting_type': setting_type,
                'settings_json': str(settings_data)
            }

            if records and len(records) > 0:
                # 更新已有记录
                success = self.db_tool.update("app_settings", data, f"setting_type = '{setting_type}'")
                self.logger.info(f"更新设置: {setting_type}, 成功: {success}")
            else:
                # 插入新记录
                success = self.db_tool.insert("app_settings", data)
                self.logger.info(f"插入新设置: {setting_type}, 成功: {success}")

            # 验证保存结果
            verify_records = self.db_tool.select("app_settings", condition=f"setting_type = '{setting_type}'")
            if verify_records:
                self.logger.info(f"验证保存结果: {verify_records[0]['settings_json']}")

            return True
        except Exception as e:
            self.logger.error(f"保存设置到数据库失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _load_settings_from_db(self, setting_type):
        """从数据库加载设置"""
        try:
            self.logger.info(f"开始加载设置: {setting_type}")

            records = self.db_tool.select("app_settings", condition=f"setting_type = '{setting_type}'")
            self.logger.info(f"查询到 {len(records) if records else 0} 条记录")

            if records and len(records) > 0:
                settings_str = records[0]['settings_json']
                self.logger.info(f"原始设置字符串: {settings_str}")

                # 安全地解析字符串为字典
                settings_dict = eval(settings_str)
                self.logger.info(f"从数据库加载设置: {setting_type} = {settings_dict}")
                return settings_dict
            else:
                self.logger.info(f"未找到设置: {setting_type}")
                return None
        except Exception as e:
            self.logger.error(f"从数据库加载设置失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None



    def _load_and_apply_settings(self):
        """加载并应用保存的设置"""
        try:
            # 加载安全设置
            safety_settings = self._load_settings_from_db('safety_settings')
            if safety_settings:
                self.teaching_ui.lineEdit_21.setText(f"{safety_settings.get('safety_height', gv.SAFETY_HEIGHT)} mm")
                self.teaching_ui.lineEdit_22.setText(f"{safety_settings.get('lift_height', getattr(gv, 'LIFT_HEIGHT', 50))} mm")
                self.teaching_ui.lineEdit_23.setText(f"{safety_settings.get('wait_time', gv.WAIT_TIME)} ms")

                # 更新全局变量
                gv.SAFETY_HEIGHT = safety_settings.get('safety_height', gv.SAFETY_HEIGHT)
                if not hasattr(gv, 'LIFT_HEIGHT'):
                    gv.LIFT_HEIGHT = 50
                gv.LIFT_HEIGHT = safety_settings.get('lift_height', gv.LIFT_HEIGHT)
                gv.WAIT_TIME = safety_settings.get('wait_time', gv.WAIT_TIME)
            else:
                # 使用默认值
                self.teaching_ui.lineEdit_21.setText(f"{gv.SAFETY_HEIGHT} mm")
                self.teaching_ui.lineEdit_22.setText(f"{getattr(gv, 'LIFT_HEIGHT', 50)} mm")
                self.teaching_ui.lineEdit_23.setText(f"{gv.WAIT_TIME} ms")

            # 加载放置设置
            put_settings = self._load_settings_from_db('put_settings')
            if put_settings:
                self.teaching_ui.lineEdit_24.setText(f"{put_settings.get('put_height', gv.PUT_HEIGHT)} mm")
                self.teaching_ui.lineEdit_25.setText(f"{put_settings.get('put_lift_height', getattr(gv, 'PUT_LIFT_HEIGHT', 50))} mm")

                # 更新全局变量
                gv.PUT_HEIGHT = put_settings.get('put_height', gv.PUT_HEIGHT)
                if not hasattr(gv, 'PUT_LIFT_HEIGHT'):
                    gv.PUT_LIFT_HEIGHT = 50
                gv.PUT_LIFT_HEIGHT = put_settings.get('put_lift_height', gv.PUT_LIFT_HEIGHT)
            else:
                # 使用默认值
                self.teaching_ui.lineEdit_24.setText(f"{gv.PUT_HEIGHT} mm")
                self.teaching_ui.lineEdit_25.setText(f"{getattr(gv, 'PUT_LIFT_HEIGHT', 50)} mm")

            self.logger.info("设置加载完成")
        except Exception as e:
            self.logger.error(f"加载设置时出错: {e}")
            # 出错时使用默认值
            self.teaching_ui.lineEdit_21.setText(f"{gv.SAFETY_HEIGHT} mm")
            self.teaching_ui.lineEdit_22.setText(f"{getattr(gv, 'LIFT_HEIGHT', 50)} mm")
            self.teaching_ui.lineEdit_23.setText(f"{gv.WAIT_TIME} ms")
            self.teaching_ui.lineEdit_24.setText(f"{gv.PUT_HEIGHT} mm")
            self.teaching_ui.lineEdit_25.setText(f"{getattr(gv, 'PUT_LIFT_HEIGHT', 50)} mm")

    # 添加缺失的方法
    def on_return_to_point_clicked(self):
        """点击返回示教点按钮时的处理函数"""
        try:
            # 检查机器人模式
            if not self._check_manual_mode():
                return

            # 记录返回操作开始
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="robot_return_point",
                operation_content="开始返回抓取示教点",
                operation_details={"point_type": "fetching_point"},
                result="success"
            )

            # 检查是否有保存的点位
            if not hasattr(self, 'fetching_point_data') or self.fetching_point_data is None:
                # 记录返回失败
                LogLogic.log_operation(
                    operation_type="robot_return_point",
                    operation_content="返回抓取示教点失败",
                    operation_details={"point_type": "fetching_point"},
                    result="failed",
                    error_message="没有保存的示教点位数据"
                )

                self.logger.warning("没有保存的点位数据")
                QMessageBox.warning(self,
                                    QCoreApplication.translate("ReturnToPoint", "返回失败"),
                                    QCoreApplication.translate("ReturnToPoint", "没有保存的示教点位，请先示教点位"))
                return

            # 生成运动命令
            command = fetching_point_
            # 使用统一指令管理器发送命令
            command_manager.send_command(command, "返回抓取点")

            # 记录返回命令发送成功
            LogLogic.log_operation(
                operation_type="robot_return_point",
                operation_content="返回抓取示教点命令已发送",
                operation_details={
                    "point_type": "fetching_point",
                    "command": command
                },
                result="success"
            )

            # 提示用户
            QMessageBox.information(self,
                                    QCoreApplication.translate("ReturnToPoint", "返回命令已发送"),
                                    QCoreApplication.translate("ReturnToPoint", "机器人正在返回示教点位"))
        except Exception as e:
            # 记录异常
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="robot_return_point",
                operation_content="返回抓取示教点异常",
                operation_details={"point_type": "fetching_point"},
                result="failed",
                error_message=str(e)
            )

            self.logger.error(f"返回示教点位时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            QMessageBox.warning(self,
                                QCoreApplication.translate("ReturnToPoint", "返回失败"),
                                QCoreApplication.translate("ReturnToPoint", f"返回示教点位时出错: {e}"))

    def on_teach_point_clicked(self):
        """点击示教点位按钮时的处理函数"""
        try:
            # 检查机器人模式
            if not self._check_manual_mode():
                return

            # 记录操作开始
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="teach_point_start",
                operation_content="开始示教抓取点位",
                operation_details={"point_type": "fetching_point"},
                result="success"
            )

            # 获取并保存当前点位
            fetching_cmd = self.fetching_point_get(save_only=True)

            if fetching_cmd:
                # 记录示教成功
                LogLogic.log_operation(
                    operation_type="teach_point_complete",
                    operation_content="示教抓取点位完成",
                    operation_details={"point_type": "fetching_point", "command": fetching_cmd},
                    result="success"
                )

                # 提示用户点位已保存
                QMessageBox.information(self,
                                        QCoreApplication.translate("TeachPoint", "示教点位成功"),
                                        QCoreApplication.translate("TeachPoint", "当前位置已成功保存为示教点位"))
            else:
                # 记录示教失败
                LogLogic.log_operation(
                    operation_type="teach_point_complete",
                    operation_content="示教抓取点位失败",
                    operation_details={"point_type": "fetching_point"},
                    result="failed",
                    error_message="获取点位数据失败"
                )
                self.logger.warning("获取点位失败，未返回有效命令")

        except Exception as e:
            # 记录异常
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="teach_point_complete",
                operation_content="示教抓取点位异常",
                operation_details={"point_type": "fetching_point"},
                result="failed",
                error_message=str(e)
            )

            self.logger.error(f"保存示教点位时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            QMessageBox.warning(self,
                                QCoreApplication.translate("TeachPoint", "示教点位失败"),
                                QCoreApplication.translate("TeachPoint", f"保存点位时出错: {e}"))

    def fetching_point_get(self, save_only=True):
        """获取当前位置作为示教点位"""
        try:

            # 获取原始位置数据
            raw_pos = self.robot_status.get_pos_values_as_tuple()

            # 检查数据有效性
            if not raw_pos:
                self.logger.error("获取到的机器人位置数据为空")
                QMessageBox.warning(self, "获取点位失败", "获取到的机器人位置数据为空")
                return None

            safe_pos = [0.0] * 12
            if raw_pos and len(raw_pos) >= 12:
                for i in range(12):
                    try:
                        safe_pos[i] = float(raw_pos[i]) if raw_pos[i] is not None else 0.0
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"机器坐标数据中发现非数值或无效值：{raw_pos[i]}，已设为 0.0。错误: {e}")
            else:
                self.logger.warning(f"位置数据长度不足，预期12，实际{len(raw_pos) if raw_pos else 0}")

            safe_pos_tuple = tuple(safe_pos)

            point_position_dict = {
                "x": safe_pos_tuple[0], "y": safe_pos_tuple[1], "z": safe_pos_tuple[2],
                "rx": safe_pos_tuple[3], "ry": safe_pos_tuple[4], "rz": safe_pos_tuple[5],
                "J1": safe_pos_tuple[6], "J2": safe_pos_tuple[7], "J3": safe_pos_tuple[8],
                "J4": safe_pos_tuple[9], "J5": safe_pos_tuple[10], "J6": safe_pos_tuple[11],
                'speed': 20, 'toolNum': 0, 'workPieceNum': 0
            }

            # 保存点位数据 - 使用一致的变量名
            self.fetching_point_data = point_position_dict

            # 保存到数据库
            self._save_point('fetching', point_position_dict)

            # 更新界面上的文本框
            self.update_fetching_ui_with_point_data(point_position_dict)

            # 生成命令字符串
            global fetching_point_

            fetching_point_ = RobotCommand.get_command_str('MoveL', point_position_dict, 203)

            # 如果不是只保存，则显示消息框
            if not save_only:
                msg = QCoreApplication.translate("FetchingPoint", "抓取过渡点已经重新定位。位置: {}")
                QMessageBox.information(self,
                                        QCoreApplication.translate("SafetyPoint", "抓取过渡点定位成功"),
                                        msg.format(point_position_dict))

            return fetching_point_
        except Exception as e:
            self.logger.error(f"获取点位时发生未预期的异常: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            QMessageBox.critical(self, "严重错误", f"获取点位时发生未预期的异常: {e}")
            return None


    def update_fetching_ui_with_point_data(self, point_data):
        """更新抓取界面上的文本框显示"""
        try:
            # 获取并记录所有lineEdit控件
            lineEdit_widgets = []
            for attr_name in dir(self.teaching_ui):
                if attr_name.startswith("lineEdit"):
                    lineEdit_widgets.append(attr_name)

            # 尝试直接使用各种可能的控件名称
            try:
                # 直接检查控件是否存在并尝试更新
                if hasattr(self.teaching_ui, "lineEdit"):
                    self.teaching_ui.lineEdit.setText(self._safe_float_format(point_data['x']))

                if hasattr(self.teaching_ui, "lineEdit_2"):
                    self.teaching_ui.lineEdit_2.setText(self._safe_float_format(point_data['y']))

                if hasattr(self.teaching_ui, "lineEdit_3"):
                    self.teaching_ui.lineEdit_3.setText(self._safe_float_format(point_data['z']))

                if hasattr(self.teaching_ui, "lineEdit_4"):
                    self.teaching_ui.lineEdit_4.setText(self._safe_float_format(point_data['rz']))

                # 强制刷新UI
                QApplication.processEvents()

            except Exception as e:
                self.logger.error(f"使用直接名称更新文本框失败: {e}")

        except Exception as e:
            self.logger.error(f"更新界面坐标数据时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

    def fetching_interim_point_get(self, save_only=True):
        """获取抓取过渡点位置"""
        try:
            raw_pos = self.robot_status.get_pos_values_as_tuple()  # 获取原始位置数据

            if not raw_pos:
                self.logger.warning("获取位置数据失败")
                QMessageBox.warning(self, "获取位置失败", "无法获取当前机器人位置，请确认机器人连接状态")
                return None

            safe_pos = [0.0] * 12
            if raw_pos and len(raw_pos) >= 12:
                for i in range(12):
                    try:
                        safe_pos[i] = float(raw_pos[i]) if raw_pos[i] is not None else 0.0
                    except (ValueError, TypeError):
                        self.logger.warning(f"机器坐标数据中发现非数值或无效值：{raw_pos[i]}，已设为 0.0。")

            safe_pos_tuple = tuple(safe_pos)
            point_position_dict = {
                "x": safe_pos_tuple[0], "y": safe_pos_tuple[1], "z": safe_pos_tuple[2],
                "rx": safe_pos_tuple[3], "ry": safe_pos_tuple[4], "rz": safe_pos_tuple[5],
                "J1": safe_pos_tuple[6], "J2": safe_pos_tuple[7], "J3": safe_pos_tuple[8],
                "J4": safe_pos_tuple[9], "J5": safe_pos_tuple[10], "J6": safe_pos_tuple[11],
                'speed': 20, 'toolNum': 0, 'workPieceNum': 0
            }

            # 保存点位数据
            self.fetching_interim_data = point_position_dict

            # 保存到数据库
            self._save_point('fetching_interim', point_position_dict)

            # 更新界面文本
            self.update_fetching_interim_ui_with_point_data(point_position_dict)

            # 生成命令字符串
            fetching_interim_point = RobotCommand.get_command_str('MoveL', point_position_dict, 203)

            # 保存命令
            self.fetching_interim_point_cmd = fetching_interim_point

            # 显示成功消息
            if not save_only:
                msg = QCoreApplication.translate("FetchingPoint", "抓取过渡点已经重新定位。位置: {}")
                QMessageBox.information(self,
                                        QCoreApplication.translate("SafetyPoint", "抓取过渡点定位成功"),
                                        msg.format(point_position_dict))

            return fetching_interim_point
        except Exception as e:
            self.logger.error(f"获取过渡点位时出错: {e}")
            QMessageBox.warning(self, "获取位置失败", f"获取过渡点位时出错: {e}")
            return None

    def update_fetching_interim_ui_with_point_data(self, point_data):
        """更新抓取过渡点界面上的文本框"""
        try:
            if hasattr(self.teaching_ui, "lineEdit_15"):
                self.teaching_ui.lineEdit_15.setText(self._safe_float_format(point_data['x']))

            if hasattr(self.teaching_ui, "lineEdit_14"):
                self.teaching_ui.lineEdit_14.setText(self._safe_float_format(point_data['y']))

            if hasattr(self.teaching_ui, "lineEdit_13"):
                self.teaching_ui.lineEdit_13.setText(self._safe_float_format(point_data['z']))

            if hasattr(self.teaching_ui, "lineEdit_12"):
                self.teaching_ui.lineEdit_12.setText(self._safe_float_format(point_data['rz']))

            # 强制刷新UI
            QApplication.processEvents()

        except Exception as e:
            self.logger.error(f"更新抓取过渡点界面坐标数据时出错: {e}")

    def on_fetching_interim_point_clicked(self):
        """点击示教抓取过渡点按钮的处理函数"""
        try:
            # 检查机器人模式
            if not self._check_manual_mode():
                return

            # 记录操作开始
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="teach_point_start",
                operation_content="开始示教抓取过渡点",
                operation_details={"point_type": "fetching_interim_point"},
                result="success"
            )

            fetching_cmd = self.fetching_interim_point_get()
            if fetching_cmd:
                # 记录示教成功
                LogLogic.log_operation(
                    operation_type="teach_point_complete",
                    operation_content="示教抓取过渡点完成",
                    operation_details={"point_type": "fetching_interim_point", "command": fetching_cmd},
                    result="success"
                )

                # 提示用户点位已保存
                QMessageBox.information(self,
                                        QCoreApplication.translate("TeachPoint", "示教点位成功"),
                                        QCoreApplication.translate("TeachPoint", "当前位置已成功保存为示教点位"))
            else:
                # 记录示教失败
                LogLogic.log_operation(
                    operation_type="teach_point_complete",
                    operation_content="示教抓取过渡点失败",
                    operation_details={"point_type": "fetching_interim_point"},
                    result="failed",
                    error_message="获取点位数据失败"
                )
                self.logger.warning("获取点位失败，未返回有效命令")

        except Exception as e:
            # 记录异常
            from ..logic.log_logic import LogLogic
            LogLogic.log_operation(
                operation_type="teach_point_complete",
                operation_content="示教抓取过渡点异常",
                operation_details={"point_type": "fetching_interim_point"},
                result="failed",
                error_message=str(e)
            )

            self.logger.error(f"保存示教点位时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            QMessageBox.warning(self,
                                QCoreApplication.translate("TeachPoint", "示教点位失败"),
                                QCoreApplication.translate("TeachPoint", f"保存点位时出错: {e}"))

    def on_return_to_put_interim_point_clicked(self):
        """点击返回放置过渡点按钮的处理函数"""
        try:
            # 检查机器人模式
            if not self._check_manual_mode():
                return

            # 检查是否有保存的命令
            if not hasattr(self, 'put_interim_point_cmd') or not self.put_interim_point_cmd:
                self.logger.warning("没有保存放置过渡点命令")
                QMessageBox.warning(self, "返回失败", "没有保存的放置过渡点位，请先示教点位")
                return

            # 发送命令给机器人
            command = self.put_interim_point_cmd
            # 使用统一指令管理器发送命令
            command_manager.send_command(command, "返回放置过渡点")

            # 提示用户
            QMessageBox.information(self, "返回命令已发送", "机器人正在返回放置过渡点位")
        except Exception as e:
            self.logger.error(f"返回放置过渡点位时出错: {e}")
            QMessageBox.warning(self, "返回失败", f"返回放置过渡点位时出错: {e}")


    def on_return_to_fetching_interim_point_clicked(self):
        """点击返回抓取过渡点按钮的处理函数"""
        try:
            # 检查机器人模式
            if not self._check_manual_mode():
                return

            # 检查是否有保存的命令
            if not hasattr(self, 'fetching_interim_point_cmd') or not self.fetching_interim_point_cmd:
                self.logger.warning("没有保存的抓取过渡点命令")
                QMessageBox.warning(self, "返回失败", "没有保存的抓取过渡点位，请先示教点位")
                return

            # 发送命令给机器人
            command = self.fetching_interim_point_cmd
            # 使用统一指令管理器发送命令
            command_manager.send_command(command, "返回抓取过渡点")

            # 提示用户
            QMessageBox.information(self, "返回命令已发送", "机器人正在返回抓取过渡点位")
        except Exception as e:
            self.logger.error(f"返回抓取过渡点位时出错: {e}")
            QMessageBox.warning(self, "返回失败", f"返回抓取过渡点位时出错: {e}")

    def put_interim_point_get(self, save_only=True):
        """获取放置过渡点位置（只保存不返回）"""
        try:
            raw_pos = self.robot_status.get_pos_values_as_tuple()  # 获取原始位置数据

            if not raw_pos:
                self.logger.warning("获取位置数据失败")
                QMessageBox.warning(self, "获取位置失败", "无法获取当前机器人位置，请确认机器人连接状态")
                return None

            safe_pos = [0.0] * 12
            if raw_pos and len(raw_pos) >= 12:
                for i in range(12):
                    try:
                        safe_pos[i] = float(raw_pos[i]) if raw_pos[i] is not None else 0.0
                    except (ValueError, TypeError):
                        self.logger.warning(f"机器坐标数据中发现非数值或无效值：{raw_pos[i]}，已设为 0.0。")

            safe_pos_tuple = tuple(safe_pos)
            point_position_dict = {
                "x": safe_pos_tuple[0], "y": safe_pos_tuple[1], "z": safe_pos_tuple[2],
                "rx": safe_pos_tuple[3], "ry": safe_pos_tuple[4], "rz": safe_pos_tuple[5],
                "J1": safe_pos_tuple[6], "J2": safe_pos_tuple[7], "J3": safe_pos_tuple[8],
                "J4": safe_pos_tuple[9], "J5": safe_pos_tuple[10], "J6": safe_pos_tuple[11],
                'speed': 20, 'toolNum': 0, 'workPieceNum': 0
            }

            # 保存点位数据
            self.put_interim_data = point_position_dict

            # 保存到数据库
            self._save_point('put_interim', point_position_dict)

            # 更新界面文本
            self.update_put_interim_ui_with_point_data(point_position_dict)

            # 生成命令字符串（尽管不会用于返回，但保存备用）
            put_interim_point = RobotCommand.get_command_str('MoveL', point_position_dict, 203)
            self.put_interim_point_cmd = put_interim_point

            # 显示成功消息
            if not save_only:
                msg = QCoreApplication.translate("PutPoint", "放置过渡点已经定位。位置: {}")
                QMessageBox.information(self,
                                        QCoreApplication.translate("PutPoint", "放置过渡点定位成功"),
                                        msg.format(point_position_dict))

            return put_interim_point
        except Exception as e:
            self.logger.error(f"获取放置过渡点位时出错: {e}")
            QMessageBox.warning(self, "获取位置失败", f"获取放置过渡点位时出错: {e}")
            return None

    def update_put_interim_ui_with_point_data(self, point_data):
        """更新放置过渡点界面上的文本框"""
        try:
            if hasattr(self.teaching_ui, "lineEdit_20"):
                self.teaching_ui.lineEdit_20.setText(self._safe_float_format(point_data['x']))

            if hasattr(self.teaching_ui, "lineEdit_18"):
                self.teaching_ui.lineEdit_18.setText(self._safe_float_format(point_data['y']))

            if hasattr(self.teaching_ui, "lineEdit_17"):
                self.teaching_ui.lineEdit_17.setText(self._safe_float_format(point_data['z']))

            if hasattr(self.teaching_ui, "lineEdit_19"):
                self.teaching_ui.lineEdit_19.setText(self._safe_float_format(point_data['rz']))

            # 强制刷新UI
            QApplication.processEvents()

        except Exception as e:
            self.logger.error(f"更新放置过渡点界面坐标数据时出错: {e}")

    def on_put_interim_point_clicked(self):
        """点击示教放置过渡点按钮的处理函数"""
        try:
            # 检查机器人模式
            if not self._check_manual_mode():
                return

            put_cmd = self.put_interim_point_get()
            if put_cmd:
                # 提示用户点位已保存
                QMessageBox.information(self,
                                        QCoreApplication.translate("TeachPoint", "示教点位成功"),
                                        QCoreApplication.translate("TeachPoint", "当前位置已成功保存为示教点位"))
            else:
                self.logger.warning("获取点位失败，未返回有效命令")
        except Exception as e:
            self.logger.error(f"保存示教点位时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            QMessageBox.warning(self,
                                QCoreApplication.translate("TeachPoint", "示教点位失败"),
                                QCoreApplication.translate("TeachPoint", f"保存点位时出错: {e}"))

    def left_tray_zorn_point(self, save_only=True):
        """获取左托盘区域点位置（只保存不返回）"""
        try:
            raw_pos = self.robot_status.get_pos_values_as_tuple()  # 获取原始位置数据

            if not raw_pos:
                self.logger.warning("获取位置数据失败")
                QMessageBox.warning(self, "获取位置失败", "无法获取当前机器人位置，请确认机器人连接状态")
                return None

            safe_pos = [0.0] * 12
            if raw_pos and len(raw_pos) >= 12:
                for i in range(12):
                    try:
                        safe_pos[i] = float(raw_pos[i]) if raw_pos[i] is not None else 0.0
                    except (ValueError, TypeError):
                        self.logger.warning(f"机器坐标数据中发现非数值或无效值：{raw_pos[i]}，已设为 0.0。")

            safe_pos_tuple = tuple(safe_pos)
            point_position_dict = {
                "x": safe_pos_tuple[0], "y": safe_pos_tuple[1], "z": safe_pos_tuple[2],
                "rx": safe_pos_tuple[3], "ry": safe_pos_tuple[4], "rz": safe_pos_tuple[5],
                "J1": safe_pos_tuple[6], "J2": safe_pos_tuple[7], "J3": safe_pos_tuple[8],
                "J4": safe_pos_tuple[9], "J5": safe_pos_tuple[10], "J6": safe_pos_tuple[11],
                'speed': 20, 'toolNum': 0, 'workPieceNum': 0
            }

            # 保存点位数据
            self.left_tray_zorn_data = point_position_dict

            # 保存到数据库
            self._save_point('left_tray_zorn', point_position_dict)

            # 更新界面文本
            self.update_left_tray_zorn_ui_with_point_data(point_position_dict)

            # 生成命令字符串（尽管不会用于返回，但保存备用）
            left_tray_cmd = RobotCommand.get_command_str('MoveL', point_position_dict, 203)
            self.left_tray_zorn_cmd = left_tray_cmd

            # 显示成功消息
            if not save_only:
                msg = QCoreApplication.translate("PutPoint", "放置过渡点已经定位。位置: {}")
                QMessageBox.information(self,
                                        QCoreApplication.translate("PutPoint", "放置过渡点定位成功"),
                                        msg.format(point_position_dict))

            return left_tray_cmd
        except Exception as e:
            self.logger.error(f"获取左托盘区域点位时出错: {e}")
            QMessageBox.warning(self, "获取位置失败", f"获取左托盘区域点位时出错: {e}")
            return None

    def update_left_tray_zorn_ui_with_point_data(self, point_data):
        """更新左托盘区域点界面上的文本框"""
        try:
            if hasattr(self.teaching_ui, "lineEdit_26"):
                self.teaching_ui.lineEdit_26.setText(self._safe_float_format(point_data['x']))

            if hasattr(self.teaching_ui, "lineEdit_27"):
                self.teaching_ui.lineEdit_27.setText(self._safe_float_format(point_data['y']))

            if hasattr(self.teaching_ui, "lineEdit_28"):
                self.teaching_ui.lineEdit_28.setText(self._safe_float_format(point_data['z']))

            if hasattr(self.teaching_ui, "lineEdit_29"):
                self.teaching_ui.lineEdit_29.setText(self._safe_float_format(point_data['rz']))

            # 强制刷新UI
            QApplication.processEvents()

        except Exception as e:
            self.logger.error(f"更新左托盘区域点界面坐标数据时出错: {e}")

    def on_left_tray_zorn_clicked(self):
        """点击示教左托盘区域点按钮的处理函数"""
        try:
            # 检查机器人模式
            if not self._check_manual_mode():
                return

            left_tray_cmd = self.left_tray_zorn_point(save_only=True)
            if left_tray_cmd:
                # 提示用户点位已保存
                QMessageBox.information(self,
                                        QCoreApplication.translate("TeachPoint", "示教点位成功"),
                                        QCoreApplication.translate("TeachPoint", "当前位置已成功保存为示教点位"))
            else:
                self.logger.warning("获取点位失败，未返回有效命令")
        except Exception as e:
            self.logger.error(f"保存示教点位时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            QMessageBox.warning(self,
                                QCoreApplication.translate("TeachPoint", "示教点位失败"),
                                QCoreApplication.translate("TeachPoint", f"保存点位时出错: {e}"))

    def right_tray_zorn_point(self, save_only=True):
        """获取右托盘区域点位置（只保存不返回）"""
        try:
            raw_pos = self.robot_status.get_pos_values_as_tuple()  # 获取原始位置数据

            if not raw_pos:
                self.logger.warning("获取位置数据失败")
                QMessageBox.warning(self, "获取位置失败", "无法获取当前机器人位置，请确认机器人连接状态")
                return None

            safe_pos = [0.0] * 12
            if raw_pos and len(raw_pos) >= 12:
                for i in range(12):
                    try:
                        safe_pos[i] = float(raw_pos[i]) if raw_pos[i] is not None else 0.0
                    except (ValueError, TypeError):
                        self.logger.warning(f"机器坐标数据中发现非数值或无效值：{raw_pos[i]}，已设为 0.0。")

            safe_pos_tuple = tuple(safe_pos)
            point_position_dict = {
                "x": safe_pos_tuple[0], "y": safe_pos_tuple[1], "z": safe_pos_tuple[2],
                "rx": safe_pos_tuple[3], "ry": safe_pos_tuple[4], "rz": safe_pos_tuple[5],
                "J1": safe_pos_tuple[6], "J2": safe_pos_tuple[7], "J3": safe_pos_tuple[8],
                "J4": safe_pos_tuple[9], "J5": safe_pos_tuple[10], "J6": safe_pos_tuple[11],
                'speed': 20, 'toolNum': 0, 'workPieceNum': 0
            }

            # 保存点位数据
            self.right_tray_zorn_data = point_position_dict

            # 保存到数据库
            self._save_point('right_tray_zorn', point_position_dict)

            # 更新界面文本
            self.update_right_tray_zorn_ui_with_point_data(point_position_dict)

            # 生成命令字符串
            right_tray_cmd = RobotCommand.get_command_str('MoveL', point_position_dict, 203)
            self.right_tray_zorn_cmd = right_tray_cmd

            # 显示成功消息
            if not save_only:
                msg = QCoreApplication.translate("PutPoint", "放置过渡点已经定位。位置: {}")
                QMessageBox.information(self,
                                        QCoreApplication.translate("PutPoint", "放置过渡点定位成功"),
                                        msg.format(point_position_dict))

            return right_tray_cmd
        except Exception as e:
            self.logger.error(f"获取右托盘区域点位时出错: {e}")
            QMessageBox.warning(self, "获取位置失败", f"获取右托盘区域点位时出错: {e}")
            return None


    def update_right_tray_zorn_ui_with_point_data(self, point_data):
        """更新右托盘区域点界面上的文本框"""
        try:
            # 获取右托盘区域点对应的文本框
            if hasattr(self.teaching_ui, "lineEdit_33"):
                self.teaching_ui.lineEdit_33.setText(self._safe_float_format(point_data['x']))

            if hasattr(self.teaching_ui, "lineEdit_32"):
                self.teaching_ui.lineEdit_32.setText(self._safe_float_format(point_data['y']))

            if hasattr(self.teaching_ui, "lineEdit_30"):
                self.teaching_ui.lineEdit_30.setText(self._safe_float_format(point_data['z']))

            if hasattr(self.teaching_ui, "lineEdit_31"):
                self.teaching_ui.lineEdit_31.setText(self._safe_float_format(point_data['rz']))

            # 强制刷新UI
            QApplication.processEvents()

        except Exception as e:
            self.logger.error(f"更新右托盘区域点界面坐标数据时出错: {e}")

    def on_right_tray_zorn_clicked(self):
        """点击示教右托盘区域点按钮的处理函数"""
        try:
            # 检查机器人模式
            if not self._check_manual_mode():
                return

            right_tray_cmd = self.right_tray_zorn_point(save_only=True)
            if right_tray_cmd:
                # 提示用户点位已保存
                QMessageBox.information(self,
                                        QCoreApplication.translate("TeachPoint", "示教点位成功"),
                                        QCoreApplication.translate("TeachPoint", "当前位置已成功保存为示教点位"))
            else:
                self.logger.warning("获取点位失败，未返回有效命令")

        except Exception as e:
            self.logger.error(f"保存示教点位时出错: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            QMessageBox.warning(self,
                                QCoreApplication.translate("TeachPoint", "示教点位失败"),
                                QCoreApplication.translate("TeachPoint", f"保存点位时出错: {e}"))

    def _init_manual_place_buttons(self):
        """初始化抓取/放置界面的按钮连接"""
        try:
            self.setup_UDthreshod_inputs()
            self.setup_Jthreshod_inputs()
            self.setup_TCPthreshod_inputs()

            self.timer = QTimer(self)
            self.timer.timeout.connect(self.update_value)
            self.timer.start(200)
            self.manual_ui.J1Slider.setRange(-175, 175)
            self.manual_ui.J2Slider.setRange(-265, 85)
            self.manual_ui.J3Slider.setRange(-150, 150)
            self.manual_ui.J4Slider.setRange(-85, 265)
            self.manual_ui.J5Slider.setRange(-175, 175)
            self.manual_ui.J6Slider.setRange(-175, 175)

            # 长按通用定时器
            self.single_press_timer = None
            # 连接所有按钮信号到统一处理函数
            #J1
            self.manual_ui.J1DButton.pressed.connect(lambda: self.start_long_press(1, 0))
            self.manual_ui.J1UButton.pressed.connect(lambda: self.start_long_press(1, 1))
            self.manual_ui.J1DButton.released.connect(self.stop_long_press)
            self.manual_ui.J1UButton.released.connect(self.stop_long_press)
            #J2
            self.manual_ui.J2DButton.pressed.connect(lambda: self.start_long_press(2, 0))
            self.manual_ui.J2UButton.pressed.connect(lambda: self.start_long_press(2, 1))
            self.manual_ui.J2DButton.released.connect(self.stop_long_press)
            self.manual_ui.J2UButton.released.connect(self.stop_long_press)
            #J3
            self.manual_ui.J3DButton.pressed.connect(lambda: self.start_long_press(3, 0))
            self.manual_ui.J3UButton.pressed.connect(lambda: self.start_long_press(3, 1))
            self.manual_ui.J3DButton.released.connect(self.stop_long_press)
            self.manual_ui.J3UButton.released.connect(self.stop_long_press)
            #J4
            self.manual_ui.J4DButton.pressed.connect(lambda: self.start_long_press(4, 0))
            self.manual_ui.J4UButton.pressed.connect(lambda: self.start_long_press(4, 1))
            self.manual_ui.J4DButton.released.connect(self.stop_long_press)
            self.manual_ui.J4UButton.released.connect(self.stop_long_press)
            #J5
            self.manual_ui.J5DButton.pressed.connect(lambda: self.start_long_press(5, 0))
            self.manual_ui.J5UButton.pressed.connect(lambda: self.start_long_press(5, 1))
            self.manual_ui.J5DButton.released.connect(self.stop_long_press)
            self.manual_ui.J5UButton.released.connect(self.stop_long_press)
            #J6
            self.manual_ui.J6DButton.pressed.connect(lambda: self.start_long_press(6, 0))
            self.manual_ui.J6UButton.pressed.connect(lambda: self.start_long_press(6, 1))
            self.manual_ui.J6DButton.released.connect(self.stop_long_press)
            self.manual_ui.J6UButton.released.connect(self.stop_long_press)

            # # X
            self.manual_ui.XDButton.pressed.connect(lambda: self.start_long_press_space(1, 0) )
            self.manual_ui.XUButton.pressed.connect(lambda: self.start_long_press_space(1, 1))
            self.manual_ui.XDButton.released.connect(self.stop_long_press)
            self.manual_ui.XUButton.released.connect(self.stop_long_press)

            # Y
            self.manual_ui.YDButton.pressed.connect(lambda: self.start_long_press_space(2, 0))
            self.manual_ui.YUButton.pressed.connect(lambda: self.start_long_press_space(2, 1))
            self.manual_ui.YDButton.released.connect(self.stop_long_press)
            self.manual_ui.YUButton.released.connect(self.stop_long_press)

            # Z
            self.manual_ui.ZDButton.pressed.connect(lambda: self.start_long_press_space(3, 0))
            self.manual_ui.ZUButton.pressed.connect(lambda: self.start_long_press_space(3, 1))
            self.manual_ui.ZDButton.released.connect(self.stop_long_press)
            self.manual_ui.ZUButton.released.connect(self.stop_long_press)

            # RX
            self.manual_ui.RXDButton.pressed.connect(lambda: self.start_long_press_space(4, 0))
            self.manual_ui.RXUButton.pressed.connect(lambda: self.start_long_press_space(4, 1))
            self.manual_ui.RXDButton.released.connect(self.stop_long_press)
            self.manual_ui.RXUButton.released.connect(self.stop_long_press)

            # RY
            self.manual_ui.RYDButton.pressed.connect(lambda: self.start_long_press_space(5, 0))
            self.manual_ui.RYUButton.pressed.connect(lambda: self.start_long_press_space(5, 1))
            self.manual_ui.RYDButton.released.connect(self.stop_long_press)
            self.manual_ui.RYUButton.released.connect(self.stop_long_press)

            # RZ
            self.manual_ui.RZDButton.pressed.connect(lambda: self.start_long_press_space(6, 0))
            self.manual_ui.RZUButton.pressed.connect(lambda: self.start_long_press_space(6, 1))
            self.manual_ui.RZDButton.released.connect(self.stop_long_press)
            self.manual_ui.RZUButton.released.connect(self.stop_long_press)

            # UP
            self.manual_ui.UpButton.pressed.connect(lambda: self.start_lift_platform_control(1))
            self.manual_ui.UpButton.released.connect(self.stop_lift_platform)

            # Down
            self.manual_ui.DownButton.pressed.connect(lambda: self.start_lift_platform_control(0))
            self.manual_ui.DownButton.released.connect(self.stop_lift_platform)

            # # fetching
            # self.manual_ui.fetchingButton.clicked.connect(lambda: self.control_gripper_fetch)
            #
            # # release
            # self.manual_ui.ReleaseButton.pressed.clicked.connect(lambda: self.control_gripper_release)

            # robot已在__init__中初始化，这里不需要重复初始化

        except Exception as e:
            self.logger.error(f"初始化手动操作界面时出错: {e}")

    def update_value(self):

        # 如果程序运行状态不是 1 或者 机器人模式不是 1 则页面的所有按钮不许操作
        self.update_button_states()

        # 定义控件列表，按顺序 J1-J6
        line_edits = [
            self.manual_ui.J1lineEdit,
            self.manual_ui.J2lineEdit,
            self.manual_ui.J3lineEdit,
            self.manual_ui.J4lineEdit,
            self.manual_ui.J5lineEdit,
            self.manual_ui.J6lineEdit,
        ]
        line_edits_tcp = [
            self.manual_ui.XlineEdit,
            self.manual_ui.YlineEdit,
            self.manual_ui.ZlineEdit,
            self.manual_ui.RXlineEdit,
            self.manual_ui.RYlineEdit,
            self.manual_ui.RZlineEdit,
        ]
        sliders = [
            self.manual_ui.J1Slider,
            self.manual_ui.J2Slider,
            self.manual_ui.J3Slider,
            self.manual_ui.J4Slider,
            self.manual_ui.J5Slider,
            self.manual_ui.J6Slider,
        ]

        # 获取状态数据
        positions = [
            self.robot_status.jt_cur_pos1,
            self.robot_status.jt_cur_pos2,
            self.robot_status.jt_cur_pos3,
            self.robot_status.jt_cur_pos4,
            self.robot_status.jt_cur_pos5,
            self.robot_status.jt_cur_pos6,
        ]
        positions_tcp = [
            self.robot_status.tl_cur_pos1,
            self.robot_status.tl_cur_pos2,
            self.robot_status.tl_cur_pos3,
            self.robot_status.tl_cur_pos4,
            self.robot_status.tl_cur_pos5,
            self.robot_status.tl_cur_pos6,
        ]

        # 同步更新 LineEdit 和 Slider
        for line_edit, slider, pos in zip(line_edits, sliders, positions):
            value = str(pos) if pos is not None else "0"
            line_edit.setText(value)
            try:
                slider.setValue(int(float(pos)) if pos is not None else 0)
            except (ValueError, TypeError):
                slider.setValue(0)

        for line_edit_tcp, pos_tcp in zip(line_edits_tcp, positions_tcp):
            value_tcp = str(pos_tcp) if pos_tcp is not None else "0"
            line_edit_tcp.setText(value_tcp)

    def start_long_press(self, axis, direction):
        """开始长按事件，设置当前轴和方向，并启动单次定时器"""
        if self.single_press_timer is not None and self.single_press_timer.isActive():
            return  # 防止重复触发

        self.current_axis = axis
        self.current_direction = direction

        # 设置单次定时器，200ms 后触发 handle_long_press 一次
        self.single_press_timer = QTimer(self)
        self.single_press_timer.setSingleShot(True)  # 单次触发
        self.single_press_timer.timeout.connect(self.handle_long_press)
        self.single_press_timer.start(200)  # 200ms 长按延迟

    def handle_long_press(self):

        """定时器触发时执行对应轴的控制"""
        if self.current_axis == 0:
            return
        # 下发开始运动指令 获取单次长按阈值
        # 单次角度


        max_distance = self.get_valid_number(self.manual_ui.lineEdit_35)

        # 速度 默认30
        vel = 30
        # 加速度 默认180
        acc = 180

        params = {
            'coordSystem': 0,
            'jointNum': self.current_axis,
            'direction': self.current_direction,
            'speed': vel,
            'acceleration': acc,
            'max_distance': max_distance
        }
        info = RobotCommand.get_command_str('StartJOG', params, 232)
        self.send_cmd_to_robot.emit(info)

    def start_long_press_space(self, axis, direction):
        """开始长按事件，设置当前轴和方向，并启动单次定时器"""
        if self.single_press_timer is not None and self.single_press_timer.isActive():
            return  # 防止重复触发

        self.current_axis = axis
        self.current_direction = direction

        # 设置单次定时器，200ms 后触发 handle_long_press 一次
        self.single_press_timer = QTimer(self)
        self.single_press_timer.setSingleShot(True)  # 单次触发
        self.single_press_timer.timeout.connect(self.handle_long_press_space)
        self.single_press_timer.start(200)  # 200ms 长按延迟

    def handle_long_press_space(self):

        """定时器触发时执行对应轴的控制"""
        if self.current_axis == 0:
            return
        # 下发开始运动指令 获取单次长按阈值
        # 单次角度

        max_distance = self.get_valid_number(self.manual_ui.lineEdit_17)

        # 速度 默认30
        vel = 30
        # 加速度 默认180
        acc = 180

        params = {
            'coordSystem': 4,
            'jointNum': self.current_axis,
            'direction': self.current_direction,
            'speed': vel,
            'acceleration': acc,
            'max_distance': max_distance
        }
        info = RobotCommand.get_command_str('StartJOG', params, 232)
        # 使用统一指令管理器发送命令
        command_manager.send_command(info, "坐标运动")

    def stop_long_press(self):
        """停止长按"""
        if self.single_press_timer and self.single_press_timer.isActive():
            self.single_press_timer.stop()

        self.current_axis = 0
        self.current_direction = -1
        # 下发停止运动指令
        info = RobotCommand.get_command_str('StopJOG',  None,233)
        self.send_cmd_to_robot.emit(info)

    def update_button_states(self):
        """统一控制手动操作按钮的启用与禁用状态"""
        enabled = (self.robot_status.program_state == 1 and
                   self.robot_status.robot_mode == 1)

        # 所有需要控制的按钮列表
        buttons = [
            self.manual_ui.J1UButton,
            self.manual_ui.J2UButton,
            self.manual_ui.J3UButton,
            self.manual_ui.J4UButton,
            self.manual_ui.J5UButton,
            self.manual_ui.J6UButton,
            self.manual_ui.J1DButton,
            self.manual_ui.J2DButton,
            self.manual_ui.J3DButton,
            self.manual_ui.J4DButton,
            self.manual_ui.J5DButton,
            self.manual_ui.J6DButton,
            self.manual_ui.XDButton,
            self.manual_ui.XUButton,
            self.manual_ui.YDButton,
            self.manual_ui.YUButton,
            self.manual_ui.ZDButton,
            self.manual_ui.ZUButton,
            self.manual_ui.RXDButton,
            self.manual_ui.RXUButton,
            self.manual_ui.RYDButton,
            self.manual_ui.RYUButton,
            self.manual_ui.RZDButton,
            self.manual_ui.RZUButton,
            self.manual_ui.UpButton,
            self.manual_ui.DownButton,
            self.manual_ui.fetchingButton,
            self.manual_ui.ReleaseButton,
        ]

        for button in buttons:
            button.setEnabled(enabled)

    def setup_Jthreshod_inputs(self):
        """配置数字输入框的国际化支持"""
        # 阈值输入 (0.1-10.0)
        threshold_validator = QDoubleValidator(0.1, 10.0, 2, self.manual_ui.lineEdit_35)
        threshold_validator.setNotation(QDoubleValidator.Notation.StandardNotation)
        self.manual_ui.lineEdit_35.setValidator(threshold_validator)
        self.manual_ui.lineEdit_35.setText("20.0")  # 默认值

    def setup_TCPthreshod_inputs(self):
        """配置数字输入框的国际化支持"""
        # 阈值输入 (0.1-10.0)
        threshold_validator = QDoubleValidator(0.1, 10.0, 2, self.manual_ui.lineEdit_17)
        threshold_validator.setNotation(QDoubleValidator.Notation.StandardNotation)
        self.manual_ui.lineEdit_17.setValidator(threshold_validator)
        self.manual_ui.lineEdit_17.setText("20.0")  # 默认值

    def setup_UDthreshod_inputs(self):
        """配置数字输入框的国际化支持"""
        # 阈值输入 (0.1-10.0)
        threshold_validator = QDoubleValidator(0.1, 10.0, 2, self.manual_ui.lineEdit_34)
        threshold_validator.setNotation(QDoubleValidator.Notation.StandardNotation)
        self.manual_ui.lineEdit_34.setValidator(threshold_validator)
        self.manual_ui.lineEdit_34.setText("20.0")  # 默认值

    def get_valid_number(self, line_edit):
        """安全获取国际化格式的数字"""
        text = line_edit.text().strip().replace(',', '.')
        try:
            return float(text)
        except ValueError:
            logger.warning(f"无效的数字输入: {line_edit.text()}")
            return None

    def start_lift_platform_control(self, direction):
        """
        开始控制升降台
        :param direction: 1表示上升，0表示下降
        """
        try:
            if self.single_press_timer is not None and self.single_press_timer.isActive():
                return  # 防止重复触发

            # 设置IO控制参数
            io_index = 1  # 假设升降台上升使用IO 1
            io_value = 1  # 设置为高电平

            if direction == 0:  # 下降
                io_index = 2  # 假设升降台下降使用IO 2

            # 生成并发送IO控制命令
            params = {
                'io_index': io_index,
                'io_value': io_value
            }
            command = RobotCommand.get_command_str('SetDO', params, 131)
            # 使用统一指令管理器发送命令
            command_manager.send_command(command, "升降台控制")

        except Exception as e:
            self.logger.error(f"控制升降台时出错: {e}")

    def stop_lift_platform(self):
        """停止升降台运动"""
        try:
            if self.single_press_timer and self.single_press_timer.isActive():
                self.single_press_timer.stop()

            # 停止升降台，关闭所有相关IO
            params1 = {'io_index': 1, 'io_value': 0}  # 关闭上升IO
            params2 = {'io_index': 2, 'io_value': 0}  # 关闭下降IO

            command1 = RobotCommand.get_command_str('SetDO', params1, 131)
            command2 = RobotCommand.get_command_str('SetDO', params2, 131)

            # 使用统一指令管理器发送命令
            command_manager.send_command(command1, "停止升降台-上升")
            command_manager.send_command(command2, "停止升降台-下降")

        except Exception as e:
            self.logger.error(f"停止升降台时出错: {e}")

    def control_gripper_fetch(self):
        """控制机械手抓取"""
        try:
            # 通过IO控制抓取
            params = {
                'io_index': 3,  # 假设抓取使用IO 3
                'io_value': 1  # 设置为高电平
            }

            # 生成并发送IO控制命令
            command = RobotCommand.get_command_str('SetDO', params, 131)
            # 使用统一指令管理器发送命令
            command_manager.send_command(command, "机械手抓取")

            # 提示用户
            self.logger.info("发送抓取命令")

            # 延时一段时间后关闭IO信号
            QTimer.singleShot(500, self._reset_gripper_fetch_io)

        except Exception as e:
            self.logger.error(f"控制机械手抓取时出错: {e}")
            QMessageBox.warning(self, "抓取失败", f"控制机械手抓取时出错: {e}")

    def _reset_gripper_fetch_io(self):
        """重置抓取IO信号"""
        try:
            params = {
                'io_index': 3,
                'io_value': 0
            }
            command = RobotCommand.get_command_str('SetDO', params, 131)
            # 使用统一指令管理器发送命令
            command_manager.send_command(command, "重置抓取IO")
        except Exception as e:
            self.logger.error(f"重置抓取IO信号时出错: {e}")

    def control_gripper_release(self):
        """控制机械手释放"""
        try:
            # 通过IO控制释放
            params = {
                'io_index': 4,  # 假设释放使用IO 4
                'io_value': 1  # 设置为高电平
            }

            # 生成并发送IO控制命令
            command = RobotCommand.get_command_str('SetDO', params, 131)
            # 使用统一指令管理器发送命令
            command_manager.send_command(command, "机械手释放")

            # 提示用户
            self.logger.info("发送释放命令")

            # 延时一段时间后关闭IO信号
            QTimer.singleShot(500, self._reset_gripper_release_io)

        except Exception as e:
            self.logger.error(f"控制机械手释放时出错: {e}")
            QMessageBox.warning(self, "释放失败", f"控制机械手释放时出错: {e}")

    def _reset_gripper_release_io(self):
        """重置释放IO信号"""
        try:
            params = {
                'io_index': 4,
                'io_value': 0
            }
            command = RobotCommand.get_command_str('SetDO', params, 131)
            # 使用统一指令管理器发送命令
            command_manager.send_command(command, "重置释放IO")
        except Exception as e:
            self.logger.error(f"重置释放IO信号时出错: {e}")
