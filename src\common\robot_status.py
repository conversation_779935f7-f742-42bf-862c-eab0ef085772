from PySide6.QtCore import QObject
from ..core.robot_8083_socket import Robot8083Socket

"""
    状态数据管理类  使用方式  #使用机器人状态数据
        self.robot_status = RobotStatus()
        print(f"TCP x坐标数据: {self.robot_status.tl_cur_pos1}")
        print(f"TCP y坐标数据: {self.robot_status.tl_cur_pos2}")
        print(f"TCP z坐标数据: {self.robot_status.get_data()['tl_cur_pos3']}")
"""

class RobotStatus(QObject):
    _instance = None
    _data = {}
    _initialized = False  # 标志位，防止重复初始化

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._data = {}
        return cls._instance

    def __init__(self):
        if not self._initialized:
            super().__init__()
            # 初始化连接信号
            Robot8083Socket.get_instance().data_signal.connect(self.set_data)
            self._initialized = True

    def set_data(self, data: dict):
        self._data.update(data)

    # 获取全部数据
    def get_data(self):
        return self._data

    """
     获取单个数据项 有其他的数据需求的可以添加getter方法
    """
    @property
    def program_state(self):
        return self._data.get('program_state')
    
    @property
    def error_code(self):
        return self._data.get('error_code')
    
    @property
    def robot_mode(self):
        return self._data.get('robot_mode')
    
    @property
    def jt_cur_pos1(self):
        return self._data.get('jt_cur_pos1')

    @property
    def jt_cur_pos2(self):
        return self._data.get('jt_cur_pos2')
    
    @property
    def jt_cur_pos3(self):
        return self._data.get('jt_cur_pos3')
    
    @property
    def jt_cur_pos4(self):
        return self._data.get('jt_cur_pos4')
    
    @property
    def jt_cur_pos5(self):
        return self._data.get('jt_cur_pos5')
    
    @property
    def jt_cur_pos6(self):
        return self._data.get('jt_cur_pos6')
    
    @property
    def tl_cur_pos1(self):
        return self._data.get('tl_cur_pos1')
    
    @property
    def tl_cur_pos2(self):
        return self._data.get('tl_cur_pos2')
    
    @property
    def tl_cur_pos3(self):
        return self._data.get('tl_cur_pos3')
    
    @property
    def tl_cur_pos4(self):
        return self._data.get('tl_cur_pos4')
    
    @property
    def tl_cur_pos5(self):
        return self._data.get('tl_cur_pos5')
    
    @property
    def tl_cur_pos6(self):
        return self._data.get('tl_cur_pos6')
    
    @property
    def toolNum(self):
        return self._data.get('toolNum')
    
    @property
    def EmergencyStop(self):
        return self._data.get('EmergencyStop')
    
    @property
    def robot_motion_done(self):
        return self._data.get('robot_motion_done')
    @property
    def program_name(self):
        return self._data.get('program_name')
    @property
    def prog_total_line(self):
        return self._data.get('prog_total_line')
    
    @property
    def prog_cur_line(self):
        return self._data.get('prog_cur_line')

    # 返回TCP的坐标和位姿 元组数据
    def get_pos_values_as_tuple(self):
        return (
            self._data.get('tl_cur_pos1'),
            self._data.get('tl_cur_pos2'),
            self._data.get('tl_cur_pos3'),
            self._data.get('tl_cur_pos4'),
            self._data.get('tl_cur_pos5'),
            self._data.get('tl_cur_pos6'),
            self._data.get('jt_cur_pos1'),
            self._data.get('jt_cur_pos2'),
            self._data.get('jt_cur_pos3'),
            self._data.get('jt_cur_pos4'),
            self._data.get('jt_cur_pos5'),
            self._data.get('jt_cur_pos6')
        )

    # 返回TCP的坐标和位姿 字典数据
    def get_pos_values_as_dict(self):
        return {
            'tl_cur_pos1': self._data.get('tl_cur_pos1'),
            'tl_cur_pos2': self._data.get('tl_cur_pos2'),
            'tl_cur_pos3': self._data.get('tl_cur_pos3'),
            'tl_cur_pos4': self._data.get('tl_cur_pos4'),
            'tl_cur_pos5': self._data.get('tl_cur_pos5'),
            'tl_cur_pos6': self._data.get('tl_cur_pos6'),
            'jt_cur_pos1': self._data.get('jt_cur_pos1'),
            'jt_cur_pos2': self._data.get('jt_cur_pos2'),
            'jt_cur_pos3': self._data.get('jt_cur_pos3'),
            'jt_cur_pos4': self._data.get('jt_cur_pos4'),
            'jt_cur_pos5': self._data.get('jt_cur_pos5'),
            'jt_cur_pos6': self._data.get('jt_cur_pos6')
        }
        
    
    