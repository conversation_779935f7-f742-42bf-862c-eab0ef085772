"""
语言切换
"""
import os
import sys
from PySide6.QtCore import QCoreApplication, QTranslator
from ..common.sql_lite_tool import SqliteTool
from ..common.log import logger

class TranslationService:
    _instance = None
    LANGUAGE_MAP = {"en": "English", "zh": "中文"}  # 语言映射表

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.translator = QTranslator()
            # cls._instance._init_db()  # 初始化数据库
            cls._instance.current_language = cls._instance._load_persisted_language()  # 加载已保存的语言
            cls._instance.load_translation(cls._instance.current_language)  # 加载对应翻译
        return cls._instance

    def _init_db(self):
        """确保系统设置表存在"""
        db = SqliteTool.get_instance()
        db.execute_sql("""
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY DEFAULT 1,
                language TEXT NOT NULL DEFAULT 'zh'
            )
        """)
        # 确保有一条默认记录
        db.execute_sql("""
            INSERT OR IGNORE INTO system_settings (id, language)
            VALUES (1, 'zh')
        """)

    def _load_persisted_language(self):
        """从数据库加载已保存的语言设置"""
        try:
            db = SqliteTool.get_instance()
            record = db.select("system_settings", fields=["language"], condition="id = 1")
            return record[0]["language"] if record else "zh"
        except Exception as e:
            print(f"加载语言设置失败: {e}")
            return "zh"  # 默认为中文

    def load_translation(self, language):
        """加载并应用翻译"""
        app = QCoreApplication.instance()
        if not app:
            return False
        # 移除旧翻译
        app.removeTranslator(self.translator)
        import sys
        import os
        
        def get_resource_path(relative_path):
            # 打包环境：资源在 sys._MEIPASS 临时目录下
            if hasattr(sys, '_MEIPASS'):
                return os.path.join(sys._MEIPASS, relative_path)
            # 开发环境：资源在项目根目录下
            return os.path.join(os.path.abspath('.'), relative_path)

        if language in ("English", "英文", "en"):
            display_lang = "English"
            language_path = get_resource_path("src\\translations\\myproject_en.qm")
            # if self.translator.load(f"translations/myproject_{lang_code}.qm"):
        else:
            display_lang = "中文"
            language_path = get_resource_path("src\\translations\\myproject_zh.qm")

        # logger.info(f"加载语言: {language_path}")
        if self.translator.load(str(language_path)):
            app.installTranslator(self.translator)
            self.current_language = display_lang
            # self._save_language(lang_code)  # 保存语言设置
            self.retranslate_all_windows()
            return True
        return False

    def _save_language(self, lang_code):
        """保存语言设置到数据库"""
        try:
            db = SqliteTool.get_instance()
            db.execute_sql(
                "UPDATE system_settings SET language = ? WHERE id = 1",
                (lang_code,)
            )
        except Exception as e:
            print(f"保存语言设置失败: {e}")

    def retranslate_all_windows(self):
        """重翻译所有窗口"""
        app = QCoreApplication.instance()
        if app:
            for widget in app.allWidgets():
                try:
                    if hasattr(widget, 'retranslateUi'):
                        widget.retranslateUi(widget)
                        widget.update()
                except Exception as e:
                    print(f"翻译窗口 {widget} 时出错: {e}")