import numpy as np
from PySide6.QtWidgets import QWidget
from PySide6.QtGui import QPainter, QColor, QBrush, QPen, QLinearGradient, QFont
from PySide6.QtCore import Qt, QRect

class PalletVisualization(QWidget):
    """平面垛盘可视化组件,固定大小为209px×225px"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        # 垛盘数据模型
        self.rows = 5  # 默认行数
        self.cols = 5  # 默认列数

        # 使用布尔数组表示箱子是否存在
        self.boxes = np.zeros((self.rows, self.cols), dtype=bool)
        
        # 添加箱子编号数组，-1表示没有箱子
        self.box_numbers = np.full((self.rows, self.cols), -1, dtype=int)
        
        # 记录当前最大编号
        self.current_box_number = 0
        
        # 显示网格
        self.show_grid = True
        # 网格线配置
        self.grid_rows = self.rows  # 网格行数，默认与数据模型一致
        self.grid_cols = self.cols  # 网格列数，默认与数据模型一致
        
        # 设置固定大小
        self.setFixedSize(209, 225)
    
    def set_grid_size(self, rows, cols):
        """设置网格大小"""
        if rows > 0 and cols > 0:
            self.rows = rows
            self.cols = cols
            self.boxes = np.zeros((self.rows, self.cols), dtype=bool)
            self.box_numbers = np.full((self.rows, self.cols), -1, dtype=int)
            self.current_box_number = 0
            # 同步网格行列数
            self.grid_rows = rows
            self.grid_cols = cols
            self.update()
            return True
        return False
    
    def set_grid_lines(self, rows, cols):
        """设置网格线的行列数，不影响数据模型和箱子状态
        
        Args:
            rows: 网格行数
            cols: 网格列数
            
        Returns:
            bool: 是否设置成功
        """
        if rows > 0 and cols > 0:
            self.grid_rows = rows
            self.grid_cols = cols
            self.update()
            return True
        return False
    
    def clear_all_boxes(self):
        """清除所有箱子"""
        self.boxes = np.zeros((self.rows, self.cols), dtype=bool)
        self.box_numbers = np.full((self.rows, self.cols), -1, dtype=int)
        self.current_box_number = 0
        self.update()
    
    def forced_full_stack(self):
        """强制满垛"""
        # for row in range(self.rows):
        #     for col in range(self.cols):
        #         self.set_box(row, col, True)
        self.boxes = np.ones((self.rows, self.cols), dtype=bool)
        self.box_numbers = np.full((self.rows, self.cols), 1, dtype=int)
        # self.box_numbers = np.full((self.rows, self.cols), -1, dtype=int)
        self.current_box_number = self.rows * self.cols
        self.update()
    
    def set_box(self, row, col, exists=True):
        """设置指定位置的箱子是否存在"""
        if 0 <= row < self.rows and 0 <= col < self.cols:
            # 如果是新增箱子，分配新编号
            if exists and not self.boxes[row, col]:
                self.box_numbers[row, col] = self.current_box_number
                self.current_box_number += 1
            # 如果是移除箱子，清除编号
            elif not exists and self.boxes[row, col]:
                self.box_numbers[row, col] = -1
            
            self.boxes[row, col] = exists
            self.update()
            return True
        return False
    
    def set_boxes(self, boxes_data):
        """批量设置箱子数据
        
        Args:
            boxes_data: 二维布尔数组或者坐标列表[(row1, col1), (row2, col2), ...]
        """
        if isinstance(boxes_data, np.ndarray) and boxes_data.shape == (self.rows, self.cols):
            # 直接设置整个数组
            self.boxes = boxes_data.astype(bool)
            # 重置编号
            self.box_numbers = np.full((self.rows, self.cols), -1, dtype=int)
            self.current_box_number = 0
            
            # 为所有存在的箱子分配编号
            for row in range(self.rows):
                for col in range(self.cols):
                    if self.boxes[row, col]:
                        self.box_numbers[row, col] = self.current_box_number
                        self.current_box_number += 1
            
            self.update()
            return True
        elif isinstance(boxes_data, list):
            # 先清空所有箱子
            self.boxes = np.zeros((self.rows, self.cols), dtype=bool)
            self.box_numbers = np.full((self.rows, self.cols), -1, dtype=int)
            self.current_box_number = 0
            
            # 根据坐标列表设置箱子
            for pos in boxes_data:
                if len(pos) == 2:
                    row, col = pos
                    if 0 <= row < self.rows and 0 <= col < self.cols:
                        self.boxes[row, col] = True
                        self.box_numbers[row, col] = self.current_box_number
                        self.current_box_number += 1
            
            self.update()
            return True
        return False
    
    
    def paintEvent(self, event):
        """绘制垛盘可视化"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 获取控件尺寸
        width = self.width()
        height = self.height()
        
        # 绘制背景 - 浅蓝色背景
        painter.fillRect(0, 0, width, height, QColor(240, 245, 255))
        
        # 绘制圆角边框
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        painter.drawRoundedRect(0, 0, width-1, height-1, 10, 10)
        
        # 给网格区域留出边距
        margin_x = 10
        margin_y = 10
        grid_width = width - 2 * margin_x
        grid_height = height - 2 * margin_y
        
        # 计算每个格子的大小 - 用于绘制箱子
        box_width = grid_width / self.cols
        box_height = grid_height / self.rows
        
        # 计算每个网格格子的大小 - 用于绘制网格线
        grid_box_width = grid_width / self.grid_cols
        grid_box_height = grid_height / self.grid_rows
        
        # 网格起始位置
        start_x = margin_x
        start_y = margin_y
        
        # 绘制网格
        if self.show_grid:
            # 使用半透明的网格线
            painter.setPen(QPen(QColor(180, 180, 180, 120), 1, Qt.DashLine))
            
            # 绘制垂直线
            for col in range(self.grid_cols + 1):
                x = start_x + col * grid_box_width
                painter.drawLine(int(x), int(start_y), int(x), int(start_y + grid_height))
            
            # 绘制水平线
            for row in range(self.grid_rows + 1):
                y = start_y + row * grid_box_height
                painter.drawLine(int(start_x), int(y), int(start_x + grid_width), int(y))
        
        # 绘制箱子
        for row in range(self.rows):
            for col in range(self.cols):
                if self.boxes[row, col]:
                    # 计算箱子位置
                    x = start_x + col * box_width
                    y = start_y + row * box_height
                    
                    # 绘制箱子，传递箱子编号
                    box_number = self.box_numbers[row, col]
                    self._draw_box(painter, x, y, box_width, box_height, box_number)
    
    def _draw_box(self, painter, x, y, width, height, number):
        """绘制单个箱子并添加编号
        
        Args:
            painter: QPainter对象
            x, y: 箱子左上角坐标
            width, height: 箱子宽高
            number: 箱子编号
        """
        # 创建渐变色
        gradient = QLinearGradient(x, y, x, y + height)
        gradient.setColorAt(0, QColor(30, 100, 200))
        gradient.setColorAt(1, QColor(70, 150, 230))
        
        # 绘制箱子背景
        painter.setBrush(QBrush(gradient))
        painter.setPen(QPen(QColor(20, 80, 180), 1))
        
        # 使箱子稍微小于格子，以便有些间距
        margin = min(width, height) * 0.1
        painter.drawRoundedRect(
            int(x + margin), int(y + margin), 
            int(width - 2*margin), int(height - 2*margin), 
            5, 5  # 圆角
        )
        
        # 绘制箱子编号
        if number >= 0:  # 只绘制有效编号
            # 设置文本颜色和字体
            painter.setPen(QPen(QColor(255, 255, 255), 1))
            font = QFont()
            font.setBold(True)
            font.setPointSize(10)
            painter.setFont(font)
            
            # 创建文本矩形区域
            text_rect = QRect(
                int(x + margin), int(y + margin), 
                int(width - 2*margin), int(height - 2*margin)
            )
            
            # 绘制文本
            painter.drawText(text_rect, Qt.AlignCenter, str(number + 1))  # 从1开始编号
    
    # def mousePressEvent(self, event):
    #     """处理鼠标点击事件"""
    #     width = self.width()
    #     height = self.height()
        
    #     # 给网格区域留出边距
    #     margin_x = 10
    #     margin_y = 10
    #     grid_width = width - 2 * margin_x
    #     grid_height = height - 2 * margin_y
        
    #     # 计算每个格子的大小
    #     box_width = grid_width / self.cols
    #     box_height = grid_height / self.rows
        
    #     # 网格起始位置
    #     start_x = margin_x
    #     start_y = margin_y
        
    #     # 检查点击是否在网格区域内
    #     if (start_x <= event.position().x() <= start_x + grid_width and
    #         start_y <= event.position().y() <= start_y + grid_height):
            
    #         # 计算点击的行列
    #         col = int((event.position().x() - start_x) / box_width)
    #         row = int((event.position().y() - start_y) / box_height)
            
    #         # 确保行列在有效范围内
    #         if 0 <= row < self.rows and 0 <= col < self.cols:
    #             # 切换箱子状态
    #             self.set_box(row, col, not self.boxes[row, col])
 
