from PySide6.QtCore import QTimer, Qt
from PySide6.QtWidgets import QWidget, QVBoxLayout

from ..common.sql_lite_tool import SqliteTool
from ..core.robot_tcp_controller import RobotTcpController
from ..core.palletizer_program import command_manager  # 添加统一指令管理器
from ..core.palletizer_instruction_executor import PalletizerInstructionExecutor
from ..ui.Ui_run import Ui_HomeWidget
from src.common.pallet_data_manager import PalletDataManager
from ..ui.custom_dialogs import CustomDialog, StatusChangeDialog, CustomStartDialog, ResumeWorkDialog
from ..common.log import Logger
from ..core.cmd_tool import RobotCommand  # 指令工具类
from .palletizer_show import PalletVisualization


class RunLogic(QWidget, Ui_HomeWidget):
    def __init__(self):
        super().__init__()
        self.setupUi(self)
        self.db_tool = SqliteTool.get_instance()  # 数据库工具类
        self.robot_controller = RobotTcpController.get_instance()  # 机器人控制器
        self.pallet_manager = PalletDataManager()  # 垛盘数据管理器  操作数据的值

        # 创建PalletVisualizationShow实例
        self.rightRobotWorkspaceShow = PalletVisualization()
        self.leftRobotWorkspaceShow = PalletVisualization()
        self.centerRobotWorkspaceShow = PalletVisualization()  # 新增中央工作区
        # 设置好尺寸
        self.rightRobotWorkspaceShow.setFixedSize(250, 260)
        self.leftRobotWorkspaceShow.setFixedSize(250, 260)

        # 创建布局来装载PalletVisualizationShow
        self.rightLayout = QVBoxLayout()
        self.rightLayout.setContentsMargins(0, 0, 0, 0)
        self.rightLayout.addWidget(self.rightRobotWorkspaceShow)
        self.rightLayout.setAlignment(Qt.AlignCenter)

        self.leftLayout = QVBoxLayout()
        self.leftLayout.setContentsMargins(0, 0, 0, 0)
        self.leftLayout.addWidget(self.leftRobotWorkspaceShow)
        self.leftLayout.setAlignment(Qt.AlignCenter)

        # 设置布局到标签
        self.rightRobotWorkspaceLabel.setLayout(self.rightLayout)
        self.leftRobotWorkspaceLabel.setLayout(self.leftLayout)

        # 隐藏原标签的文本内容
        self.rightRobotWorkspaceLabel.setText("")
        self.leftRobotWorkspaceLabel.setText("")
        self.centerRobotWorkspaceLabel.setText("")  # 隐藏中央标签文本

        # 初始化一些示例数据
        self.rightRobotWorkspaceShow.set_grid_size(4, 6)
        self.leftRobotWorkspaceShow.set_grid_size(6, 4)

        self.rightRobotWorkspaceShow.set_boxes([(0, 0), (2, 1)])

        # 添加当前活动垛盘状态跟踪
        self.current_active_pallet = None  # 'left' 或 'right'

        # 图片路径定义
        self.workspace_images = {
            'left': 'src/resources/run/Robot_working_area_left.png',
            'right': 'src/resources/run/Robot_working_area_right.png',
            'default': 'src/resources/run/Robot_working_area.png'  # 添加默认图片
        }
        # 初始化中心工作区图片
        self.init_center_workspace_image()

        # 垛盘ID常量
        self.LEFT_PALLET_ID = "left_pallet"
        self.RIGHT_PALLET_ID = "right_pallet"

        # 添加码垛相关的基准位置和偏移参数
        self.base_position = None  # 基准位置信息
        self.gripper_orientation = {'rx': 180, 'ry': 0, 'rz': 0}  # 抓手垂直向下的姿态

        # 自定义启动参数存储变量
        self.custom_pallet_id = ""
        self.custom_target_layers = 0
        self.custom_target_parts = 0

        # 系统状态管理
        self.system_status = "stopped"  # stopped, running, paused
        self.is_paused = False
        self.update_control_buttons()

        # 记录启动前的垛盘状态
        self.pre_start_status = {
            self.LEFT_PALLET_ID: None,
            self.RIGHT_PALLET_ID: None
        }

        # 状态映射
        self.status_map = {
            'idle': '空闲',
            'ready': '就绪',
            'working': '作业中',
            'paused': '暂停',
            'full': '满垛'
        }

        # 初始化UI
        self.init_ui()
        self.connect_signals()

        # 添加指令执行器
        self.instruction_executor = PalletizerInstructionExecutor()
        self.connect_executor_signals()

        # 定时器用于刷新数据
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_pallet_data)
        self.refresh_timer.start(1000)  # 每秒刷新一次

    def init_center_workspace_image(self):
        """初始化中心工作区图片"""
        try:
            from PySide6.QtGui import QPixmap
            # 默认显示未运行状态的图片
            default_image_path = self.workspace_images['default']
            pixmap = QPixmap(default_image_path)
            if not pixmap.isNull():
                self.centerRobotWorkspaceLabel.setPixmap(pixmap)
                Logger.info(f"初始化中心工作区图片: {default_image_path}")
            else:
                Logger.warning(f"无法加载默认工作区图片: {default_image_path}")
                self.centerRobotWorkspaceLabel.setText("工作区图片")
        except Exception as e:
            Logger.error(f"初始化中心工作区图片失败: {e}")
            self.centerRobotWorkspaceLabel.setText("工作区图片")

    def update_center_workspace_image(self, pallet_side):
        """更新中心工作区图片

        Args:
            pallet_side (str): 'left'、'right' 或 'default'
        """
        try:
            from PySide6.QtGui import QPixmap

            if pallet_side not in self.workspace_images:
                Logger.warning(f"无效的垛盘侧面: {pallet_side}")
                return False

            # 如果当前活动垛盘没有变化，不需要更新
            if self.current_active_pallet == pallet_side:
                return True

            image_path = self.workspace_images[pallet_side]
            pixmap = QPixmap(image_path)

            if not pixmap.isNull():
                self.centerRobotWorkspaceLabel.setPixmap(pixmap)
                self.current_active_pallet = pallet_side

                if pallet_side == 'left':
                    pallet_name = "左侧托盘"
                elif pallet_side == 'right':
                    pallet_name = "右垛盘"
                else:
                    pallet_name = "默认工作区"

                Logger.info(f"切换中心工作区图片: {pallet_name} - {image_path}")
                return True
            else:
                Logger.error(f"无法加载工作区图片: {image_path}")
                return False

        except Exception as e:
            Logger.error(f"更新中心工作区图片失败: {e}")
            return False

    def set_active_pallet(self, pallet_id):
        """设置当前活动的垛盘并更新中心工作区图片

        Args:
            pallet_id (str): 垛盘ID，如 'left_pallet' 或 'right_pallet'
        """
        try:
            # 根据垛盘ID确定侧面
            if 'left' in pallet_id.lower():
                pallet_side = 'left'
            elif 'right' in pallet_id.lower():
                pallet_side = 'right'
            else:
                Logger.warning(f"无法识别垛盘侧面: {pallet_id}")
                return False

            # 更新中心工作区图片
            return self.update_center_workspace_image(pallet_side)

        except Exception as e:
            Logger.error(f"设置活动垛盘失败: {e}")
            return False

    def get_selected_pallet(self):
        """获取当前选择的垛盘

        Returns:
            str: 垛盘ID，如 'left_pallet' 或 'right_pallet'
        """
        # 这里需要根据您的具体业务逻辑来实现
        # 可能需要检查UI状态、数据库状态或其他条件来确定当前活动的垛盘

        # 示例实现（需要根据实际情况调整）:
        try:
            # 检查左右垛盘的状态，确定哪个是当前活动的
            left_status = self.pallet_manager.get_pallet_status('left_pallet')
            right_status = self.pallet_manager.get_pallet_status('right_pallet')

            # 根据状态判断当前活动垛盘
            if left_status and left_status.get('is_active', False):
                return 'left_pallet'
            elif right_status and right_status.get('is_active', False):
                return 'right_pallet'
            else:
                # 默认返回左侧垛盘
                return 'left_pallet'

        except Exception as e:
            Logger.error(f"获取选择垛盘失败: {e}")
            return 'left_pallet'  # 默认返回左侧

    def connect_signals(self):
        """连接信号槽"""
        # 左垛盘控制按钮
        self.leftReadyButton.clicked.connect(lambda: self.set_pallet_ready(self.LEFT_PALLET_ID))
        self.leftMaintenanceButton.clicked.connect(lambda: self.force_full_pallet(self.LEFT_PALLET_ID))
        self.leftResetButton.clicked.connect(lambda: self.reset_pallet_data(self.LEFT_PALLET_ID))
        self.leftClearanceToggle.clicked.connect(lambda: self.toggle_cancel_palletizing(self.LEFT_PALLET_ID))

        # 右垛盘控制按钮
        self.rightReadyButton.clicked.connect(lambda: self.set_pallet_ready(self.RIGHT_PALLET_ID))
        self.rightMaintenanceButton.clicked.connect(lambda: self.force_full_pallet(self.RIGHT_PALLET_ID))
        self.rightResetButton.clicked.connect(lambda: self.reset_pallet_data(self.RIGHT_PALLET_ID))
        self.rightClearanceToggle.clicked.connect(lambda: self.toggle_cancel_palletizing(self.RIGHT_PALLET_ID))

        # 主控制按钮
        self.startButton.clicked.connect(self.on_start_button_clicked)
        self.stopButton.clicked.connect(self.on_stop_button_clicked)
        self.pauseButton.clicked.connect(self.on_pause_button_clicked)
        self.systemResetButton.clicked.connect(self.on_system_reset_button_clicked)

        # 自定义启动按钮
        self.customAutoButton.clicked.connect(self.show_custom_start_dialog)

    def connect_executor_signals(self):
        """连接指令执行器信号"""
        self.instruction_executor.execution_started.connect(self.on_execution_started)
        self.instruction_executor.execution_progress.connect(self.on_execution_progress)
        self.instruction_executor.execution_finished.connect(self.on_execution_finished)
        self.instruction_executor.error_occurred.connect(self.on_execution_error)

    def on_start_button_clicked(self):
        """开始按钮点击事件"""
        try:
            Logger.info("=== 开始按钮被点击 ===")

            # 检测就绪的垛盘
            ready_pallets = self.get_ready_pallets()
            if not ready_pallets:
                CustomDialog.show_warning(
                    self,
                    "无就绪垛盘",
                    "没有检测到就绪状态的垛盘，请先设置垛盘为就绪状态。"
                )
                return

            Logger.info(f"检测到就绪垛盘: {[self.get_pallet_name(pid) for pid in ready_pallets]}")

            # 为每个就绪垛盘准备配置和生成指令
            all_instructions = {}
            total_instruction_count = 0

            for pallet_id in ready_pallets:
                pallet_config = self.prepare_pallet_config_for_pallet(pallet_id)
                recipe_data = self.prepare_recipe_data()
                teaching_data = self.prepare_teaching_data()

                if pallet_config and teaching_data:
                    # 生成该垛盘的指令序列
                    instruction_sequence = self.instruction_executor.generate_instruction_preview(
                        pallet_config, recipe_data, teaching_data, pallet_id
                    )

                    if instruction_sequence:
                        all_instructions[pallet_id] = instruction_sequence
                        total_instruction_count += len(instruction_sequence)
                        Logger.info(f"{self.get_pallet_name(pallet_id)} 生成 {len(instruction_sequence)} 条指令")

            if all_instructions:
                # 显示所有垛盘的指令预览
                pallet_names = [self.get_pallet_name(pid) for pid in all_instructions.keys()]
                reply = CustomDialog.show_question(
                    self,
                    "多垛盘指令序列已生成",
                    f"已为以下垛盘生成指令：\n{', '.join(pallet_names)}\n\n"
                    f"总计 {total_instruction_count} 条指令\n是否开始执行码垛作业？"
                )

                if reply:
                    Logger.info("用户确认开始执行多垛盘作业")
                    self.start_multi_pallet_execution(all_instructions)
                else:
                    Logger.info("用户取消执行")
            else:
                Logger.error("所有垛盘的指令序列生成失败")

        except Exception as e:
            Logger.error(f"开始按钮处理失败: {e}")

    def on_stop_button_clicked(self):
        """停止按钮点击事件"""
        if self.system_status == "stopped":
            CustomDialog.show_warning(
                self,
                "系统状态提示",
                "系统已经处于停止状态。"
            )
            return

        # 显示停止确认对话框
        if CustomDialog.show_question(
                self,
                "确认停止系统",
                "确定要停止码垛系统吗？\n\n停止后：\n• 机械臂将完成当前动作后停止\n• 所有作业将被中断\n• 需要重新启动才能继续作业\n\n确认停止？"
        ):
            # 停止码垛作业
            self.stop_palletizing()

            # 使用统一指令管理器发送停止指令
            success = command_manager.send_stop_command()
            if success:
                self.system_status = "stopped"
                self.is_paused = False

                # 恢复启动前的状态
                for pallet_id, pre_status in self.pre_start_status.items():
                    if pre_status == 'ready':
                        self.pallet_manager.update_pallet_status(pallet_id, "ready")
                    elif pre_status is not None:
                        self.pallet_manager.update_pallet_status(pallet_id, pre_status)

                # 清空状态记录
                self.pre_start_status = {
                    self.LEFT_PALLET_ID: None,
                    self.RIGHT_PALLET_ID: None
                }

                # 恢复默认工作区图片
                self.update_center_workspace_image('default')

                self.update_control_buttons()
            else:
                CustomDialog.show_error(self, "操作失败", "停止指令发送失败")

    def on_pause_button_clicked(self):
        """暂停/继续按钮点击事件"""
        if self.system_status == "stopped":
            CustomDialog.show_warning(
                self,
                "操作受限",
                "系统未启动，无法执行暂停操作。\n请先启动系统。"
            )
            return

        if not self.is_paused:
            # 当前未暂停，执行暂停操作
            if CustomDialog.show_question(
                    self,
                    "确认暂停系统",
                    "确定要暂停码垛系统吗？\n\n暂停后：\n• 机械臂将完成当前动作后暂停\n• 可以点击继续按钮恢复作业\n• 暂停期间可以进行安全检查\n\n确认暂停？"
            ):
                # 暂停码垛作业
                self.pause_palletizing()

                # 使用统一指令管理器发送暂停指令
                success = command_manager.send_pause_command()
                if success:
                    self.is_paused = True
                    self.system_status = "paused"

                    # 只更新正在作业的垛盘为暂停状态
                    left_pallet = self.pallet_manager.get_pallet_status(self.LEFT_PALLET_ID)
                    right_pallet = self.pallet_manager.get_pallet_status(self.RIGHT_PALLET_ID)

                    if left_pallet and left_pallet['status'] == 'working':
                        self.pallet_manager.update_pallet_status(self.LEFT_PALLET_ID, "paused")
                    if right_pallet and right_pallet['status'] == 'working':
                        self.pallet_manager.update_pallet_status(self.RIGHT_PALLET_ID, "paused")

                    self.update_control_buttons()
                else:
                    CustomDialog.show_error(self, "操作失败", "暂停指令发送失败")
        else:
            # 当前已暂停，执行继续操作
            if CustomDialog.show_question(
                    self,
                    "确认继续作业",
                    "确定要继续码垛作业吗？\n\n继续后：\n• 系统将从暂停点恢复作业\n• 机械臂将继续执行码垛任务\n• 请确保工作区域安全\n\n确认继续？"
            ):
                # 恢复码垛作业
                self.resume_palletizing()

                # 使用统一指令管理器发送继续指令
                success = command_manager.send_resume_command()
                if success:
                    self.is_paused = False
                    self.system_status = "running"

                    # 只更新暂停状态的垛盘为作业中
                    left_pallet = self.pallet_manager.get_pallet_status(self.LEFT_PALLET_ID)
                    right_pallet = self.pallet_manager.get_pallet_status(self.RIGHT_PALLET_ID)

                    if left_pallet and left_pallet['status'] == 'paused':
                        self.pallet_manager.update_pallet_status(self.LEFT_PALLET_ID, "working")
                    if right_pallet and right_pallet['status'] == 'paused':
                        self.pallet_manager.update_pallet_status(self.RIGHT_PALLET_ID, "working")

                    self.update_control_buttons()
                else:
                    CustomDialog.show_error(self, "操作失败", "恢复指令发送失败")

    def on_system_reset_button_clicked(self):
        """系统复位按钮点击事件"""
        # 显示复位确认对话框
        if CustomDialog.show_question(
                self,
                "确认系统复位",
                "确定要执行系统复位吗？\n\n复位操作将：\n• 机械臂回到初始位置\n• 升降平台回到初始位置\n• 清除当前作业状态\n\n【重要提示】\n请确认初始位置已正确定义！\n\n确认执行复位？"
        ):
            # 执行复位操作
            self.system_status = "stopped"
            self.is_paused = False
            self.update_control_buttons()

            # 发送复位指令到指定位置
            reset_cmd = "/f/bIII201III201III143IIIMoveJ(-29.633,-115.488,-114.97,-39.805,89.351,-151.559,414.819,-354.615,130.57,179.922,0.695,31.927,1,0,100.0,100,100,0,0,0,0,10,0,0,0,0,0,0,0)III/b/f"
            success = command_manager.send_command(reset_cmd, "系统复位")

            if success:
                Logger.info(f"系统复位指令发送成功")
            else:
                CustomDialog.show_error(self, "操作失败", "复位指令发送失败")

    def update_control_buttons(self):
        """更新控制按钮的显示状态"""
        if self.system_status == "stopped":
            # 停止状态
            self.startButton.setText("启动")
            self.startButton.setStyleSheet(
                "QPushButton { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #4F8EF7, stop:0.1 #4285F4, stop:0.5 #3b82f6, stop:0.9 #2563EB, stop:1 #1D4ED8); "
                "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
                "border: 1px solid #1E40AF; "
                "border-top: 1px solid #60A5FA; "
                "border-left: 1px solid #60A5FA; "
                "border-radius: 6px; "
                "padding: 2px 8px; "
                "} "
                "QPushButton:hover { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #60A5FA, stop:0.1 #4F8EF7, stop:0.5 #4285F4, stop:0.9 #3B82F6, stop:1 #2563EB); "
                "} "
                "QPushButton:pressed { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #1E40AF, stop:0.1 #2563EB, stop:0.5 #3B82F6, stop:0.9 #4285F4, stop:1 #60A5FA); "
                "padding-top: 3px; padding-left: 9px; "
                "}"
            )
            self.startButton.setEnabled(True)

            self.stopButton.setText("停止")
            self.stopButton.setStyleSheet(
                "QPushButton { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #7C8B9A, stop:0.1 #71808F, stop:0.5 #6b7280, stop:0.9 #5B6470, stop:1 #4B5563); "
                "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
                "border: 1px solid #374151; "
                "border-top: 1px solid #9CA3AF; "
                "border-left: 1px solid #9CA3AF; "
                "border-radius: 6px; "
                "padding: 2px 8px; "
                "}"
            )
            self.stopButton.setEnabled(False)

            self.pauseButton.setText("暂停")
            self.pauseButton.setStyleSheet(
                "QPushButton { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #7C8B9A, stop:0.1 #71808F, stop:0.5 #6b7280, stop:0.9 #5B6470, stop:1 #4B5563); "
                "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
                "border: 1px solid #374151; "
                "border-top: 1px solid #9CA3AF; "
                "border-left: 1px solid #9CA3AF; "
                "border-radius: 6px; "
                "padding: 2px 8px; "
                "}"
            )
            self.pauseButton.setEnabled(False)

        elif self.system_status == "running":
            # 运行状态
            self.startButton.setText("运行中")
            self.startButton.setStyleSheet(
                "QPushButton { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #34D399, stop:0.1 #10B981, stop:0.5 #22c55e, stop:0.9 #16A34A, stop:1 #15803D); "
                "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
                "border: 1px solid #166534; "
                "border-top: 1px solid #6EE7B7; "
                "border-left: 1px solid #6EE7B7; "
                "border-radius: 6px; "
                "padding: 2px 8px; "
                "}"
            )
            self.startButton.setEnabled(False)

            self.stopButton.setText("停止")
            self.stopButton.setStyleSheet(
                "QPushButton { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #F87171, stop:0.1 #EF4444, stop:0.5 #DC2626, stop:0.9 #B91C1C, stop:1 #991B1B); "
                "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
                "border: 1px solid #7F1D1D; "
                "border-top: 1px solid #FCA5A5; "
                "border-left: 1px solid #FCA5A5; "
                "border-radius: 6px; "
                "padding: 2px 8px; "
                "} "
                "QPushButton:hover { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #FCA5A5, stop:0.1 #F87171, stop:0.5 #EF4444, stop:0.9 #DC2626, stop:1 #B91C1C); "
                "} "
                "QPushButton:pressed { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #7F1D1D, stop:0.1 #991B1B, stop:0.5 #B91C1C, stop:0.9 #DC2626, stop:1 #EF4444); "
                "padding-top: 3px; padding-left: 9px; "
                "}"
            )
            self.stopButton.setEnabled(True)

            self.pauseButton.setText("暂停")
            self.pauseButton.setStyleSheet(
                "QPushButton { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #FBBF24, stop:0.1 #F59E0B, stop:0.5 #D97706, stop:0.9 #B45309, stop:1 #92400E); "
                "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
                "border: 1px solid #78350F; "
                "border-top: 1px solid #FDE68A; "
                "border-left: 1px solid #FDE68A; "
                "border-radius: 6px; "
                "padding: 2px 8px; "
                "} "
                "QPushButton:hover { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #FDE68A, stop:0.1 #FBBF24, stop:0.5 #F59E0B, stop:0.9 #D97706, stop:1 #B45309); "
                "} "
                "QPushButton:pressed { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #78350F, stop:0.1 #92400E, stop:0.5 #B45309, stop:0.9 #D97706, stop:1 #F59E0B); "
                "padding-top: 3px; padding-left: 9px; "
                "}"
            )
            self.pauseButton.setEnabled(True)

        elif self.system_status == "paused":
            # 暂停状态
            self.startButton.setText("运行中")
            self.startButton.setStyleSheet(
                "QPushButton { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #7C8B9A, stop:0.1 #71808F, stop:0.5 #6b7280, stop:0.9 #5B6470, stop:1 #4B5563); "
                "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
                "border: 1px solid #374151; "
                "border-top: 1px solid #9CA3AF; "
                "border-left: 1px solid #9CA3AF; "
                "border-radius: 6px; "
                "padding: 2px 8px; "
                "}"
            )
            self.startButton.setEnabled(False)

            self.stopButton.setText("停止")
            self.stopButton.setStyleSheet(
                "QPushButton { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #F87171, stop:0.1 #EF4444, stop:0.5 #DC2626, stop:0.9 #B91C1C, stop:1 #991B1B); "
                "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
                "border: 1px solid #7F1D1D; "
                "border-top: 1px solid #FCA5A5; "
                "border-left: 1px solid #FCA5A5; "
                "border-radius: 6px; "
                "padding: 2px 8px; "
                "} "
                "QPushButton:hover { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #FCA5A5, stop:0.1 #F87171, stop:0.5 #EF4444, stop:0.9 #DC2626, stop:1 #B91C1C); "
                "} "
                "QPushButton:pressed { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #7F1D1D, stop:0.1 #991B1B, stop:0.5 #B91C1C, stop:0.9 #DC2626, stop:1 #EF4444); "
                "padding-top: 3px; padding-left: 9px; "
                "}"
            )
            self.stopButton.setEnabled(True)

            self.pauseButton.setText("继续")
            self.pauseButton.setStyleSheet(
                "QPushButton { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #34D399, stop:0.1 #10B981, stop:0.5 #22c55e, stop:0.9 #16A34A, stop:1 #15803D); "
                "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
                "border: 1px solid #166534; "
                "border-top: 1px solid #6EE7B7; "
                "border-left: 1px solid #6EE7B7; "
                "border-radius: 6px; "
                "padding: 2px 8px; "
                "} "
                "QPushButton:hover { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #6EE7B7, stop:0.1 #34D399, stop:0.5 #10B981, stop:0.9 #22c55e, stop:1 #16A34A); "
                "} "
                "QPushButton:pressed { "
                "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
                "stop:0 #166534, stop:0.1 #15803D, stop:0.5 #16A34A, stop:0.9 #22c55e, stop:1 #10B981); "
                "padding-top: 3px; padding-left: 9px; "
                "}"
            )
            self.pauseButton.setEnabled(True)

        # 系统复位按钮始终可用 - 保持现有的立体效果
        self.systemResetButton.setText("复位")
        self.systemResetButton.setStyleSheet(
            "QPushButton { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #8A9099, stop:0.1 #7A8088, stop:0.5 #6E737B, stop:0.9 #9CA2A8, stop:1 #A7ADB6); "
            "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
            "border: 1px solid #4A4F55; "
            "border-top: 1px solid #9CA2A8; "
            "border-left: 1px solid #9CA2A8; "
            "border-radius: 4px; "
            "padding: 2px 6px; "
            "} "
            "QPushButton:hover { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #9AA1A9, stop:0.1 #8A9099, stop:0.5 #7E838B, stop:0.9 #ACB2B8, stop:1 #B7BDC6); "
            "} "
            "QPushButton:pressed { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #5E636B, stop:0.1 #6E737B, stop:0.5 #7A8088, stop:0.9 #8A9099, stop:1 #9CA2A8); "
            "border: 1px solid #9CA2A8; "
            "border-top: 1px solid #4A4F55; "
            "border-left: 1px solid #4A4F55; "
            "padding-top: 3px; padding-left: 7px; "
            "}"
        )
        self.systemResetButton.setEnabled(True)

        # # 系统复位按钮始终可用
        # self.systemResetButton.setText("复位")
        # self.systemResetButton.setStyleSheet("background-color: #9EA4AC; min-height: 32px; font-size: 12px; color: white;")
        # self.systemResetButton.setEnabled(True)

        # 自定义启动
        self.customAutoButton.setStyleSheet(
            "QPushButton { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #3BC5C4, stop:0.1 #2FB8B7, stop:0.5 #27B4B3, stop:0.9 #1FA5A4, stop:1 #179695); "
            "min-height: 24px; font-size: 12px; color: white; font-weight: bold; "
            "border: 1px solid #0F7A79; "
            "border-top: 1px solid #5DD5D4; "
            "border-left: 1px solid #5DD5D4; "
            "border-radius: 6px; "
            "padding: 2px 8px; "
            "} "
            "QPushButton:hover { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #5DD5D4, stop:0.1 #3BC5C4, stop:0.5 #2FB8B7, stop:0.9 #27B4B3, stop:1 #1FA5A4); "
            "} "
            "QPushButton:pressed { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #0F7A79, stop:0.1 #179695, stop:0.5 #1FA5A4, stop:0.9 #27B4B3, stop:1 #2FB8B7); "
            "padding-top: 3px; padding-left: 9px; "
            "}"
        )
        # 左右垛盘按钮
        # 为左垛盘就绪按钮添加立体效果
        self.leftReadyButton.setStyleSheet(
            "QPushButton { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #34D399, stop:0.1 #10B981, stop:0.5 #22c55e, stop:0.9 #16A34A, stop:1 #15803D); "
            "min-height: 22px; font-size: 12px; color: white; font-weight: bold; "
            "border: 1px solid #166534; "
            "border-top: 1px solid #6EE7B7; "
            "border-left: 1px solid #6EE7B7; "
            "border-radius: 6px; "
            "padding: 2px 8px; "
            "} "
            "QPushButton:hover { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #6EE7B7, stop:0.1 #34D399, stop:0.5 #10B981, stop:0.9 #059669, stop:1 #047857); "
            "} "
            "QPushButton:pressed { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #047857, stop:0.1 #059669, stop:0.5 #10B981, stop:0.9 #16A34A, stop:1 #22C55E); "
            "padding-top: 3px; padding-left: 9px; "
            "}"
        )

        # 为右垛盘就绪按钮添加立体效果
        self.rightReadyButton.setStyleSheet(
            "QPushButton { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #34D399, stop:0.1 #10B981, stop:0.5 #22c55e, stop:0.9 #16A34A, stop:1 #15803D); "
            "min-height: 22px; font-size: 12px; color: white; font-weight: bold; "
            "border: 1px solid #166534; "
            "border-top: 1px solid #6EE7B7; "
            "border-left: 1px solid #6EE7B7; "
            "border-radius: 6px; "
            "padding: 2px 8px; "
            "} "
            "QPushButton:hover { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #6EE7B7, stop:0.1 #34D399, stop:0.5 #10B981, stop:0.9 #059669, stop:1 #047857); "
            "} "
            "QPushButton:pressed { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #047857, stop:0.1 #059669, stop:0.5 #10B981, stop:0.9 #16A34A, stop:1 #22C55E); "
            "padding-top: 3px; padding-left: 9px; "
            "}"
        )

        # 为左垛盘强制满垛按钮添加立体效果
        self.leftMaintenanceButton.setStyleSheet(
            "QPushButton { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #FBBF24, stop:0.1 #F59E0B, stop:0.5 #D97706, stop:0.9 #B45309, stop:1 #92400E); "
            "min-height: 22px; font-size: 12px; color: white; font-weight: bold; "
            "border: 1px solid #78350F; "
            "border-top: 1px solid #FDE68A; "
            "border-left: 1px solid #FDE68A; "
            "border-radius: 6px; "
            "padding: 2px 8px; "
            # "text-shadow: 1px 1px 2px rgba(0,0,0,0.3); "
            # "box-shadow: 0px 2px 4px rgba(0,0,0,0.2), "
            "inset 0px 1px 0px rgba(255,255,255,0.2); "
            "} "
            "QPushButton:hover { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #FDE68A, stop:0.1 #FBBF24, stop:0.5 #F59E0B, stop:0.9 #D97706, stop:1 #B45309); "
            "} "
            "QPushButton:pressed { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #78350F, stop:0.1 #92400E, stop:0.5 #B45309, stop:0.9 #D97706, stop:1 #F59E0B); "
            # "box-shadow: inset 0px 1px 2px rgba(0,0,0,0.2); "
            "padding-top: 3px; padding-left: 9px; "
            "}"
        )

        # 为右垛盘强制满垛按钮添加立体效果
        self.rightMaintenanceButton.setStyleSheet(
            "QPushButton { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #FBBF24, stop:0.1 #F59E0B, stop:0.5 #D97706, stop:0.9 #B45309, stop:1 #92400E); "
            "min-height: 22px; font-size: 12px; color: white; font-weight: bold; "
            "border: 1px solid #78350F; "
            "border-top: 1px solid #FDE68A; "
            "border-left: 1px solid #FDE68A; "
            "border-radius: 6px; "
            "padding: 2px 8px; "
            # "text-shadow: 1px 1px 2px rgba(0,0,0,0.3); "
            # "box-shadow: 0px 2px 4px rgba(0,0,0,0.2), "
            "inset 0px 1px 0px rgba(255,255,255,0.2); "
            "} "
            "QPushButton:hover { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #FDE68A, stop:0.1 #FBBF24, stop:0.5 #F59E0B, stop:0.9 #D97706, stop:1 #B45309); "
            "} "
            "QPushButton:pressed { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #78350F, stop:0.1 #92400E, stop:0.5 #B45309, stop:0.9 #D97706, stop:1 #F59E0B); "
            # "box-shadow: inset 0px 1px 2px rgba(0,0,0,0.2); "
            "padding-top: 3px; padding-left: 9px; "
            "}"
        )

        # 为左垛盘重置按钮添加立体效果 - 使用#0D57A4蓝色渐变
        self.leftResetButton.setStyleSheet(
            "QPushButton { "

            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #1E6BC5, stop:0.1 #1560B8, stop:0.5 #0D57A4, stop:0.9 #0A4D91, stop:1 #08437E); "
            "min-height: 22px; font-size: 12px; color: white; font-weight: bold; "
            "border: 1px solid #06396B; "
            "border-top: 1px solid #4A8FD6; "
            "border-left: 1px solid #4A8FD6; "
            "border-radius: 6px; "
            "padding: 2px 8px; "
            # "text-shadow: 1px 1px 2px rgba(0,0,0,0.3); "
            # "box-shadow: 0px 2px 4px rgba(0,0,0,0.2), "
            "inset 0px 1px 0px rgba(255,255,255,0.2); "
            "} "
            "QPushButton:hover { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #4A8FD6, stop:0.1 #1E6BC5, stop:0.5 #1560B8, stop:0.9 #0D57A4, stop:1 #0A4D91); "
            "} "
            "QPushButton:pressed { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #06396B, stop:0.1 #08437E, stop:0.5 #0A4D91, stop:0.9 #0D57A4, stop:1 #1560B8); "
            # "box-shadow: inset 0px 1px 2px rgba(0,0,0,0.2); "
            "padding-top: 3px; padding-left: 9px; "
            "}"
        )

        # 为右垛盘重置按钮添加立体效果 - 使用#0D57A4蓝色渐变
        self.rightResetButton.setStyleSheet(
            "QPushButton { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #1E6BC5, stop:0.1 #1560B8, stop:0.5 #0D57A4, stop:0.9 #0A4D91, stop:1 #08437E); "
            "min-height: 22px; font-size: 12px; color: white; font-weight: bold; "
            "border: 1px solid #06396B; "
            "border-top: 1px solid #4A8FD6; "
            "border-left: 1px solid #4A8FD6; "
            "border-radius: 6px; "
            "padding: 2px 8px; "
            # "text-shadow: 1px 1px 2px rgba(0,0,0,0.3); "
            # "box-shadow: 0px 2px 4px rgba(0,0,0,0.2), "
            "inset 0px 1px 0px rgba(255,255,255,0.2); "
            "} "
            "QPushButton:hover { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #4A8FD6, stop:0.1 #1E6BC5, stop:0.5 #1560B8, stop:0.9 #0D57A4, stop:1 #0A4D91); "
            "} "
            "QPushButton:pressed { "
            "background: qlineargradient(x1:0, y1:0, x2:0, y2:1, "
            "stop:0 #06396B, stop:0.1 #08437E, stop:0.5 #0A4D91, stop:0.9 #0D57A4, stop:1 #1560B8); "
            # "box-shadow: inset 0px 1px 2px rgba(0,0,0,0.2); "
            "padding-top: 3px; padding-left: 9px; "
            "}"
        )

    def init_ui(self):
        """初始化UI显示"""
        self.refresh_pallet_data()

    def stop_palletizing(self):
        """停止码垛作业"""
        try:
            # 停止指令执行器
            self.instruction_executor.stop_palletizing()
            Logger.info("码垛作业已停止")
        except Exception as e:
            Logger.error(f"停止码垛作业失败: {e}")

    def pause_palletizing(self):
        """暂停码垛作业"""
        try:
            # 暂停指令执行器
            self.instruction_executor.pause_palletizing()
            Logger.info("码垛作业已暂停")
        except Exception as e:
            Logger.error(f"暂停码垛作业失败: {e}")

    def resume_palletizing(self):
        """恢复码垛作业"""
        try:
            # 恢复指令执行器
            self.instruction_executor.resume_palletizing()
            Logger.info("码垛作业已恢复")
        except Exception as e:
            Logger.error(f"恢复码垛作业失败: {e}")
        self.update_control_buttons()  # 初始化按钮状态

    def show_custom_start_dialog(self):
        """显示自定义启动对话框"""
        dialog = CustomStartDialog(self)

        if dialog.exec():
            if dialog.result_accepted:
                settings = dialog.get_settings()

                # 存储用户输入的参数到类变量中
                self.custom_pallet_id = settings['pallet_id']
                self.custom_target_layers = settings['target_layers']
                self.custom_target_parts = settings['target_parts']

                # 显示确认启动对话框
                pallet_name = "左垛盘" if self.custom_pallet_id == "left_pallet" else "右垛盘"

                if CustomDialog.show_question(
                        self,
                        "确认自定义启动",
                        f"自定义启动参数：\n\n"
                        f"• 选择垛盘：{pallet_name}\n"
                        f"• 层数：{self.custom_target_layers}\n"
                        f"• 工作号：{self.custom_target_parts}\n\n"
                        f"确定要启动码垛系统吗？\n\n"
                        f"启动后系统将开始自动作业，请确保：\n"
                        f"• 所有安全防护措施已到位\n"
                        f"• 垛盘已正确放置\n"
                        f"• 机械臂工作区域无人员\n\n"
                        f"确认启动？"
                ):
                    # 执行启动逻辑 - 与普通启动按钮相同的逻辑
                    if self.system_status == "running":
                        CustomDialog.show_warning(
                            self,
                            "系统状态提示",
                            "系统已经在运行中，无需重复启动。"
                        )
                        return

                    # 检查指定垛盘状态是否为就绪
                    target_pallet = self.pallet_manager.get_pallet_status(self.custom_pallet_id)

                    if not target_pallet or target_pallet['status'] != 'ready':
                        CustomDialog.show_warning(
                            self,
                            "启动条件不满足",
                            f"启动失败！\n\n选择的{pallet_name}未处于就绪状态。\n\n请先设置垛盘为就绪状态后再启动。"
                        )
                        return

                    # 执行启动
                    self.system_status = "running"
                    self.is_paused = False

                    # 记录启动前的状态
                    self.pre_start_status[self.custom_pallet_id] = target_pallet['status']

                    # 更新指定垛盘为作业中
                    self.pallet_manager.update_pallet_status(self.custom_pallet_id, "working")

                    # 更新控制按钮状态
                    self.update_control_buttons()

                    # 准备自定义启动数据
                    pallet_config = self.prepare_custom_pallet_config()
                    recipe_data = self.prepare_recipe_data()
                    teaching_data = self.prepare_teaching_data()

                    # 启动自定义码垛作业
                    success = self.instruction_executor.start_custom_palletizing(
                        pallet_config, recipe_data, teaching_data,
                        self.custom_target_layers, self.custom_target_parts
                    )

                    if success:
                        CustomDialog.show_success(
                            self,
                            "自定义启动成功",
                            f"码垛系统已成功启动！\n\n"
                            f"启动参数：\n"
                            f"• 垛盘：{pallet_name}\n"
                            f"• 层数：{self.custom_target_layers}\n"
                            f"• 工作号：{self.custom_target_parts}\n\n"
                            f"系统正在运行中..."
                        )
                    else:
                        CustomDialog.show_error(
                            self,
                            "自定义启动失败",
                            "码垛系统自定义启动失败，请检查配置数据"
                        )
                        # 恢复状态
                        self.system_status = "stopped"
                        self.update_control_buttons()

    def refresh_pallet_data(self):
        """刷新垛盘数据显示"""
        # 刷新左垛盘
        self.update_pallet_display(self.LEFT_PALLET_ID, "left")
        # 刷新右垛盘
        self.update_pallet_display(self.RIGHT_PALLET_ID, "right")
        # 刷新统计数据
        self.update_statistics_display()

        # 检查并更新工作区图片
        self.check_and_update_active_pallet()

    def update_pallet_display(self, pallet_id: str, side: str):
        """更新垛盘显示"""
        pallet_data = self.pallet_manager.get_pallet_status(pallet_id)
        if not pallet_data:
            return

        # 更新状态按钮
        status_button = getattr(self, f"{side}StatusButton")
        self.update_status_button(status_button, pallet_data['status'])

        # 更新取消码垛开关
        clearance_toggle = getattr(self, f"{side}ClearanceToggle")
        self.update_clearance_toggle(clearance_toggle, pallet_data['cancel_palletizing'])

        # 更新当前层数显示
        current_layer_label = getattr(self, f"{side}CurrentLayerValueLabel")
        work_record = self.pallet_manager.get_work_record(pallet_id)
        total_layers = work_record['total_layers'] if work_record else 0
        current_layer_label.setText(f"{pallet_data['current_layer']}/{total_layers}")

        # 更新当前层箱子数显示（显示当前层已放置的箱子数）
        current_box_label = getattr(self, f"{side}CurrentBoxValueLabel")
        current_layer_boxes = pallet_data.get('current_layer_boxes', 0)
        current_box_label.setText(str(current_layer_boxes))

        # 更新总箱子数显示（显示已完成箱数/计划总箱数）
        total_box_label = getattr(self, f"{side}TotalBoxValueLabel")
        work_record = self.pallet_manager.get_work_record(pallet_id)
        completed_boxes = work_record['completed_boxes'] if work_record else 0
        total_boxes = work_record['total_boxes'] if work_record else 0
        total_box_label.setText(f"{completed_boxes}/{total_boxes}")

        # 如果垛盘状态发生变化，更新中心工作区图片
        try:
            # 检查是否需要更新中心工作区图片
            pallet_status = self.pallet_manager.get_pallet_status(pallet_id)
            if pallet_status and pallet_status.get('status') in ['working', 'paused']:
                self.set_active_pallet(pallet_id)
        except Exception as e:
            Logger.error(f"更新垛盘显示时切换工作区图片失败: {e}")

    def update_status_button(self, button, status: str):
        """更新状态按钮显示"""
        status_config = {
            'idle': {'text': '空闲', 'color': '#6b7280'},
            'ready': {'text': '就绪', 'color': '#22c55e'},
            'working': {'text': '作业中', 'color': '#ef4444'},
            'paused': {'text': '暂停', 'color': '#3b82f6'},
            'full': {'text': '满垛', 'color': '#f59e0b'}
        }

        config = status_config.get(status, status_config['idle'])
        button.setText(config['text'])
        button.setStyleSheet(f"""
            background-color: {config['color']};
            color: white;
            border-radius: 12px;
            padding: 4px 12px;
            font-size: 11px;
            font-weight: bold;
            min-width: 40px;
        """)

    def update_clearance_toggle(self, toggle_button, cancel_status: int):
        """更新取消码垛开关显示"""
        if cancel_status == 0:
            toggle_button.setStyleSheet("""
                background-color: #d1d5db;
                border-radius: 12px;
                min-height: 24px;
                min-width: 48px;
                background:url(:/run/run/OFF.png);
                background-repeat: no-repeat;
            """)
        else:
            toggle_button.setStyleSheet("""
                background-color: #22c55e;
                border-radius: 12px;
                min-height: 24px;
                min-width: 48px;
                background:url(:/run/run/ON.png);
                background-repeat: no-repeat;
            """)

    def update_statistics_display(self):
        """更新统计数据显示"""
        left_record = self.pallet_manager.get_work_record(self.LEFT_PALLET_ID)
        right_record = self.pallet_manager.get_work_record(self.RIGHT_PALLET_ID)

        # 更新速度标签（垛数）
        left_layers = left_record['completed_layers'] if left_record else 0
        right_layers = right_record['completed_layers'] if right_record else 0
        total_layers = left_layers + right_layers

        self.leftSpeedLabel.setText(f"左垛: {left_layers}")
        self.rightSpeedLabel.setText(f"右垛: {right_layers}")
        self.totalSpeedLabel.setText(f"总垛: {total_layers}")

        # 更新总数标签（箱数）
        left_boxes = left_record['completed_boxes'] if left_record else 0
        right_boxes = right_record['completed_boxes'] if right_record else 0
        total_boxes = left_boxes + right_boxes

        self.leftTotalLabel.setText(f"左垛箱: {left_boxes}")
        self.rightTotalLabel.setText(f"右垛箱: {right_boxes}")
        self.totalBoxCountLabel.setText(f"总箱: {total_boxes}")

    def get_pallet_name(self, pallet_id: str) -> str:
        """获取垛盘名称"""
        return "左垛盘" if pallet_id == self.LEFT_PALLET_ID else "右垛盘"

    def set_pallet_ready(self, pallet_id: str):
        """设置垛盘就绪"""
        pallet_data = self.pallet_manager.get_pallet_status(pallet_id)
        if not pallet_data:
            return

        pallet_name = self.get_pallet_name(pallet_id)
        current_status = self.status_map.get(pallet_data['status'], pallet_data['status'])

        if pallet_data['status'] != 'idle':
            CustomDialog.show_warning(
                self,
                "操作受限",
                f"{pallet_name}当前状态为'{current_status}'，只有空闲状态的垛盘才能设置为就绪。"
            )
            return

        # 使用状态变更对话框
        dialog = StatusChangeDialog(
            self,
            pallet_name,
            current_status,
            "就绪"
        )

        if dialog.exec():
            if self.pallet_manager.set_pallet_ready(pallet_id):
                pass
                # CustomDialog.show_success(
                #     self,
                #     "操作成功",
                #     f"{pallet_name}已成功设置为就绪状态！"
                # )
            else:
                CustomDialog.show_error(
                    self,
                    "操作失败",
                    f"设置{pallet_name}为就绪状态失败，请重试。"
                )

    def toggle_cancel_palletizing(self, pallet_id: str):
        """切换取消码垛状态"""
        pallet_data = self.pallet_manager.get_pallet_status(pallet_id)
        if not pallet_data:
            return

        pallet_name = self.get_pallet_name(pallet_id)
        current_cancel_status = pallet_data['cancel_palletizing']

        # 如果从开启(1)切换到关闭(0)，检查是否有暂停数据
        if current_cancel_status == 1:
            # 获取暂停时的数据
            paused_data = self.pallet_manager.get_paused_data(pallet_id)

            if paused_data and (paused_data['paused_layer'] > 0 or paused_data['paused_work_number'] > 0):
                # 显示恢复工作对话框
                resume_result = ResumeWorkDialog.show_resume_work(
                    self,
                    pallet_name,
                    paused_data['paused_layer'],
                    paused_data['paused_work_number']
                )

                if resume_result:
                    # 用户选择继续工作，恢复到暂停时的状态
                    if self.pallet_manager.toggle_cancel_palletizing(pallet_id):
                        # 可以在这里添加恢复工作的逻辑
                        # 比如设置当前层数和工作号
                        CustomDialog.show_success(
                            self,
                            "恢复工作",
                            f"已从{pallet_name}的第{paused_data['paused_layer']}层、工作号{paused_data['paused_work_number']}继续工作！"
                        )
                    else:
                        CustomDialog.show_error(
                            self,
                            "操作失败",
                            f"切换{pallet_name}取消码垛状态失败，请重试。"
                        )
                else:
                    # 用户选择不继续，显示重新开始确认对话框
                    restart_result = CustomDialog.show_question(
                        self,
                        "确认重新开始",
                        f"确定要让{pallet_name}重新开始工作吗？\n\n选择确定：清除之前的进度，从初始状态开始\n选择取消：保持当前状态不变"
                    )

                    if restart_result:
                        # 用户确认重新开始，切换状态并清除暂停数据
                        if self.pallet_manager.toggle_cancel_palletizing(pallet_id):
                            self.pallet_manager.clear_paused_data(pallet_id)
                            # CustomDialog.show_info(
                            #     self,
                            #     "重新开始",
                            #     f"{pallet_name}将从初始位置重新开始工作。"
                            # )
                        else:
                            CustomDialog.show_error(
                                self,
                                "操作失败",
                                f"切换{pallet_name}取消码垛状态失败，请重试。"
                            )
                    else:
                        # 用户取消重新开始，不做任何操作，相当于没有点击取消码垛按钮
                        # 保持原有状态不变
                        pass
            else:
                # 没有暂停数据，直接切换状态
                new_status_text = "关闭"
                if CustomDialog.show_question(
                        self,
                        "切换取消码垛状态",
                        f"确定要{new_status_text}{pallet_name}的取消码垛功能吗？\n\n当前状态：开启"
                ):
                    if self.pallet_manager.toggle_cancel_palletizing(pallet_id):
                        pass
                    else:
                        CustomDialog.show_error(
                            self,
                            "操作失败",
                            f"切换{pallet_name}取消码垛状态失败，请重试。"
                        )
        else:
            # 从关闭(0)切换到开启(1)
            new_status_text = "开启"
            if CustomDialog.show_question(
                    self,
                    "切换取消码垛状态",
                    f"确定要{new_status_text}{pallet_name}的取消码垛功能吗？\n\n当前状态：关闭"
            ):
                if self.pallet_manager.toggle_cancel_palletizing(pallet_id):
                    pass
                else:
                    CustomDialog.show_error(
                        self,
                        "操作失败",
                        f"切换{pallet_name}取消码垛状态失败，请重试。"
                    )

    def reset_pallet_data(self, pallet_id: str):
        """重置垛盘数据"""
        pallet_name = self.get_pallet_name(pallet_id)

        if CustomDialog.show_question(
                self,
                "确认重置垛盘数据",
                f"确定要重置{pallet_name}的数据吗？\n\n此操作将清除：\n• 当前层数\n• 当前层箱子数\n• 当前位置坐标\n\n此操作不可撤销！"
        ):
            if self.pallet_manager.reset_pallet_data(pallet_id):
                pass
                # CustomDialog.show_success(
                #     self,
                #     "重置成功",
                #     f"{pallet_name}数据已成功重置！"
                # )
            else:
                CustomDialog.show_error(
                    self,
                    "重置失败",
                    f"重置{pallet_name}数据失败，请重试。"
                )

    def force_full_pallet(self, pallet_id: str):
        """强制满垛"""
        pallet_name = self.get_pallet_name(pallet_id)

        if CustomDialog.show_question(
                self,
                "确认强制满垛",
                f"确定要强制设置{pallet_name}为满垛状态吗？\n\n执行此操作后：\n• 设备将在完成当前动作后停止该垛盘的作业\n• 垛盘状态将变更为'满垛'\n• 需要手动清理后才能继续使用\n\n请确认是否继续？"
        ):
            if self.pallet_manager.force_full_pallet(pallet_id):
                pass
                # CustomDialog.show_success(
                #     self,
                #     "操作成功",
                #     f"{pallet_name}已设置为强制满垛状态！\n设备将在完成当前动作后停止该垛盘的作业。"
                # )
            else:
                CustomDialog.show_error(
                    self,
                    "操作失败",
                    f"设置{pallet_name}强制满垛失败，请重试。"
                )

    def get_inverse_kinematics(self, x, y, z, rx=180, ry=0, rz=0, config=0):
        """
        使用GetInverseKin()逆运动学解算获取完整的位置信息

        Args:
            x, y, z: 目标位置坐标 (mm)
            rx, ry, rz: 目标姿态角度 (度)，默认为垂直向下
            config: 配置参数，默认为0

        Returns:
            dict: 包含J1-J6关节角度和TCP位置的完整信息
        """
        try:
            # 构建GetInverseKin指令参数
            params = {
                'flag': 0,  # 标志位
                'x': x,
                'y': y,
                'z': z,
                'a': rx,  # 绕X轴旋转角度
                'b': ry,  # 绕Y轴旋转角度
                'c': rz,  # 绕Z轴旋转角度
                'config': config  # 配置参数
            }

            # 生成逆运动学指令
            cmd_str = RobotCommand.get_command_str('GetInverseKin', params, 377)

            # 发送指令并等待响应
            success = command_manager.send_command(cmd_str, "逆运动学解算")
            if success:
                Logger.info(f"发送逆运动学解算指令: 目标位置({x}, {y}, {z}), 姿态({rx}, {ry}, {rz})")
            else:
                Logger.error("逆运动学解算指令发送失败")
                return None

            # 注意：这里需要等待机器人响应并解析返回的关节角度
            # 实际应用中需要通过信号槽机制处理响应

        except Exception as e:
            Logger.error(f"逆运动学解算失败: {e}")
            return None

    def set_base_position(self, x, y, z, rx=180, ry=0, rz=0):
        """
        设置基准位置，通过逆运动学解算获取完整的位置信息

        Args:
            x, y, z: 基准位置坐标
            rx, ry, rz: 基准姿态，默认为垂直向下
        """
        # 获取基准位置的完整信息
        base_info = self.get_inverse_kinematics(x, y, z, rx, ry, rz)

        if base_info:
            self.base_position = {
                'tcp_x': x,
                'tcp_y': y,
                'tcp_z': z,
                'tcp_rx': rx,
                'tcp_ry': ry,
                'tcp_rz': rz,
                # 关节角度需要从逆运动学响应中获取
                'joint_j1': 0,  # 实际值需要从响应中解析
                'joint_j2': 0,
                'joint_j3': 0,
                'joint_j4': 0,
                'joint_j5': 0,
                'joint_j6': 0
            }
            Logger.info(f"设置基准位置: ({x}, {y}, {z}), 姿态: ({rx}, {ry}, {rz})")

    def move_with_offset(self, dt_x=0, dt_y=0, dt_z=0, dt_rx=0, dt_ry=0, dt_rz=0,
                         move_type='MoveL', speed=100):
        """
        基于基准位置进行偏移移动

        Args:
            dt_x, dt_y, dt_z: XYZ方向的偏移量 (mm)
            dt_rx, dt_ry, dt_rz: 姿态偏移量 (度)
            move_type: 移动类型 ('MoveJ', 'MoveL')
            speed: 移动速度
        """
        if not self.base_position:
            Logger.error("未设置基准位置，无法执行偏移移动")
            return False

        try:
            # 计算目标位置（基准位置 + 偏移量）
            target_position = {
                'tcp_x': self.base_position['tcp_x'] + dt_x,
                'tcp_y': self.base_position['tcp_y'] + dt_y,
                'tcp_z': self.base_position['tcp_z'] + dt_z,
                'tcp_rx': self.gripper_orientation['rx'] + dt_rx,  # 保持垂直向下
                'tcp_ry': self.gripper_orientation['ry'] + dt_ry,
                'tcp_rz': self.gripper_orientation['rz'] + dt_rz,
            }

            # 先通过逆运动学获取目标位置的关节角度
            target_kinematics = self.get_inverse_kinematics(
                target_position['tcp_x'],
                target_position['tcp_y'],
                target_position['tcp_z'],
                target_position['tcp_rx'],
                target_position['tcp_ry'],
                target_position['tcp_rz']
            )

            # 构建移动指令参数（包含偏移信息）
            move_params = {
                # 关节角度（需要从逆运动学结果中获取）
                'J1': target_kinematics.get('J1', 0) if target_kinematics else 0,
                'J2': target_kinematics.get('J2', 0) if target_kinematics else 0,
                'J3': target_kinematics.get('J3', 0) if target_kinematics else 0,
                'J4': target_kinematics.get('J4', 0) if target_kinematics else 0,
                'J5': target_kinematics.get('J5', 0) if target_kinematics else 0,
                'J6': target_kinematics.get('J6', 0) if target_kinematics else 0,

                # TCP位置和姿态
                'x': target_position['tcp_x'],
                'y': target_position['tcp_y'],
                'z': target_position['tcp_z'],
                'rx': target_position['tcp_rx'],
                'ry': target_position['tcp_ry'],
                'rz': target_position['tcp_rz'],

                # 运动参数
                'speed': speed,
                'toolNum': 1,
                'workPieceNum': 0,
                'acc': 100,
                'ovl': 100
            }

            # 生成移动指令（偏移操作已集成在cmd_tool.py的指令生成中）
            cmd_str = RobotCommand.get_command_str(move_type, move_params)

            # 发送移动指令
            success = command_manager.send_command(cmd_str, f"偏移移动-{move_type}")
            if success:
                Logger.info(f"执行偏移移动: {move_type}, 偏移量({dt_x}, {dt_y}, {dt_z}), "
                            f"目标位置({target_position['tcp_x']}, {target_position['tcp_y']}, {target_position['tcp_z']})")
                return True
            else:
                Logger.error("偏移移动指令发送失败")
                return False

        except Exception as e:
            Logger.error(f"偏移移动失败: {e}")
            return False

    def pick_and_place_sequence(self, pick_x, pick_y, pick_z, place_x, place_y, place_z,
                                approach_height=50, speed=50):
        """
        执行抓取和放置序列，保持抓手垂直向下

        Args:
            pick_x, pick_y, pick_z: 抓取位置坐标
            place_x, place_y, place_z: 放置位置坐标
            approach_height: 接近高度偏移 (mm)
            speed: 移动速度
        """
        try:
            # 1. 设置抓取位置为基准位置
            self.set_base_position(pick_x, pick_y, pick_z)

            # 2. 移动到抓取位置上方（接近位置）
            self.move_with_offset(dt_z=approach_height, speed=speed)

            # 3. 下降到抓取位置
            self.move_with_offset(dt_z=0, speed=speed // 2)  # 较慢速度下降

            # 4. 执行抓取动作（这里可以添加IO控制）
            # self.gripper_close()  # 需要实现夹爪控制

            # 5. 上升到安全高度
            self.move_with_offset(dt_z=approach_height, speed=speed // 2)

            # 6. 移动到放置位置上方
            # 计算从抓取位置到放置位置的偏移
            offset_x = place_x - pick_x
            offset_y = place_y - pick_y
            offset_z = place_z - pick_z + approach_height

            self.move_with_offset(dt_x=offset_x, dt_y=offset_y, dt_z=offset_z, speed=speed)

            # 7. 下降到放置位置
            self.move_with_offset(dt_x=offset_x, dt_y=offset_y,
                                  dt_z=place_z - pick_z, speed=speed // 2)

            # 8. 执行放置动作
            # self.gripper_open()  # 需要实现夹爪控制

            # 9. 上升到安全高度
            self.move_with_offset(dt_x=offset_x, dt_y=offset_y,
                                  dt_z=place_z - pick_z + approach_height, speed=speed // 2)

            Logger.info(f"完成抓取放置序列: 从({pick_x}, {pick_y}, {pick_z}) 到 ({place_x}, {place_y}, {place_z})")
            return True

        except Exception as e:
            Logger.error(f"抓取放置序列执行失败: {e}")
            return False

    def handle_inverse_kinematics_response(self, response_data):
        """
        处理逆运动学解算的响应数据

        Args:
            response_data: 机器人返回的响应数据
        """
        try:
            # 解析响应数据，提取关节角度信息
            # 具体解析格式需要根据机器人协议文档确定
            # 这里提供一个示例框架

            # 假设响应格式包含J1-J6的关节角度
            # parsed_data = self.parse_robot_response(response_data)

            # if parsed_data and 'joints' in parsed_data:
            #     joint_angles = parsed_data['joints']
            #     Logger.info(f"获取到关节角度: {joint_angles}")
            #     return joint_angles

            pass

        except Exception as e:
            Logger.error(f"解析逆运动学响应失败: {e}")
            return None

    def check_and_update_active_pallet(self):
        """检查并更新当前活动垛盘的工作区图片"""
        if self.system_status != "running":
            return

        try:
            # 获取当前垛盘状态
            left_pallet = self.pallet_manager.get_pallet_status(self.LEFT_PALLET_ID)
            right_pallet = self.pallet_manager.get_pallet_status(self.RIGHT_PALLET_ID)

            left_working = left_pallet and left_pallet['status'] in ['working', 'paused']
            right_working = right_pallet and right_pallet['status'] in ['working', 'paused']

            # 根据当前工作状态更新图片
            if left_working and not right_working:
                self.update_center_workspace_image('left')
            elif right_working and not left_working:
                self.update_center_workspace_image('right')
            elif left_working and right_working:
                # 如果两个都在工作，可以根据业务逻辑决定显示哪个
                # 这里保持当前显示不变，或者可以根据最后操作的垛盘来决定
                pass
            else:
                # 都不在工作，显示默认图片
                self.update_center_workspace_image('default')

        except Exception as e:
            Logger.error(f"检查活动垛盘失败: {e}")

    def get_ready_pallets(self):
        """获取所有就绪状态的垛盘ID列表"""
        ready_pallets = []

        # 检查左垛盘
        left_pallet = self.pallet_manager.get_pallet_status(self.LEFT_PALLET_ID)
        if left_pallet and left_pallet['status'] == 'ready':
            ready_pallets.append(self.LEFT_PALLET_ID)

        # 检查右垛盘
        right_pallet = self.pallet_manager.get_pallet_status(self.RIGHT_PALLET_ID)
        if right_pallet and right_pallet['status'] == 'ready':
            ready_pallets.append(self.RIGHT_PALLET_ID)

        return ready_pallets

    def prepare_pallet_config_for_pallet(self, pallet_id: str):
        """为特定垛盘准备配置数据"""
        try:
            # 获取基础配置
            base_config = self.pallet_manager.get_pallet_config()

            # 根据垛盘ID添加特定配置
            base_config['pallet_id'] = pallet_id
            base_config['pallet_name'] = self.get_pallet_name(pallet_id)

            # 可以根据不同垛盘设置不同的参数
            if pallet_id == self.LEFT_PALLET_ID:
                base_config['side'] = 'left'
            elif pallet_id == self.RIGHT_PALLET_ID:
                base_config['side'] = 'right'

            return base_config
        except Exception as e:
            Logger.error(f"准备垛盘 {pallet_id} 配置数据失败: {e}")
            return None

    def start_multi_pallet_execution(self, all_instructions):
        """启动多垛盘执行"""
        try:
            # 记录启动前状态
            for pallet_id in all_instructions.keys():
                pallet_data = self.pallet_manager.get_pallet_status(pallet_id)
                self.pre_start_status[pallet_id] = pallet_data['status'] if pallet_data else None

                # 设置为工作状态
                self.pallet_manager.update_pallet_status(pallet_id, "working")

            # 更新系统状态
            self.system_status = "running"
            self.update_control_buttons()

            # 启动指令执行器
            if not hasattr(self, 'instruction_executor'):
                self.instruction_executor = PalletizerInstructionExecutor()
                self.connect_executor_signals()

            self.instruction_executor.start_multi_pallet_execution(all_instructions)

            # 刷新显示
            self.refresh_pallet_data()

        except Exception as e:
            Logger.error(f"多垛盘执行启动失败: {e}")

    def prepare_pallet_config(self):
        """准备托盘配置数据"""
        try:
            return self.pallet_manager.get_pallet_config()
        except Exception as e:
            Logger.error(f"准备托盘配置数据失败: {e}")
            return None

    def prepare_custom_pallet_config(self):
        """准备自定义垛盘配置数据"""
        try:
            # 根据自定义选择的垛盘获取配置
            return self.pallet_manager.get_pallet_config(self.custom_pallet_id)
        except Exception as e:
            Logger.error(f"准备自定义垛盘配置数据失败: {e}")
            return None

    def prepare_recipe_data(self):
        """准备配方数据（不再使用，返回None）"""
        Logger.info("不再使用配方数据，跳过配方准备")
        return None

    def prepare_teaching_data(self):
        """准备示教数据"""
        try:
            # 从数据库获取示教点数据
            teaching_points = self.pallet_manager.get_teaching_points_by_type()

            # 映射数据库中的point_type到执行器期望的键名
            mapped_data = {
                'grab_point': teaching_points.get('fetching'),
                'grab_transition_point': teaching_points.get('fetching_interim'),
                'place_transition_point': teaching_points.get('put_interim'),
                'left_pallet_zero_point': teaching_points.get('left_tray_zorn'),
                'right_pallet_zero_point': teaching_points.get('right_tray_zorn'),
                'initial_position': teaching_points.get('fetching')  # 使用fetching作为初始位置
            }

            # 过滤掉None值
            filtered_data = {k: v for k, v in mapped_data.items() if v is not None}

            return filtered_data
        except Exception as e:
            Logger.error(f"准备示教数据失败: {e}")
            return None

    def on_execution_started(self, pallet_id):
        """指令执行开始回调 - 支持垛盘ID"""
        try:
            Logger.info(f"垛盘 {self.get_pallet_name(pallet_id)} 开始执行")

            # 更新当前活动垛盘的工作区图片
            if pallet_id == self.LEFT_PALLET_ID:
                self.update_center_workspace_image('left')
            elif pallet_id == self.RIGHT_PALLET_ID:
                self.update_center_workspace_image('right')

            # 更新垛盘状态显示
            self.refresh_pallet_data()

        except Exception as e:
            Logger.error(f"处理执行开始事件失败: {e}")

    def on_execution_progress(self, pallet_id, layer, box_count, progress):
        """指令执行进度回调 - 支持垛盘ID"""
        try:
            Logger.info(
                f"垛盘 {self.get_pallet_name(pallet_id)} 进度更新: 层数: {layer}, 箱数: {box_count}, 进度: {progress}%")

            # 更新垛盘数据
            self.pallet_manager.update_pallet_progress(pallet_id, layer, box_count)

            # 更新当前活动垛盘的工作区图片
            if pallet_id == self.LEFT_PALLET_ID:
                self.update_center_workspace_image('left')
            elif pallet_id == self.RIGHT_PALLET_ID:
                self.update_center_workspace_image('right')

            # 刷新显示
            self.refresh_pallet_data()

        except Exception as e:
            Logger.error(f"处理执行进度事件失败: {e}")

    def on_execution_finished(self, pallet_id, success, message):
        """指令执行完成回调 - 支持垛盘ID"""
        try:
            pallet_name = self.get_pallet_name(pallet_id)

            if success:
                Logger.info(f"垛盘 {pallet_name} 作业完成: {message}")
                # 更新垛盘状态为满垛
                self.pallet_manager.update_pallet_status(pallet_id, "full")

                # 更新对应的StatusButton
                if pallet_id == self.LEFT_PALLET_ID:
                    self.update_status_button(self.leftStatusButton, "full")
                elif pallet_id == self.RIGHT_PALLET_ID:
                    self.update_status_button(self.rightStatusButton, "full")
            else:
                Logger.error(f"垛盘 {pallet_name} 作业失败: {message}")
                CustomDialog.show_error(
                    self,
                    "作业失败",
                    f"垛盘 {pallet_name} 作业失败:\n{message}"
                )

            # 检查是否所有垛盘都完成
            self.check_all_pallets_status()

        except Exception as e:
            Logger.error(f"处理执行完成事件失败: {e}")

    def on_execution_error(self, pallet_id, error_code, error_message):
        """指令执行错误回调"""
        try:
            Logger.error(f"码垛作业错误: {pallet_id}, 错误码: {error_code}, 错误信息: {error_message}")

            # 显示错误对话框
            CustomDialog.show_error(
                self,
                "作业错误",
                f"垛盘 {self.get_pallet_name(pallet_id)} 发生错误:\n"
                f"错误码: {error_code}\n"
                f"错误信息: {error_message}\n\n"
                f"请检查设备状态后重试。"
            )

            # 暂停系统
            self.system_status = "paused"
            self.is_paused = True
            self.pallet_manager.update_pallet_status(pallet_id, "paused")
            self.update_control_buttons()

        except Exception as e:
            Logger.error(f"处理执行错误事件失败: {e}")

    def check_all_pallets_status(self):
        """检查所有垛盘状态，判断是否需要停止系统"""
        try:
            left_pallet = self.pallet_manager.get_pallet_status(self.LEFT_PALLET_ID)
            right_pallet = self.pallet_manager.get_pallet_status(self.RIGHT_PALLET_ID)

            left_working = left_pallet and left_pallet['status'] in ['working', 'paused']
            right_working = right_pallet and right_pallet['status'] in ['working', 'paused']

            # 如果没有垛盘在工作，停止系统
            if not left_working and not right_working:
                self.system_status = "stopped"
                self.is_paused = False
                self.update_center_workspace_image('default')
                self.update_control_buttons()

                Logger.info("所有垛盘作业完成，系统自动停止")

        except Exception as e:
            Logger.error(f"检查垛盘状态失败: {e}")

    def print_instruction_sequence(self, instruction_sequence):
        """打印指令序列到日志和控制台"""
        Logger.info("=== 生成的指令序列 ===")
        print("\n=== 码垛指令序列 ===")

        for i, instruction in enumerate(instruction_sequence, 1):
            log_msg = f"{i:3d}. {instruction}"
            Logger.info(log_msg)
            print(log_msg)

        Logger.info(f"=== 指令序列结束，共 {len(instruction_sequence)} 条指令 ===")
        print(f"=== 指令序列结束，共 {len(instruction_sequence)} 条指令 ===\n")

    def showEvent(self, event):
        """页面显示时触发"""
        super().showEvent(event)

        from src.logic.IO_logic import set_function_output
        # 开启启动灯
        result = set_function_output('启动灯', True)
        if result:
            print("启动灯开启成功")
        else:
            print("启动灯开启失败，请检查IO配置")

        # # 开启抓取功能
        # set_function_output('抓取', True)
        # 开启抓取功能
        set_function_output('抓取', False)
        print(set_function_output('抓取', False))

        self.refresh_pallet_data()