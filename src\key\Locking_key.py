import platform
import subprocess
import hashlib
import re
import base64

SHORT_CODE_SALT = "a_fixed_salt_for_shortening_keys"


def _get_cpu_id_windows():
    """获取Windows硬件指纹，优先使用单一可靠的硬件标识符。"""

    # 策略1: 优先获取主板序列号（最稳定且唯一）
    try:
        command = "Get-CimInstance -ClassName Win32_BaseBoard | Select-Object -ExpandProperty SerialNumber"
        result = subprocess.check_output(
            ['powershell', '-Command', command],
            text=True,
            stderr=subprocess.DEVNULL
        ).strip()
        if result and result != "" and result.lower() not in ["none", "to be filled by o.e.m.", "default string",
                                                              "0123456789"]:
            print(f"INFO: Found BaseBoard SerialNumber: {result}")
            return hashlib.sha256(f"baseboard:{result}".encode('utf-8')).hexdigest()
    except Exception:
        pass

    # 策略2: 备用方案 - 获取BIOS序列号
    try:
        command = "Get-CimInstance -ClassName Win32_BIOS | Select-Object -ExpandProperty SerialNumber"
        result = subprocess.check_output(
            ['powershell', '-Command', command],
            text=True,
            stderr=subprocess.DEVNULL
        ).strip()
        if result and result != "" and result.lower() not in ["none", "to be filled by o.e.m.", "default string",
                                                              "0123456789"]:
            print(f"INFO: Found BIOS SerialNumber: {result}")
            return hashlib.sha256(f"bios:{result}".encode('utf-8')).hexdigest()
    except Exception:
        pass

    # 如果前两个策略都失败，组合主板和BIOS信息作为最后手段
    try:
        commands = {
            "baseboard_product": "Get-CimInstance -ClassName Win32_BaseBoard | Select-Object -ExpandProperty Product",
            "bios_version": "Get-CimInstance -ClassName Win32_BIOS | Select-Object -ExpandProperty SMBIOSBIOSVersion"
        }

        hardware_info = []
        for info_type, cmd in commands.items():
            try:
                result = subprocess.check_output(
                    ['powershell', '-Command', cmd],
                    text=True,
                    stderr=subprocess.DEVNULL
                ).strip()
                if result and result != "" and result.lower() not in ["none", "default"]:
                    hardware_info.append(f"{info_type}:{result}")
            except Exception:
                continue

        if hardware_info:
            combined_str = "|".join(hardware_info)
            print(f"INFO: Using combined hardware info: {len(hardware_info)} items")
            return hashlib.sha256(f"combined:{combined_str}".encode('utf-8')).hexdigest()
    except Exception:
        pass

    print("WARNING: All hardware fingerprint strategies failed")
    return None


def _get_cpu_id_linux():
    """
    为Linux（特别是Buildroot）设计的更健壮的ID获取方法。
    按顺序尝试: 1. CPU序列号 -> 2. eth0的MAC地址 -> 3. 回退到旧方法
    """
    # --- 策略1: 尝试从 /proc/cpuinfo 获取唯一的 CPU Serial ---
    try:
        with open('/proc/cpuinfo', 'r') as f:
            for line in f:
                if line.strip().lower().startswith('serial'):
                    serial = line.split(':')[1].strip()
                    if serial and serial != "0000000000000000":  # 确保不是一个空的占位符
                        # 找到一个看起来有效的序列号，直接用它
                        print(f"INFO: Found CPU Serial: {serial}")
                        return hashlib.sha256(f"cpuserial:{serial}".encode()).hexdigest()
    except Exception:
        pass  # 获取失败，继续下一个策略

    # --- 策略2: 尝试获取 eth0 的 MAC 地址 ---
    try:
        # 使用 'ip link' 或 'ifconfig'
        command = ['ip', 'link', 'show', 'eth0']
        try:
            output = subprocess.check_output(command, text=True, stderr=subprocess.DEVNULL)
        except FileNotFoundError:
            # 如果 'ip' 命令不存在，尝试 'ifconfig'
            command = ['ifconfig', 'eth0']
            output = subprocess.check_output(command, text=True, stderr=subprocess.DEVNULL)

        # 使用正则表达式匹配MAC地址
        mac_match = re.search(r'ether\s+([0-9a-fA-F]{2}(?::[0-9a-fA-F]{2}){5})', output)
        if mac_match:
            mac_address = mac_match.group(1).strip()
            print(f"INFO: Found MAC Address: {mac_address}")
            return hashlib.sha256(f"mac:{mac_address}".encode()).hexdigest()
    except Exception:
        pass  # 获取失败，继续下一个策略

    # --- 策略3: 回退到旧的、不太可靠的方法 ---
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpu_info = f.read()
        key_lines = [line for line in cpu_info.split('\n') if
                     'vendor_id' in line or 'model name' in line or 'cpu family' in line or 'Hardware' in line]
        stable_info = "".join(key.strip() for key in key_lines)
        if stable_info:
            print("INFO: Falling back to generic cpuinfo fields.")
            return hashlib.sha256(f"cpuinfo_fallback:{stable_info}".encode()).hexdigest()
    except Exception:
        return None  # 所有方法都失败了

    return None


def get_cpu_id_fingerprint():
    """
    主指纹生成函数。它现在调用特定平台的函数，
    这些函数内部已经完成了哈希，保证了输出的一致性。
    """
    system_type = platform.system()
    platform_specific_hash = None
    if system_type == "Windows":
        platform_specific_hash = _get_cpu_id_windows()
    elif system_type == "Linux":
        platform_specific_hash = _get_cpu_id_linux()
    else:
        return None

    if not platform_specific_hash:
        return None

    # 再次哈希以增加复杂度和隔离性
    # 即使平台哈希泄露，最终指纹也不会直接暴露。
    unique_string = f"fingerprint_layer_2:{platform_specific_hash}".strip().lower()
    return hashlib.sha256(unique_string.encode('utf-8')).hexdigest()


def get_short_machine_code(fingerprint: str) -> str:
    """从最终指纹生成用户友好的短码。"""
    if not fingerprint:
        return None
    salted_fp = (fingerprint + SHORT_CODE_SALT).encode('utf-8')
    hashed = hashlib.sha256(salted_fp).digest()
    short_hash = hashed[:8]
    # 使用标准Base64编码，避免'-'和'_'，减少混淆
    short_code = base64.b64encode(short_hash).decode('utf-8').rstrip('=')
    return short_code