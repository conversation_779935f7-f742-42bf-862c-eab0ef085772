"""
全局变量
"""
is_8080_connected = False
is_8083_connected = False

# 码垛机器人设置相关全局变量
PALLET_WIDTH = "1200"
PALLET_LENGTH = "1200"
BOX_WIDTH = "300"
BOX_LENGTH = "200"
SAFETY_HEIGHT = 100
APPROACH_HEIGHT = 50
GRIP_FORCE = 50
WAIT_TIME = 10
PUT_HEIGHT = 100
LIFT_HEIGHT = 100        # 爬取抬升高度默认值
PUT_LIFT_HEIGHT = 100    # 放置后抬升高度默认值

# IO映射设置
INPUT_MAPPING = {}
OUTPUT_MAPPING = {}
SAFETY_INPUTS = []

# 偏移补偿设置
OFFSET_COMPENSATIONX_ = "0"
OFFSET_COMPENSATIONY_ = "0"
OFFSET_COMPENSATIONZ_ = "0"
OFFSET_COMPENSATIONRX_ = "0"
OFFSET_COMPENSATIONRY_ = "0"
OFFSET_COMPENSATIONRZ_ = "0"

# 速度设置
GLOBAL_SPEED_ = "30"
TRUE_SPEED_ = "1000"

# 系统设置
language_ = "中文"
update_system_settings = False
Simulated_click = 0
IP_change = False

Version_Number_details = "None"#版本号

Simulated_click = 1#改变英文后，解决打开软件时表格不能切换为英文bug
power_on=0 #软件开启时的等待时间


############linux################
system_type = "Windows"

yaoshi=None #物理硬件
jianpan=None
############linux################


################IO################
# IO状态全局变量 - 控制箱数字量IO (16个DI + 16个DO)
IO_STATUS = {
    # 数字量输入状态 DI0-DI15
    'DI': {f'DI{i}': 0 for i in range(16)},
    # 数字量输出状态 DO0-DO15
    'DO': {f'DO{i}': 0 for i in range(16)},
    # 工具IO状态 (仅bit0和bit1有效)
    'TOOL_DI': {'TDI0': 0, 'TDI1': 0},
    'TOOL_DO': {'TDO0': 0, 'TDO1': 0}
}
# IO状态更新锁
IO_STATUS_LOCK = None

io_logic_instance=None
################IO################
