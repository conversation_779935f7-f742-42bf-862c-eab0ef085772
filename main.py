import sys
import os
# 我电脑一直跳Unknown property box-shadow，我屏蔽一下--hzy
os.environ["QT_LOGGING_RULES"] = "*=false;qt.qpa.*=false"

import platform
from PySide6.QtWidgets import QApplication
from PySide6.QtNetwork import QLocalServer, QLocalSocket
from PySide6.QtGui import QIcon, Qt
from PySide6.QtCore import QCoreApplication, QTimer
# --- 导入应用程序模块 ---
from src.logic.main_windows import MainWindow
from src.common.sql_lite_tool import SqliteTool
from src.common import global_vars as gv
from src.key.activation_manager import ActivationManager


def initialize_app(app, resource_path):
    """主程序初始化入口。"""
    # 设置图标和应用ID
    if gv.system_type != "Linux":
        try:
            icon_path = os.path.join(resource_path, 'src/images/app_icon.ico')
            app.setWindowIcon(QIcon(icon_path))
            from ctypes import windll
            appid = 'faaomeide.palletizer_robot.version'
            windll.shell32.SetCurrentProcessExplicitAppUserModelID(appid)
        except (ImportError, FileNotFoundError, AttributeError):
            pass

    # 单例检测
    server_name = "PalletizerLink_Singleton_Server_v1"
    socket = QLocalSocket()
    socket.connectToServer(server_name)
    if socket.waitForConnected(500):
        print("应用程序已在运行。")
        sys.exit(1)
    app.local_server = QLocalServer()
    app.local_server.listen(server_name)

    # 初始化数据库
    SqliteTool.initialize()

    # 激活成功后的回调函数
    def on_activation_success():
        print("激活验证成功，正在初始化主程序...")
        setup_application_tables(SqliteTool.get_instance())

        record = SqliteTool.get_instance().select("system_settings", fields=["language"],
                                                  condition="1=1 ORDER BY id DESC LIMIT 1")
        if record:
            gv.language_ = record[0]["language"]

        app.main_window = MainWindow()
        app.main_window.setWindowTitle(QCoreApplication.translate("MainWindow", "智能码垛机器人"))
        app.main_window.resize(1024, 764)
        app.main_window.show()
        notify_launcher_if_needed()

    # 创建并启动激活管理器
    app.activation_manager = ActivationManager(app, on_activation_success)
    app.activation_manager.start()


def setup_application_tables(db_tool):
    """初始化所有业务相关的数据库表。"""
    schemas = [
        """CREATE TABLE IF NOT EXISTS speed_settings
           (
               id INTEGER PRIMARY KEY,
               speed TEXT
           );""",
        """CREATE TABLE IF NOT EXISTS collision_settings
           (
               id INTEGER PRIMARY KEY,
               collision_grade_type TEXT,
               j1 NOT NULL DEFAULT '60',
               j2 NOT NULL DEFAULT '60',
               j3 NOT NULL DEFAULT '60',
               j4 NOT NULL DEFAULT '60',
               j5 NOT NULL DEFAULT '60',
               j6 NOT NULL DEFAULT '60'
           );""",
        """        CREATE TABLE IF NOT EXISTS ip_settings(
                    id INTEGER PRIMARY KEY,
                    ip TEXT NOT NULL DEFAULT '************'
            );""",
        # 添加点位数据库表
        """CREATE TABLE IF NOT EXISTS teaching_points(
            id INTEGER PRIMARY KEY,  --主键ID
            point_type TEXT,         --点的类型
            x TEXT, y TEXT, z TEXT,  --关节/TCP
            rx TEXT, ry TEXT, rz TEXT,
            j1 TEXT, j2 TEXT, j3 TEXT, j4 TEXT, j5 TEXT, j6 TEXT,
            speed NOT NULL DEFAULT 20, --速度
            toolNum NOT NULL DEFAULT 0,  --工具号
            workPieceNum NOT NULL DEFAULT 0
       );""",

        # 垛盘状态管理表
        """CREATE TABLE IF NOT EXISTS pallet_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,                    -- 主键ID，自增
            pallet_id TEXT NOT NULL UNIQUE,                         -- 垛盘ID（左垛盘/右垛盘）
            pallet_name TEXT NOT NULL,                              -- 垛盘名称
            status TEXT NOT NULL DEFAULT 'idle',                   -- 状态：idle(空闲)、ready(就绪)、working(作业中)、paused(暂停)、full(满垛)
            is_ready INTEGER DEFAULT 0,                            -- 是否就绪：0-否，1-是
            cancel_palletizing INTEGER DEFAULT 0,                  -- 取消码垛开关：0-关闭，1-打开
            current_layer INTEGER DEFAULT 1,                       -- 当前层数
            current_layer_boxes INTEGER DEFAULT 0,                 -- 当前层已放置箱子数
            current_position_x INTEGER DEFAULT 0,                  -- 当前码放位置X坐标
            current_position_y INTEGER DEFAULT 0,                  -- 当前码放位置Y坐标
            paused_layer INTEGER DEFAULT 0,                        -- 暂停时的层数
            paused_work_number INTEGER DEFAULT 0,                  -- 暂停时的工作号
            last_save_position TEXT,                               -- 最后保存的位置信息（JSON格式）
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,         -- 创建时间
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP          -- 更新时间
        );""",

        # 添加字段的SQL语句（用于已存在的表）
        # """ALTER TABLE pallet_status ADD COLUMN paused_layer INTEGER DEFAULT 0;""",
        # """ALTER TABLE pallet_status ADD COLUMN paused_work_number INTEGER DEFAULT 0;""",

        # 垛盘作业记录表
        """CREATE TABLE IF NOT EXISTS pallet_work_record (
            id INTEGER PRIMARY KEY AUTOINCREMENT,                   -- 主键ID，自增
            pallet_id TEXT NOT NULL,                                -- 垛盘ID
            work_session_id TEXT NOT NULL,                          -- 作业会话ID，用于标识一次完整的作业过程
            start_time DATETIME NOT NULL,                           -- 作业开始时间
            end_time DATETIME,                                      -- 作业结束时间
            total_layers INTEGER DEFAULT 0,                        -- 计划总层数
            completed_layers INTEGER DEFAULT 0,                    -- 已完成层数
            total_boxes INTEGER DEFAULT 0,                         -- 计划总箱数
            completed_boxes INTEGER DEFAULT 0,                     -- 已完成箱数
            status TEXT DEFAULT 'active',                          -- 记录状态：active(进行中)、completed(完成)、interrupted(中断)
            save_position TEXT,                                     -- 保存的位置信息（JSON格式），用于断点续传
            formula_id INTEGER,                                     -- 使用的配方ID
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP          -- 记录创建时间
        );""",

        # IO功能配置表 - 定义系统功能
        """CREATE TABLE IF NOT EXISTS io_functions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            function_name TEXT NOT NULL UNIQUE,                    -- 功能名称（如：start_signal、pause_signal等）
            function_type TEXT NOT NULL,                          -- 功能类型：input(输入功能)、output(输出功能)
            description TEXT,                                     -- 功能描述
            is_enabled INTEGER DEFAULT 1,                        -- 是否启用：0-禁用，1-启用
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );""",
        # IO端口映射表 - 功能与物理IO端口的映射关系
        """CREATE TABLE IF NOT EXISTS io_port_mapping (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            function_name TEXT NOT NULL,                          -- 功能名称，关联io_functions表
            io_type TEXT NOT NULL,                               -- IO类型：DI、DO
            port_number INTEGER NOT NULL,                        -- IO端口号（0-7）
            active_level TEXT DEFAULT 'high',                   -- 有效电平：high(高电平有效)、low(低电平有效)
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (function_name) REFERENCES io_functions(function_name)
        );""",
        # IO状态缓存表 - 缓存当前IO状态
        """CREATE TABLE IF NOT EXISTS io_status_cache (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            io_type TEXT NOT NULL,                               -- IO类型：DI、DO
            port_number INTEGER NOT NULL,                        -- IO端口号
            current_value INTEGER DEFAULT 0,                    -- 当前值：0或1
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(io_type, port_number)
        );""",

        # 原有的程序相关表
        """CREATE TABLE IF NOT EXISTS program(
        id INTEGER PRIMARY KEY,                                   -- 程序表：id-主键,program_name-程序名称
        program_name TEXT NOT NULL UNIQUE
        );""",

        """CREATE TABLE IF NOT EXISTS program_detail (
            id INTEGER PRIMARY KEY,                                 -- 主键ID
            instruction_type TEXT NOT NULL,                         -- 指令类型
            instruction_type_number INTEGER NOT NULL DEFAULT 0,     -- 指令类型编号
            point_position TEXT NOT NULL,                           -- 点位位置信息
            speed REAL NOT NULL,                                    -- 运行速度
            program_id TEXT NOT NULL                                -- 关联的程序ID
        );""",

        """CREATE TABLE IF NOT EXISTS robot_settings (
            id INTEGER PRIMARY KEY,                                 -- 主键ID
            collision_grade_type TEXT,                              -- 碰撞等级类型
            j1 TEXT, j2 TEXT, j3 TEXT, j4 TEXT, j5 TEXT, j6 TEXT   -- 机械臂6个关节的设置参数
        );""",

        """CREATE TABLE IF NOT EXISTS robot_application_settings (
            id INTEGER PRIMARY KEY,                                 -- 主键ID
            global_speed TEXT NOT NULL DEFAULT '30',                -- 全局速度设置（百分比）
            true_speed TEXT NOT NULL DEFAULT '1000',                -- 真实速度设置（mm/s）
            end_effector_load TEXT,                                 -- 末端执行器负载
            offset_compensation_x TEXT,                             -- X轴偏移补偿
            offset_compensation_y TEXT,                             -- Y轴偏移补偿
            offset_compensation_z TEXT,                             -- Z轴偏移补偿
            offset_compensation_rx TEXT,                            -- RX轴偏移补偿
            offset_compensation_ry TEXT,                            -- RY轴偏移补偿
            offset_compensation_rz TEXT                             -- RZ轴偏移补偿
        );""",

        """CREATE TABLE IF NOT EXISTS system_settings (
            id INTEGER PRIMARY KEY,                                 -- 主键ID
            language TEXT,                                          -- 语言
            ip TEXT                                                 -- IP地址
        );""",

        """CREATE TABLE IF NOT EXISTS alarm_record (
            id integer NOT NULL PRIMARY KEY,                        -- 主键ID
            alarm_code text NOT NULL,                               -- 报警代码
            alarm_des text NOT NULL,                                -- 报警描述
            time text NOT NULL,                                     -- 报警时间
            status integer NOT NULL DEFAULT 0                      -- 报警状态：0-未处理，1-已处理
        );""",

        # 配方管理相关表
        """CREATE TABLE IF NOT EXISTS formulas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,                   -- 主键ID，自增
            formula_name TEXT NOT NULL UNIQUE,                      -- 配方名称，唯一
            description TEXT,                                       -- 配方描述
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,          -- 创建时间
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP           -- 更新时间
        );""",

        # 箱子/托盘参数表
        """CREATE TABLE IF NOT EXISTS box_pallet_params (
            id INTEGER PRIMARY KEY AUTOINCREMENT,                   -- 主键ID，自增
            formula_id INTEGER NOT NULL,                            -- 关联的配方ID
            direction TEXT NOT NULL DEFAULT '横向',                  -- 方向:横向or纵向
            box_length REAL NOT NULL DEFAULT 0,                    -- 箱子长度(mm)
            box_width REAL NOT NULL DEFAULT 0,                     -- 箱子宽度(mm)
            box_height REAL NOT NULL DEFAULT 0,                    -- 箱子高度(mm)
            box_weight REAL NOT NULL DEFAULT 0,                    -- 箱子重量(kg)
            pallet_length REAL NOT NULL DEFAULT 0,                 -- 托盘长度(mm)
            pallet_width REAL NOT NULL DEFAULT 0,                  -- 托盘宽度(mm)
            pallet_height REAL NOT NULL DEFAULT 0,                 -- 托盘高度(mm)
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,          -- 创建时间
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,          -- 更新时间
            FOREIGN KEY (formula_id) REFERENCES formulas(id) ON DELETE CASCADE
        );""",

        # 层样式配置表 - 支持多层保存
        """CREATE TABLE IF NOT EXISTS layer_style_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,                   -- 主键ID，自增
            formula_id INTEGER NOT NULL,                            -- 关联的配方ID
            layer_index INTEGER DEFAULT 1,                         -- 层索引，从1开始
            pattern_data TEXT NOT NULL,                             -- 层样式数据(JSON格式，包含箱子布局信息)
            auto_mode INTEGER DEFAULT 1,                           -- 自动模式：0-手动，1-自动
            gap_size REAL DEFAULT 10,                              -- 间隙大小(mm)
            rows_count INTEGER DEFAULT 0,                          -- 行数
            cols_count INTEGER DEFAULT 0,                          -- 列数
            row_spacing REAL DEFAULT 0,                            -- 行间距(mm)
            col_spacing REAL DEFAULT 0,                            -- 列间距(mm)
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,          -- 创建时间
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,          -- 更新时间
            FOREIGN KEY (formula_id) REFERENCES formulas(id) ON DELETE CASCADE,
            UNIQUE(formula_id, layer_index)                        -- 确保每个配方的每层只有一条记录
        );""",

        # 垛型配置表
        """CREATE TABLE IF NOT EXISTS stack_type_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,                   -- 主键ID，自增
            formula_id INTEGER NOT NULL,                            -- 关联的配方ID
            left_stack_layers INTEGER DEFAULT 1,                   -- 左垛层数
            right_stack_layers INTEGER DEFAULT 1,                  -- 右垛层数
            left_repeat_enabled INTEGER DEFAULT 0,                 -- 左垛重复模式：0-关闭，1-开启
            right_repeat_enabled INTEGER DEFAULT 0,                -- 右垛重复模式：0-关闭，1-开启
            left_right_consistent INTEGER DEFAULT 0,               -- 左右一致：0-不一致，1-一致
            left_stack_layer_styles TEXT DEFAULT '',               -- 左垛每一层的层样式选择(JSON格式，存储层样式名称数组)
            right_stack_layer_styles TEXT DEFAULT '',              -- 右垛每一层的层样式选择(JSON格式，存储层样式名称数组)
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,          -- 创建时间
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,          -- 更新时间
            FOREIGN KEY (formula_id) REFERENCES formulas(id) ON DELETE CASCADE
        );""",

        # # 创建索引以提高查询性能
        # # 垛盘状态表索引
        # """CREATE INDEX IF NOT EXISTS idx_pallet_status_id ON pallet_status(pallet_id);""",
        # # 作业记录表索引
        # """CREATE INDEX IF NOT EXISTS idx_pallet_work_record_session ON pallet_work_record(work_session_id);""",
        # # 实时数据表索引
        # """CREATE INDEX IF NOT EXISTS idx_pallet_realtime_data_pallet ON pallet_realtime_data(pallet_id, layer_number);""",
        # # 工作站状态表索引
        # """CREATE INDEX IF NOT EXISTS idx_workstation_status_id ON workstation_status(station_id);""",
        # # IO配置表索引
        # """CREATE INDEX IF NOT EXISTS idx_io_configuration_signal ON io_configuration(signal_name);""",
        # # 机械臂位置表索引
        # """CREATE INDEX IF NOT EXISTS idx_robot_position_name ON robot_position_definition(position_name);"""
        # "CREATE TABLE IF NOT EXISTS system_settings (id INTEGER PRIMARY KEY, language TEXT, ip TEXT);",
    ]
    for schema in schemas:
        db_tool.execute_sql(schema)

    # 插入默认数据
    # 垛盘状态默认数据
    if not db_tool.select("pallet_status"):
        db_tool.execute_sql(
            "INSERT INTO pallet_status (pallet_id, pallet_name) VALUES ('left_pallet', '左垛盘'), ('right_pallet', '右垛盘');")

    # 垛盘作业记录默认数据
    if not db_tool.select("pallet_work_record"):
        import uuid
        from datetime import datetime

        # 生成唯一的作业会话ID
        session_id_left = str(uuid.uuid4())
        session_id_right = str(uuid.uuid4())
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        work_record_configs = [
            f"('left_pallet', '{session_id_left}', '{current_time}', NULL, 12, 0, 200, 0, 'active', NULL, NULL)",
            f"('right_pallet', '{session_id_right}', '{current_time}', NULL, 12, 0, 200, 0, 'active', NULL, NULL)"
        ]
        db_tool.execute_sql(
            f"INSERT INTO pallet_work_record (pallet_id, work_session_id, start_time, end_time, total_layers, completed_layers, total_boxes, completed_boxes, status, save_position, formula_id) VALUES {', '.join(work_record_configs)};")

    # IO功能默认数据
    if not db_tool.select("io_functions"):
        io_functions = [
            "('start_signal', 'input', '启动信号')",
            "('pause_signal', 'input', '暂停信号')",
            "('stop_signal', 'input', '停止信号')",
            "('grab_signal', 'input', '抓取信号')",
            "('pressure_detect', 'input', '压力检测')",
            "('vacuum_detect', 'input', '真空检测')",
            "('left_tray_detect', 'input', '左托盘检测')",
            "('right_tray_detect', 'input', '右托盘检测')",
            "('start_light', 'output', '启动指示灯')",
            "('pause_light', 'output', '暂停指示灯')",
            "('stop_light', 'output', '停止指示灯')",
            "('grab_light', 'output', '抓取指示灯')",
            "('pressure_light', 'output', '压力指示灯')",
            "('vacuum_light', 'output', '真空指示灯')",
            "('left_tray_light', 'output', '左托盘指示灯')",
            "('right_tray_light', 'output', '右托盘指示灯')"
        ]
        db_tool.execute_sql(
            f"INSERT INTO io_functions (function_name, function_type, description) VALUES {', '.join(io_functions)};")

    # IO端口映射默认数据
    if not db_tool.select("io_port_mapping"):
        io_mappings = [
            "('start_signal', 'DI', 0, 'low')",
            "('pause_signal', 'DI', 1, 'low')",
            "('stop_signal', 'DI', 2, 'low')",
            "('grab_signal', 'DI', 3, 'low')",
            "('pressure_detect', 'DI', 4, 'low')",
            "('vacuum_detect', 'DI', 5, 'low')",
            "('left_tray_detect', 'DI', 6, 'low')",
            "('right_tray_detect', 'DI', 7, 'low')",
            "('start_light', 'DO', 0, 'high')",
            "('pause_light', 'DO', 1, 'high')",
            "('stop_light', 'DO', 2, 'high')",
            "('grab_light', 'DO', 3, 'high')",
            "('pressure_light', 'DO', 4, 'high')",
            "('vacuum_light', 'DO', 5, 'high')",
            "('left_tray_light', 'DO', 6, 'high')",
            "('right_tray_light', 'DO', 7, 'high')"
        ]
        db_tool.execute_sql(
            f"INSERT INTO io_port_mapping (function_name, io_type, port_number, active_level) VALUES {', '.join(io_mappings)};")

    # 初始化IO状态缓存
    if not db_tool.select("io_status_cache"):
        io_status_init = []
        # 初始化DI0-DI7
        for i in range(8):
            io_status_init.append(f"('DI', {i}, 0)")
        # 初始化DO0-DO7
        for i in range(8):
            io_status_init.append(f"('DO', {i}, 0)")
        db_tool.execute_sql(
            f"INSERT INTO io_status_cache (io_type, port_number, current_value) VALUES {', '.join(io_status_init)};")

    if not db_tool.select("system_settings"):
        db_tool.insert("system_settings", {"language": "中文", "ip": "************"})

    if not db_tool.select("ip_settings"):
        db_tool.insert("ip_settings", {"ip": "************"})


    print("应用数据表初始化完成。")


def notify_launcher_if_needed():
    if "--started-by-launcher" in sys.argv:
        socket = QLocalSocket()
        socket.connectToServer("PalletizerLink_Launcher_Signal_Server_12345")
        socket.waitForConnected(200)


if __name__ == "__main__":
    os_type = platform.system()
    if os_type == 'Linux':
        gv.system_type = "Linux"
        try:
            os.system('date -s "2025-06-06 6:6:00"')
            os.system('echo "nameserver *******" > /etc/resolv.conf')
        except Exception as e:
            print(f"设置系统时间失败: {e}")

    if gv.system_type == "Linux":
        os.environ["QT_IM_MODULE"] = "qtvirtualkeyboard"
        os.environ["QT_IM_MODULE"] = "Qt5Input"
        os.environ["QT_QUICK_BACKEND"] = "software"

    app = QApplication(sys.argv)

    if gv.system_type != "Linux":
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseSoftwareOpenGL)

    if gv.system_type == "Linux":
        QTimer.singleShot(0, lambda: initialize_app(app, ''))
    else:
        resource_path = getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__)))
        QTimer.singleShot(0, lambda: initialize_app(app, resource_path))

    sys.exit(app.exec())
