# 协作机器人上位机应用

基于PySide6开发的协作机器人上位机应用程序。

## 项目结构

```
weldRobot/
├── docs/                   # 项目文档
├── src/                    # 源代码
│   ├── logic/              # 业务逻辑
│   ├── ui/                 # UI界面代码
│   ├── common/             # 通用工具类
│   ├── resources/          # 资源文件
│   │   ├── icons/          # 图标资源
│   │   ├── styles/         # 样式表
│   ├── tests/              # 单元测试
│── main.py             # 程序入口
├── requirements.txt        # Python依赖
├── setup.py               # 打包配置
└── .gitignore             # Git忽略规则
```

## 开发环境

- Python 3.12+
- PySide6

## 项目运行

python main.py

## 项目打包(简单打包命令，实际开发中需要根据具体情况调整)
1.
    pyinstaller main.spec
2.
    使用innosetup软件打开install_script.iss文件
    innosetup软件下载链接 https://files.jrsoftware.org/is/6/innosetup-6.4.3.exe

3. 
    使用Ctrl+F9完成打包操作
4. 
    双击Output文件夹内的mysetup.exe完成软件的安装


## 项目打包-新
1.      
        pyinstaller --onefile --windowed --name "Launcher" --icon="app_icon.ico" --collect-all pyside6 --add-data "src/resources;src/resources" launcher.py
        
        pyinstaller main.spec

         将launcher.exe文件与WeldRobotApp.exe放在同文件夹
2.
    使用innosetup软件打开install_script.iss文件
    innosetup软件下载链接 https://files.jrsoftware.org/is/6/innosetup-6.4.3.exe

3. 
    使用Ctrl+F9完成打包操作
4. 
    双击Output文件夹内的mysetup.exe完成软件的安装

## 国际化

1. 通过pylupdate6 ***.py -ts ***_en.ts(生成英文的ts文件)
   通过pylupdate6 ***.py -ts ***_zh.ts(生成中文的ts文件)
2. 生成ts文件后，使用linguistQt语言家对文件进行翻译
   翻译后点击保存，然后进行发布，会在ts文件相同的文件夹生成对应的英文和中文qm文件
