#增加软件的启动速度脚本
#pyinstaller --onefile --windowed --name "Launcher" --icon="icon.ico" --collect-all pyside6 launcher.py

# pyinstaller --onefile --windowed --name "Launcher" --icon="app_icon.ico" --collect-all pyside6 --add-data "src/resources;src/resources" launcher.py
import sys
import os
import subprocess
from PySide6.QtWidgets import QApplication, QSplashScreen
from PySide6.QtGui import QMovie, QPixmap, Qt
from PySide6.QtNetwork import QLocalServer, QLocalSocket

# 定义一个唯一的本地服务器名称，用于通信
LAUNCHER_SIGNAL_NAME = "WeldingLink_Launcher_Signal_Server_12345"


def main():
    app = QApplication(sys.argv)

    # 强制软件渲染，避免在低端设备上出问题
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseSoftwareOpenGL)

    # --- 1. 立即显示启动动画 ---
    # 这里的路径逻辑和主程序保持一致
    resource_path = getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__)))
    gif_path = os.path.join(resource_path, 'src/resources/start.gif')

    if not os.path.exists(gif_path):
        # 如果找不到gif，直接尝试启动主程序并退出
        # 注意: MainApp.exe需要和Launcher.exe在同一个目录下
        main_app_path = os.path.join(os.path.dirname(sys.executable), 'WeldRobotApp.exe')
        if os.path.exists(main_app_path):
            subprocess.Popen([main_app_path])
        sys.exit(0)

    movie = QMovie(gif_path)
    splash = QSplashScreen(QPixmap(movie.frameRect().size()))
    splash.setWindowFlag(Qt.WindowStaysOnTopHint, True)
    splash.showMessage("正在启动，请稍候...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)

    movie.frameChanged.connect(lambda: splash.setPixmap(movie.currentPixmap()))
    movie.start()
    splash.show()
    app.processEvents()

    # --- 2. 在后台启动主程序 ---
    # 假设主程序打包后叫 MainApp.exe，并且和 Launcher.exe 在同一个目录
    main_app_path = os.path.join(os.path.dirname(sys.executable), 'WeldRobotApp.exe')
    if os.path.exists(main_app_path):
        # 传递一个特殊参数，告诉主程序它是由启动器启动的
        subprocess.Popen([main_app_path, "--started-by-launcher"])
    else:
        # 如果找不到主程序，显示错误并退出
        splash.showMessage("错误：找不到主程序文件！", Qt.AlignBottom | Qt.AlignCenter, Qt.red)
        # 停留几秒让用户看到
        app.exec()  # 这会阻塞，直到用户关闭splash
        sys.exit(1)

    # --- 3. 等待主程序的“就绪”信号 ---
    # 我们使用QLocalServer来接收这个信号
    server = QLocalServer()

    def on_main_app_ready():
        # 收到信号后，关闭启动器
        server.close()
        app.quit()

    server.newConnection.connect(on_main_app_ready)

    # 监听信号，如果监听失败（比如被占用了），也直接退出
    if not server.listen(LAUNCHER_SIGNAL_NAME):
        # 可能已有启动器在运行，直接退出
        server.close()
        sys.exit(0)

    app.exec()  # 进入事件循环，等待信号


if __name__ == '__main__':
    main()