<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>formula</class>
 <widget class="QWidget" name="formula">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>857</width>
    <height>510</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>840</width>
    <height>510</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QFrame" name="frame">
   <property name="geometry">
    <rect>
     <x>12</x>
     <y>40</y>
     <width>815</width>
     <height>64</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>815</width>
     <height>64</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>815</width>
     <height>64</height>
    </size>
   </property>
   <property name="frameShape">
    <enum>QFrame::Shape::StyledPanel</enum>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Shadow::Raised</enum>
   </property>
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>10</y>
      <width>383</width>
      <height>43</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Policy::Fixed</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>13</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="input_btn">
       <property name="minimumSize">
        <size>
         <width>104</width>
         <height>40</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>104</width>
         <height>40</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
        background-color: #3498db;
        color: white;
        border-radius: 12px;
    }
    QPushButton:hover {
        background-color: #3498db;  /* 保持原色，不改变 */
    }
	QPushButton:pressed{
        padding-top: 8px;     /* 上边距增加 */
        padding-bottom: 4px;  /* 下边距减少 */
}</string>
       </property>
       <property name="text">
        <string>导入</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Policy::Fixed</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>13</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="export_btn">
       <property name="minimumSize">
        <size>
         <width>104</width>
         <height>40</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>104</width>
         <height>40</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
        background-color: #17ACAB;
        color: white;
        border-radius: 12px;
    }
    QPushButton:hover {
        background-color: #17ACAB;  /* 保持原色，不改变 */
    }
	QPushButton:pressed{
        padding-top: 8px;     /* 上边距增加 */
        padding-bottom: 4px;  /* 下边距减少 */
}</string>
       </property>
       <property name="text">
        <string>导出</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Policy::Fixed</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>13</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="add_btn">
       <property name="minimumSize">
        <size>
         <width>104</width>
         <height>39</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>104</width>
         <height>40</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>12</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
        background-color: #37A918;
        color: white;
        border-radius: 12px;
    }
    QPushButton:hover {
        background-color: #37A918;  /* 保持原色，不改变 */
    }
	QPushButton:pressed{
        padding-top: 8px;     /* 上边距增加 */
        padding-bottom: 4px;  /* 下边距减少 */
}</string>
       </property>
       <property name="text">
        <string>新增</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QFrame" name="frame_2">
   <property name="geometry">
    <rect>
     <x>12</x>
     <y>110</y>
     <width>815</width>
     <height>397</height>
    </rect>
   </property>
   <property name="minimumSize">
    <size>
     <width>815</width>
     <height>397</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>815</width>
     <height>397</height>
    </size>
   </property>
   <property name="frameShape">
    <enum>QFrame::Shape::StyledPanel</enum>
   </property>
   <property name="frameShadow">
    <enum>QFrame::Shadow::Raised</enum>
   </property>
   <widget class="QTableWidget" name="formula_table">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>791</width>
      <height>381</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>785</width>
      <height>0</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>100000</width>
      <height>100000</height>
     </size>
    </property>
    <property name="styleSheet">
     <string notr="true"/>
    </property>
    <property name="rowCount">
     <number>0</number>
    </property>
    <attribute name="horizontalHeaderMinimumSectionSize">
     <number>50</number>
    </attribute>
    <attribute name="horizontalHeaderDefaultSectionSize">
     <number>80</number>
    </attribute>
    <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
     <bool>false</bool>
    </attribute>
    <attribute name="horizontalHeaderStretchLastSection">
     <bool>false</bool>
    </attribute>
    <attribute name="verticalHeaderVisible">
     <bool>false</bool>
    </attribute>
    <attribute name="verticalHeaderDefaultSectionSize">
     <number>50</number>
    </attribute>
    <column>
     <property name="text">
      <string>序号</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>配方名称</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>箱子长度</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>箱子宽度</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>箱子高度</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>托盘X方向长度</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>托盘Y方向长度</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>托盘高度</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>左垛层数</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>右垛层数</string>
     </property>
    </column>
    <column>
     <property name="text">
      <string>操作</string>
     </property>
    </column>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
