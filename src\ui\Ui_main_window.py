# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'main_window.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ursor,
    <PERSON><PERSON><PERSON>, QFontData<PERSON>, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QFrame, QHBoxLayout, QLabel,
    QPushButton, QSizePolicy, QSlider, QSpacerItem,
    QStackedWidget, QVBoxLayout, QWidget)

from ..resources.main_window import resources_rc

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(1024, 795)
        self.mainVerticalLayout = QVBoxLayout(Form)
        self.mainVerticalLayout.setSpacing(0)
        self.mainVerticalLayout.setObjectName(u"mainVerticalLayout")
        self.mainVerticalLayout.setContentsMargins(0, 0, 0, 0)
        self.frame_5 = QFrame(Form)
        self.frame_5.setObjectName(u"frame_5")
        self.frame_5.setMinimumSize(QSize(0, 80))
        self.frame_5.setMaximumSize(QSize(16777215, 80))
        self.frame_5.setStyleSheet(u"    background-image: url(:/\u6807\u9898\u680f\u80cc\u666f/\u6807\u9898\u680f\u80cc\u666f2x.png);\n"
"    background-repeat: no-repeat;\n"
"    background-position: center;\n"
"    background-size: 100% 100%;\n"
"    border: none;\n"
"")
        self.frame_5.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_5.setFrameShadow(QFrame.Shadow.Raised)
        self.topHorizontalLayout = QHBoxLayout(self.frame_5)
        self.topHorizontalLayout.setSpacing(10)
        self.topHorizontalLayout.setObjectName(u"topHorizontalLayout")
        self.topHorizontalLayout.setContentsMargins(20, 10, 20, 10)
        self.NameLabel_2 = QLabel(self.frame_5)
        self.NameLabel_2.setObjectName(u"NameLabel_2")
        self.NameLabel_2.setMinimumSize(QSize(325, 43))
        self.NameLabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font-weight: 400;\n"
"font-size: 14px;\n"
"color: #F53D3A;\n"
"text-align: left;\n"
"background-image: url(:/logo/LOGO.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")
        self.NameLabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.topHorizontalLayout.addWidget(self.NameLabel_2)

        self.topHorizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.topHorizontalLayout.addItem(self.topHorizontalSpacer)

        self.Speedlabel_3 = QLabel(self.frame_5)
        self.Speedlabel_3.setObjectName(u"Speedlabel_3")
        self.Speedlabel_3.setMinimumSize(QSize(16, 16))
        self.Speedlabel_3.setStyleSheet(u"font-family: MiSans;\n"
"font: 18pt \"Microsoft YaHei UI\";\n"
"font-weight: 400;\n"
"font-size: 18px;\n"
"color: #030B1C;\n"
"text-align: left;\n"
"background-image: url(:/time/\u65f6\u95f4.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")
        self.Speedlabel_3.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.topHorizontalLayout.addWidget(self.Speedlabel_3)

        self.TimeLabel_2 = QLabel(self.frame_5)
        self.TimeLabel_2.setObjectName(u"TimeLabel_2")
        self.TimeLabel_2.setMinimumSize(QSize(141, 29))
        self.TimeLabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font-weight: 400;\n"
"font: 16pt \"Microsoft YaHei UI\";\n"
"color: #B4B4B4;\n"
"text-align: left;\n"
"")
        self.TimeLabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.topHorizontalLayout.addWidget(self.TimeLabel_2)

        self.translateButtonsLayout = QHBoxLayout()
        self.translateButtonsLayout.setSpacing(0)
        self.translateButtonsLayout.setObjectName(u"translateButtonsLayout")
        self.translateButtonsLayout.setContentsMargins(0, 0, 0, 0)
        self.Translate1Label_2 = QPushButton(self.frame_5)
        self.Translate1Label_2.setObjectName(u"Translate1Label_2")
        self.Translate1Label_2.setMinimumSize(QSize(30, 29))
        self.Translate1Label_2.setMaximumSize(QSize(30, 29))
        font = QFont()
        font.setPointSize(11)
        self.Translate1Label_2.setFont(font)
        self.Translate1Label_2.setStyleSheet(u"background-image: url(:/Chinese/\u4e2d\u82f1\u6587\u4e2d\u6587.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")

        self.translateButtonsLayout.addWidget(self.Translate1Label_2)

        self.Translate2Label_2 = QPushButton(self.frame_5)
        self.Translate2Label_2.setObjectName(u"Translate2Label_2")
        self.Translate2Label_2.setMinimumSize(QSize(30, 29))
        self.Translate2Label_2.setMaximumSize(QSize(30, 29))
        self.Translate2Label_2.setFont(font)
        self.Translate2Label_2.setStyleSheet(u"background-image: url(:/English/\u4e2d\u82f1\u6587 \u82f1\u6587.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")

        self.translateButtonsLayout.addWidget(self.Translate2Label_2)


        self.topHorizontalLayout.addLayout(self.translateButtonsLayout)

        self.Scale_downButton_2 = QPushButton(self.frame_5)
        self.Scale_downButton_2.setObjectName(u"Scale_downButton_2")
        self.Scale_downButton_2.setMinimumSize(QSize(30, 29))
        self.Scale_downButton_2.setMaximumSize(QSize(30, 29))
        self.Scale_downButton_2.setFont(font)
        self.Scale_downButton_2.setStyleSheet(u"background-image: url(:/small/\u7f29\u5c0f.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")

        self.topHorizontalLayout.addWidget(self.Scale_downButton_2)

        self.Scale_upButton_2 = QPushButton(self.frame_5)
        self.Scale_upButton_2.setObjectName(u"Scale_upButton_2")
        self.Scale_upButton_2.setMinimumSize(QSize(30, 29))
        self.Scale_upButton_2.setMaximumSize(QSize(30, 29))
        self.Scale_upButton_2.setFont(font)
        self.Scale_upButton_2.setStyleSheet(u"color: #F53D3A;\n"
"background-image: url(:/big/\u7a97\u53e3.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")

        self.topHorizontalLayout.addWidget(self.Scale_upButton_2)

        self.CloseButton_2 = QPushButton(self.frame_5)
        self.CloseButton_2.setObjectName(u"CloseButton_2")
        self.CloseButton_2.setMinimumSize(QSize(30, 29))
        self.CloseButton_2.setMaximumSize(QSize(30, 29))
        self.CloseButton_2.setFont(font)
        self.CloseButton_2.setStyleSheet(u"background-image: url(:/close/\u5173\u95ed.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")

        self.topHorizontalLayout.addWidget(self.CloseButton_2)


        self.mainVerticalLayout.addWidget(self.frame_5)

        self.frame_2 = QFrame(Form)
        self.frame_2.setObjectName(u"frame_2")
        self.frame_2.setMinimumSize(QSize(0, 71))
        self.frame_2.setMaximumSize(QSize(16777215, 71))
        self.frame_2.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px;")
        self.frame_2.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_2.setFrameShadow(QFrame.Shadow.Raised)
        self.controlHorizontalLayout = QHBoxLayout(self.frame_2)
        self.controlHorizontalLayout.setSpacing(10)
        self.controlHorizontalLayout.setObjectName(u"controlHorizontalLayout")
        self.controlHorizontalLayout.setContentsMargins(30, 10, 20, 10)
        self.Speedlabel_2 = QLabel(self.frame_2)
        self.Speedlabel_2.setObjectName(u"Speedlabel_2")
        self.Speedlabel_2.setMinimumSize(QSize(41, 21))
        self.Speedlabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font: 18pt \"Microsoft YaHei UI\";\n"
"font-weight: 400;\n"
"font-size: 18px;\n"
"color: #030B1C;\n"
"text-align: left;")
        self.Speedlabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.controlHorizontalLayout.addWidget(self.Speedlabel_2)

        self.speedControlLayout = QHBoxLayout()
        self.speedControlLayout.setSpacing(0)
        self.speedControlLayout.setObjectName(u"speedControlLayout")
        self.speedControlLayout.setContentsMargins(0, 0, 0, 0)
        self.DownButton_2 = QPushButton(self.frame_2)
        self.DownButton_2.setObjectName(u"DownButton_2")
        self.DownButton_2.setMinimumSize(QSize(40, 40))
        self.DownButton_2.setMaximumSize(QSize(40, 40))
        self.DownButton_2.setFont(font)
        self.DownButton_2.setStyleSheet(u"background-image: url(:/Down/\u51cf.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")

        self.speedControlLayout.addWidget(self.DownButton_2)

        self.pushSlider_2 = QSlider(self.frame_2)
        self.pushSlider_2.setObjectName(u"pushSlider_2")
        self.pushSlider_2.setOrientation(Qt.Orientation.Horizontal)

        self.speedControlLayout.addWidget(self.pushSlider_2)

        self.UPButton_2 = QPushButton(self.frame_2)
        self.UPButton_2.setObjectName(u"UPButton_2")
        self.UPButton_2.setMinimumSize(QSize(40, 40))
        self.UPButton_2.setMaximumSize(QSize(40, 40))
        self.UPButton_2.setFont(font)
        self.UPButton_2.setStyleSheet(u"background-image: url(:/Up/\u52a0.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")

        self.speedControlLayout.addWidget(self.UPButton_2)

        self.Speedvaluelabel_2 = QLabel(self.frame_2)
        self.Speedvaluelabel_2.setObjectName(u"Speedvaluelabel_2")
        self.Speedvaluelabel_2.setMinimumSize(QSize(41, 21))
        self.Speedvaluelabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font: 18pt \"Microsoft YaHei UI\";\n"
"font-weight: 400;\n"
"font-size: 18px;\n"
"color: #030B1C;\n"
"text-align: left;")
        self.Speedvaluelabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.speedControlLayout.addWidget(self.Speedvaluelabel_2)


        self.controlHorizontalLayout.addLayout(self.speedControlLayout)

        self.controlHorizontalSpacer = QSpacerItem(30, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.controlHorizontalLayout.addItem(self.controlHorizontalSpacer)

        self.ModeButton_2 = QPushButton(self.frame_2)
        self.ModeButton_2.setObjectName(u"ModeButton_2")
        self.ModeButton_2.setMinimumSize(QSize(105, 45))
        font1 = QFont()
        font1.setPointSize(14)
        self.ModeButton_2.setFont(font1)
        self.ModeButton_2.setStyleSheet(u"")

        self.controlHorizontalLayout.addWidget(self.ModeButton_2)

        self.ClearalarmButton_2 = QPushButton(self.frame_2)
        self.ClearalarmButton_2.setObjectName(u"ClearalarmButton_2")
        self.ClearalarmButton_2.setMinimumSize(QSize(124, 45))
        self.ClearalarmButton_2.setFont(font1)
        self.ClearalarmButton_2.setStyleSheet(u"background-image: url(:/clearAlarm/\u6e05\u9664\u62a5\u8b66(1).png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")

        self.controlHorizontalLayout.addWidget(self.ClearalarmButton_2)

        self.EStopButton_9 = QPushButton(self.frame_2)
        self.EStopButton_9.setObjectName(u"EStopButton_9")
        self.EStopButton_9.setMinimumSize(QSize(84, 45))
        self.EStopButton_9.setMaximumSize(QSize(84, 45))
        font2 = QFont()
        font2.setPointSize(16)
        self.EStopButton_9.setFont(font2)
        self.EStopButton_9.setStyleSheet(u"background-image: url(:/Estop/STOP(1).png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")

        self.controlHorizontalLayout.addWidget(self.EStopButton_9)


        self.mainVerticalLayout.addWidget(self.frame_2)

        self.mainContentLayout = QHBoxLayout()
        self.mainContentLayout.setSpacing(10)
        self.mainContentLayout.setObjectName(u"mainContentLayout")
        self.mainContentLayout.setContentsMargins(10, 0, 10, 0)
        self.leftVerticalLayout = QVBoxLayout()
        self.leftVerticalLayout.setSpacing(0)
        self.leftVerticalLayout.setObjectName(u"leftVerticalLayout")
        self.leftVerticalSpacer_2 = QSpacerItem(20, 5, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)

        self.leftVerticalLayout.addItem(self.leftVerticalSpacer_2)

        self.RunButton_2 = QPushButton(Form)
        self.RunButton_2.setObjectName(u"RunButton_2")
        self.RunButton_2.setMinimumSize(QSize(150, 110))
        self.RunButton_2.setMaximumSize(QSize(191, 16777215))
        self.RunButton_2.setFont(font2)
        self.RunButton_2.setStyleSheet(u"")

        self.leftVerticalLayout.addWidget(self.RunButton_2)

        self.FormulaButton_2 = QPushButton(Form)
        self.FormulaButton_2.setObjectName(u"FormulaButton_2")
        self.FormulaButton_2.setMinimumSize(QSize(150, 110))
        self.FormulaButton_2.setMaximumSize(QSize(191, 16777215))
        self.FormulaButton_2.setFont(font2)
        self.FormulaButton_2.setStyleSheet(u"")

        self.leftVerticalLayout.addWidget(self.FormulaButton_2)

        self.TeachingButton_2 = QPushButton(Form)
        self.TeachingButton_2.setObjectName(u"TeachingButton_2")
        self.TeachingButton_2.setMinimumSize(QSize(150, 110))
        self.TeachingButton_2.setMaximumSize(QSize(191, 16777215))
        self.TeachingButton_2.setFont(font2)
        self.TeachingButton_2.setStyleSheet(u"")

        self.leftVerticalLayout.addWidget(self.TeachingButton_2)

        self.IOButton_2 = QPushButton(Form)
        self.IOButton_2.setObjectName(u"IOButton_2")
        self.IOButton_2.setMinimumSize(QSize(150, 110))
        self.IOButton_2.setMaximumSize(QSize(191, 16777215))
        self.IOButton_2.setFont(font2)
        self.IOButton_2.setStyleSheet(u"")

        self.leftVerticalLayout.addWidget(self.IOButton_2)

        self.LogButton_2 = QPushButton(Form)
        self.LogButton_2.setObjectName(u"LogButton_2")
        self.LogButton_2.setMinimumSize(QSize(150, 110))
        self.LogButton_2.setMaximumSize(QSize(191, 16777215))
        self.LogButton_2.setFont(font2)
        self.LogButton_2.setStyleSheet(u"")

        self.leftVerticalLayout.addWidget(self.LogButton_2)

        self.leftVerticalSpacer = QSpacerItem(20, 5, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)

        self.leftVerticalLayout.addItem(self.leftVerticalSpacer)


        self.mainContentLayout.addLayout(self.leftVerticalLayout)

        self.stackedWidget = QStackedWidget(Form)
        self.stackedWidget.setObjectName(u"stackedWidget")
        self.stackedWidget.setMinimumSize(QSize(600, 400))
        self.page_3 = QWidget()
        self.page_3.setObjectName(u"page_3")
        self.stackedWidget.addWidget(self.page_3)
        self.page_4 = QWidget()
        self.page_4.setObjectName(u"page_4")
        self.stackedWidget.addWidget(self.page_4)

        self.mainContentLayout.addWidget(self.stackedWidget)


        self.mainVerticalLayout.addLayout(self.mainContentLayout)

        self.frame_4 = QFrame(Form)
        self.frame_4.setObjectName(u"frame_4")
        self.frame_4.setMinimumSize(QSize(0, 80))
        self.frame_4.setMaximumSize(QSize(16777215, 80))
        self.frame_4.setStyleSheet(u"background: #FAFBFD;")
        self.frame_4.setFrameShape(QFrame.Shape.StyledPanel)
        self.frame_4.setFrameShadow(QFrame.Shadow.Raised)
        self.bottomHorizontalLayout = QHBoxLayout(self.frame_4)
        self.bottomHorizontalLayout.setSpacing(15)
        self.bottomHorizontalLayout.setObjectName(u"bottomHorizontalLayout")
        self.bottomHorizontalLayout.setContentsMargins(20, 10, 20, 10)
        self.leftBottomLayout = QHBoxLayout()
        self.leftBottomLayout.setSpacing(10)
        self.leftBottomLayout.setObjectName(u"leftBottomLayout")
        self.AlarmLabel = QLabel(self.frame_4)
        self.AlarmLabel.setObjectName(u"AlarmLabel")
        self.AlarmLabel.setMinimumSize(QSize(68, 64))
        self.AlarmLabel.setStyleSheet(u"")
        self.AlarmLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.leftBottomLayout.addWidget(self.AlarmLabel)

        self.Alarminforlabel_2 = QLabel(self.frame_4)
        self.Alarminforlabel_2.setObjectName(u"Alarminforlabel_2")
        self.Alarminforlabel_2.setMinimumSize(QSize(193, 29))
        self.Alarminforlabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font-weight: 400;\n"
"font-size: 14px;\n"
"color: #F53D3A;\n"
"text-align: left;")
        self.Alarminforlabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.leftBottomLayout.addWidget(self.Alarminforlabel_2)


        self.bottomHorizontalLayout.addLayout(self.leftBottomLayout)

        self.dateTimeLayout = QVBoxLayout()
        self.dateTimeLayout.setObjectName(u"dateTimeLayout")
        self.datalabel_2 = QLabel(self.frame_4)
        self.datalabel_2.setObjectName(u"datalabel_2")
        self.datalabel_2.setMinimumSize(QSize(81, 29))
        self.datalabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font-weight: 400;\n"
"font-size: 14px;\n"
"color: #B4B4B4;\n"
"text-align: left;")
        self.datalabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.dateTimeLayout.addWidget(self.datalabel_2)

        self.hourlabel_2 = QLabel(self.frame_4)
        self.hourlabel_2.setObjectName(u"hourlabel_2")
        self.hourlabel_2.setMinimumSize(QSize(81, 29))
        self.hourlabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font-weight: 400;\n"
"font-size: 14px;\n"
"color: #B4B4B4;\n"
"text-align: left;")
        self.hourlabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.dateTimeLayout.addWidget(self.hourlabel_2)


        self.bottomHorizontalLayout.addLayout(self.dateTimeLayout)

        self.collisionLayout = QHBoxLayout()
        self.collisionLayout.setSpacing(10)
        self.collisionLayout.setObjectName(u"collisionLayout")
        self.ColliLabel = QLabel(self.frame_4)
        self.ColliLabel.setObjectName(u"ColliLabel")
        self.ColliLabel.setMinimumSize(QSize(56, 58))
        self.ColliLabel.setStyleSheet(u"font-family: MiSans;\n"
"font: 18pt \"Microsoft YaHei UI\";\n"
"font-weight: 400;\n"
"font-size: 18px;\n"
"color: #030B1C;\n"
"text-align: left;\n"
"background-image: url(:/collision/\u78b0\u649e.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;")
        self.ColliLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.collisionLayout.addWidget(self.ColliLabel)

        self.collisionControlLayout = QHBoxLayout()
        self.collisionControlLayout.setSpacing(0)
        self.collisionControlLayout.setObjectName(u"collisionControlLayout")
        self.collisionControlLayout.setContentsMargins(0, 0, 0, 0)
        self.downcopushButton_2 = QPushButton(self.frame_4)
        self.downcopushButton_2.setObjectName(u"downcopushButton_2")
        self.downcopushButton_2.setMinimumSize(QSize(40, 40))
        self.downcopushButton_2.setMaximumSize(QSize(40, 40))
        self.downcopushButton_2.setFont(font)
        self.downcopushButton_2.setStyleSheet(u"background-image: url(:/Down/\u51cf.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.collisionControlLayout.addWidget(self.downcopushButton_2)

        self.pushSlider = QSlider(self.frame_4)
        self.pushSlider.setObjectName(u"pushSlider")
        self.pushSlider.setOrientation(Qt.Orientation.Horizontal)

        self.collisionControlLayout.addWidget(self.pushSlider)

        self.UpcoButton_2 = QPushButton(self.frame_4)
        self.UpcoButton_2.setObjectName(u"UpcoButton_2")
        self.UpcoButton_2.setMinimumSize(QSize(40, 40))
        self.UpcoButton_2.setMaximumSize(QSize(40, 40))
        self.UpcoButton_2.setFont(font)
        self.UpcoButton_2.setStyleSheet(u"background-image: url(:/Up/\u52a0.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.collisionControlLayout.addWidget(self.UpcoButton_2)

        self.collilabellabel_2 = QLabel(self.frame_4)
        self.collilabellabel_2.setObjectName(u"collilabellabel_2")
        self.collilabellabel_2.setMinimumSize(QSize(31, 21))
        self.collilabellabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font: 18pt \"Microsoft YaHei UI\";\n"
"font-weight: 400;\n"
"font-size: 14px;\n"
"color: #030B1C;\n"
"text-align: left;")
        self.collilabellabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.collisionControlLayout.addWidget(self.collilabellabel_2)


        self.collisionLayout.addLayout(self.collisionControlLayout)


        self.bottomHorizontalLayout.addLayout(self.collisionLayout)

        self.networkLayout = QHBoxLayout()
        self.networkLayout.setSpacing(10)
        self.networkLayout.setObjectName(u"networkLayout")
        self.bottomHorizontalSpacer_2 = QSpacerItem(30, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.networkLayout.addItem(self.bottomHorizontalSpacer_2)

        self.IpButton_2 = QPushButton(self.frame_4)
        self.IpButton_2.setObjectName(u"IpButton_2")
        self.IpButton_2.setMinimumSize(QSize(40, 40))
        self.IpButton_2.setMaximumSize(QSize(40, 40))
        self.IpButton_2.setFont(font)
        self.IpButton_2.setStyleSheet(u"color: #F53D3A;\n"
"background-image: url(:/IP/IP\u8bbe\u7f6e.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.networkLayout.addWidget(self.IpButton_2)

        self.IpButton_3 = QPushButton(self.frame_4)
        self.IpButton_3.setObjectName(u"IpButton_3")
        self.IpButton_3.setMinimumSize(QSize(40, 40))
        self.IpButton_3.setMaximumSize(QSize(40, 40))
        self.IpButton_3.setFont(font)
        self.IpButton_3.setStyleSheet(u"color: #F53D3A;\n"
"border: none;\n"
"background-image: url(:/connect/\u5df2\u8fde\u63a5.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.networkLayout.addWidget(self.IpButton_3)

        self.Ipinforlabel_2 = QLabel(self.frame_4)
        self.Ipinforlabel_2.setObjectName(u"Ipinforlabel_2")
        self.Ipinforlabel_2.setMinimumSize(QSize(41, 29))
        self.Ipinforlabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font-weight: 400;\n"
"font-size: 14px;\n"
"color: #F53D3A;\n"
"text-align: left;")
        self.Ipinforlabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.networkLayout.addWidget(self.Ipinforlabel_2)


        self.bottomHorizontalLayout.addLayout(self.networkLayout)

        self.bottomHorizontalSpacer = QSpacerItem(30, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.bottomHorizontalLayout.addItem(self.bottomHorizontalSpacer)

        self.versionLayout = QVBoxLayout()
        self.versionLayout.setObjectName(u"versionLayout")
        self.CVlabel_2 = QLabel(self.frame_4)
        self.CVlabel_2.setObjectName(u"CVlabel_2")
        self.CVlabel_2.setMinimumSize(QSize(101, 29))
        self.CVlabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font-weight: 400;\n"
"font-size: 14px;\n"
"color: #B4B4B4;\n"
"text-align: left;")
        self.CVlabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.versionLayout.addWidget(self.CVlabel_2)

        self.Versionlabel_2 = QLabel(self.frame_4)
        self.Versionlabel_2.setObjectName(u"Versionlabel_2")
        self.Versionlabel_2.setMinimumSize(QSize(91, 29))
        self.Versionlabel_2.setStyleSheet(u"font-family: MiSans;\n"
"font-weight: 400;\n"
"font-size: 14px;\n"
"color: #B4B4B4;\n"
"text-align: left;")
        self.Versionlabel_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.versionLayout.addWidget(self.Versionlabel_2)


        self.bottomHorizontalLayout.addLayout(self.versionLayout)


        self.mainVerticalLayout.addWidget(self.frame_4)


        self.retranslateUi(Form)

        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.NameLabel_2.setText("")
        self.Speedlabel_3.setText("")
        self.TimeLabel_2.setText(QCoreApplication.translate("Form", u"2025/07/04  11:36:20", None))
#if QT_CONFIG(tooltip)
        self.Translate1Label_2.setToolTip(QCoreApplication.translate("Form", u"\u4e2d\u6587", None))
#endif // QT_CONFIG(tooltip)
        self.Translate1Label_2.setText("")
#if QT_CONFIG(tooltip)
        self.Translate2Label_2.setToolTip(QCoreApplication.translate("Form", u"English", None))
#endif // QT_CONFIG(tooltip)
        self.Translate2Label_2.setText("")
#if QT_CONFIG(tooltip)
        self.Scale_downButton_2.setToolTip(QCoreApplication.translate("Form", u"\u6700\u5c0f\u5316\u7a97\u53e3", None))
#endif // QT_CONFIG(tooltip)
        self.Scale_downButton_2.setText("")
#if QT_CONFIG(tooltip)
        self.Scale_upButton_2.setToolTip(QCoreApplication.translate("Form", u"\u6700\u5927\u5316/\u8fd8\u539f\u7a97\u53e3", None))
#endif // QT_CONFIG(tooltip)
        self.Scale_upButton_2.setText("")
#if QT_CONFIG(tooltip)
        self.CloseButton_2.setToolTip(QCoreApplication.translate("Form", u"\u5173\u95ed\u7a97\u53e3", None))
#endif // QT_CONFIG(tooltip)
        self.CloseButton_2.setText("")
        self.Speedlabel_2.setText(QCoreApplication.translate("Form", u"\u901f\u5ea6", None))
        self.DownButton_2.setText("")
        self.UPButton_2.setText("")
        self.Speedvaluelabel_2.setText(QCoreApplication.translate("Form", u"50%", None))
        self.ModeButton_2.setText("")
        self.ClearalarmButton_2.setText("")
        self.EStopButton_9.setText("")
        self.RunButton_2.setText("")
        self.FormulaButton_2.setText("")
        self.TeachingButton_2.setText("")
        self.IOButton_2.setText("")
        self.LogButton_2.setText("")
        self.AlarmLabel.setText("")
        self.Alarminforlabel_2.setText(QCoreApplication.translate("Form", u"6\u8f74\u8f6f\u9650\u4f4d6\u8f74\u8f6f\u9650\u4f4d6\u8f74\u8f6f\u9650\u4f4d", None))
        self.datalabel_2.setText(QCoreApplication.translate("Form", u"2025/07/04", None))
        self.hourlabel_2.setText(QCoreApplication.translate("Form", u"11\uff1a57\uff1a30", None))
        self.ColliLabel.setText("")
        self.downcopushButton_2.setText("")
        self.UpcoButton_2.setText("")
        self.collilabellabel_2.setText(QCoreApplication.translate("Form", u"50%", None))
        self.IpButton_2.setText("")
        self.IpButton_3.setText("")
        self.Ipinforlabel_2.setText(QCoreApplication.translate("Form", u"\u5df2\u8fde\u63a5", None))
        self.CVlabel_2.setText(QCoreApplication.translate("Form", u"CV: FR20-3.8.2", None))
        self.Versionlabel_2.setText(QCoreApplication.translate("Form", u"Version: 1.0.0", None))
    # retranslateUi

