from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QGraphicsDropShadowEffect, QComboBox, QSpinBox, QLineEdit
)
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QColor

class CustomDialog(QDialog):
    """自定义美观对话框"""
    
    def __init__(self, parent=None, title="提示", message="", dialog_type="info"):
        super().__init__(parent)
        self.dialog_type = dialog_type
        self.result_value = False
        self.setup_ui(title, message)
        self.setup_animations()
    
    def setup_ui(self, title, message):
        """设置UI界面"""
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setFixedSize(400, 350)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 主框架
        self.main_frame = QFrame()
        self.main_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 5)
        self.main_frame.setGraphicsEffect(shadow)
        
        # 框架布局
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(20, 20, 20, 20)
        frame_layout.setSpacing(15)
        
        # 标题区域
        title_layout = QHBoxLayout()
        
        # 图标
        icon_label = QLabel()
        icon_label.setFixedSize(24, 24)
        icon_label.setStyleSheet(self.get_icon_style())
        title_layout.addWidget(icon_label)
        
        # 标题文本
        title_label = QLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-left: 8px; border: none;")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(24, 24)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                color: #95a5a6;
                font-size: 18px;
                font-weight: bold;
                border-radius: 12px;
            }
            QPushButton:hover {
                background-color: #e74c3c;
                color: white;
            }
        """)
        close_btn.clicked.connect(self.reject)
        title_layout.addWidget(close_btn)
        
        frame_layout.addLayout(title_layout)
        
        # 消息内容
        message_label = QLabel(message)
        message_label.setFont(QFont("Microsoft YaHei", 11))
        message_label.setStyleSheet("color: #34495e; line-height: 1.5;")
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop)
        frame_layout.addWidget(message_label)
        
        # 弹性空间
        frame_layout.addStretch()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        if self.dialog_type == "question":
            # 确认对话框 - 两个按钮
            cancel_btn = QPushButton("取消")
            cancel_btn.setFixedSize(80, 35)
            cancel_btn.setStyleSheet(self.get_button_style("cancel"))
            cancel_btn.clicked.connect(self.reject)
            button_layout.addWidget(cancel_btn)
            
            confirm_btn = QPushButton("确定")
            confirm_btn.setFixedSize(80, 35)
            confirm_btn.setStyleSheet(self.get_button_style("confirm"))
            confirm_btn.clicked.connect(self.accept)
            button_layout.addWidget(confirm_btn)
        else:
            # 信息对话框 - 一个按钮
            ok_btn = QPushButton("确定")
            ok_btn.setFixedSize(80, 35)
            ok_btn.setStyleSheet(self.get_button_style("ok"))
            ok_btn.clicked.connect(self.accept)
            button_layout.addWidget(ok_btn)
        
        frame_layout.addLayout(button_layout)
        main_layout.addWidget(self.main_frame)
    
    def get_icon_style(self):
        """获取图标样式"""
        icon_styles = {
            "info": "background-color: #3498db; border-radius: 12px;",
            "success": "background-color: #27ae60; border-radius: 12px;",
            "warning": "background-color: #f39c12; border-radius: 12px;",
            "error": "background-color: #e74c3c; border-radius: 12px;",
            "question": "background-color: #9b59b6; border-radius: 12px;"
        }
        return icon_styles.get(self.dialog_type, icon_styles["info"])
    def get_button_style(self, button_type):
        """获取按钮样式"""
        base_style = """
            QPushButton {
                border: none;
                border-radius: 6px;
                font-family: "Microsoft YaHei";
                font-size: 11px;
                font-weight: bold;
                color: white;
            }
            QPushButton:hover {
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                border: 1px solid rgba(0, 0, 0, 0.2);
            }
        """
        
        button_colors = {
            "ok": """
                QPushButton {
                    background-color: #3498db;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """,
            "confirm": """
                QPushButton {
                    background-color: #22c55e;
                }
                QPushButton:hover {
                    background-color: #16a34a;
                }
            """,
            "cancel": """
                QPushButton {
                    background-color: #95a5a6;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """
        }
        
        return base_style + button_colors.get(button_type, button_colors["ok"])

    def setup_animations(self):
        """设置动画效果"""
        # 淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(200)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 缩放动画
        self.scale_animation = QPropertyAnimation(self.main_frame, b"geometry")
        self.scale_animation.setDuration(200)
        self.scale_animation.setEasingCurve(QEasingCurve.Type.OutBack)
    
    def showEvent(self, event):
        """显示事件 - 播放动画"""
        super().showEvent(event)
        
        # 设置初始状态
        self.setWindowOpacity(0.0)
        
        # 计算居中位置
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2
            self.move(x, y)
        
        # 设置缩放动画的起始和结束状态
        center_rect = QRect(self.width()//4, self.height()//4, self.width()//2, self.height()//2)
        final_rect = QRect(0, 0, self.width(), self.height())
        
        self.scale_animation.setStartValue(center_rect)
        self.scale_animation.setEndValue(final_rect)
        
        # 播放动画
        self.fade_animation.start()
        self.scale_animation.start()
    
    @staticmethod
    def show_info(parent, title, message):
        """显示信息对话框"""
        dialog = CustomDialog(parent, title, message, "info")
        return dialog.exec()
    
    @staticmethod
    def show_success(parent, title, message):
        """显示成功对话框"""
        dialog = CustomDialog(parent, title, message, "success")
        return dialog.exec()
    
    @staticmethod
    def show_warning(parent, title, message):
        """显示警告对话框"""
        dialog = CustomDialog(parent, title, message, "warning")
        return dialog.exec()
    
    @staticmethod
    def show_error(parent, title, message):
        """显示错误对话框"""
        dialog = CustomDialog(parent, title, message, "error")
        return dialog.exec()
    
    @staticmethod
    def show_question(parent, title, message):
        """显示确认对话框"""
        dialog = CustomDialog(parent, title, message, "question")
        result = dialog.exec()
        return result == QDialog.DialogCode.Accepted

class StatusChangeDialog(CustomDialog):
    """状态变更专用对话框"""
    
    def __init__(self, parent=None, pallet_name="垛盘", current_status="空闲", target_status="就绪"):
        self.pallet_name = pallet_name
        self.current_status = current_status
        self.target_status = target_status
        
        title = f"{pallet_name}状态变更"
        message = f"当前状态：{current_status}\n目标状态：{target_status}\n\n确定要执行此操作吗？"
        
        super().__init__(parent, title, message, "question")
        self.setup_status_ui()
    
    def setup_status_ui(self):
        """设置状态显示UI"""
        # 在消息标签后添加状态指示器
        frame_layout = self.main_frame.layout()
        
        # 状态指示器布局
        status_layout = QHBoxLayout()
        
        # 当前状态
        current_frame = QFrame()
        current_frame.setFixedSize(80, 40)
        current_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {self.get_status_color(self.current_status)};
                border-radius: 8px;
                border: 2px solid #ecf0f1;
            }}
        """)
        current_label = QLabel(self.current_status)
        current_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        current_label.setStyleSheet("color: white; font-weight: bold; font-size: 10px;")
        current_layout = QVBoxLayout(current_frame)
        current_layout.addWidget(current_label)
        current_layout.setContentsMargins(5, 5, 5, 5)
        
        # 箭头
        arrow_label = QLabel("→")
        arrow_label.setStyleSheet("color: #7f8c8d; font-size: 20px; font-weight: bold;")
        arrow_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 目标状态
        target_frame = QFrame()
        target_frame.setFixedSize(80, 40)
        target_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {self.get_status_color(self.target_status)};
                border-radius: 8px;
                border: 2px solid #ecf0f1;
            }}
        """)
        target_label = QLabel(self.target_status)
        target_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        target_label.setStyleSheet("color: white; font-weight: bold; font-size: 10px;")
        target_layout = QVBoxLayout(target_frame)
        target_layout.addWidget(target_label)
        target_layout.setContentsMargins(5, 5, 5, 5)
        
        status_layout.addStretch()
        status_layout.addWidget(current_frame)
        status_layout.addWidget(arrow_label)
        status_layout.addWidget(target_frame)
        status_layout.addStretch()
        
        # 插入到消息标签后面
        frame_layout.insertLayout(2, status_layout)
    
    def get_status_color(self, status):
        """获取状态对应的颜色"""
        colors = {
            '空闲': '#6b7280',
            '就绪': '#22c55e',
            '作业中': '#ef4444',
            '暂停': '#3b82f6',
            '满垛': '#f59e0b'
        }
        return colors.get(status, '#6b7280')

class CustomStartDialog(QDialog):
    """自定义启动对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_pallet = ""
        self.target_layers = 0
        self.target_parts = 0
        self.result_accepted = False
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """设置UI界面"""
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        self.setFixedSize(450, 350)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        
        # 主框架
        self.main_frame = QFrame()
        self.main_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
                border: 2px solid #e1e8ed;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 5)
        self.main_frame.setGraphicsEffect(shadow)
        
        # 框架布局
        frame_layout = QVBoxLayout(self.main_frame)
        frame_layout.setContentsMargins(25, 25, 25, 25)
        frame_layout.setSpacing(20)
        
        # 标题 - 更大字体、更明显颜色、添加背景色
        # 标题 - 修复文字显示问题
        title_label = QLabel("自定义启动设置")
        title_label.setFont(QFont("Microsoft YaHei", 18))
        title_label.setStyleSheet("color: #4a5568;font-size: 18px; font-weight: bold; background-color: transparent; border: none;")
        # title_label.setStyleSheet("""
        #     QLabel {
        #         color: white; 
        #         background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
        #             stop:0 #1e40af, stop:1 #3b82f6);  /* 使用更深的蓝色渐变 */
        #         border-radius: 12px;
        #         padding: 15px 20px;
        #         margin-bottom: 10px;
        #         font-weight: 900;
        #     }
        # """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(title_label)
        
        # 垛盘选择 - 左侧无边框无底色，右侧有边框有底色
        pallet_layout = QHBoxLayout()
        pallet_label = QLabel("选择垛盘:")
        pallet_label.setFont(QFont("Microsoft YaHei", 12))
        pallet_label.setStyleSheet("color: #4a5568; font-weight: bold; background-color: transparent; border: none;")
        pallet_label.setFixedWidth(100)
        
        self.pallet_combo = QComboBox()
        self.pallet_combo.addItems(["左垛盘", "右垛盘"])
        self.pallet_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background-color: #f8f9fa;
                padding: 8px 12px;
                font-size: 12px;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #3182ce;
                background-color: #ffffff;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background: transparent;
            }
            QComboBox::down-arrow {
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #718096;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #e2e8f0;
                background-color: white;
                selection-background-color: #e9ecef;
            }
        """)
        
        pallet_layout.addWidget(pallet_label)
        pallet_layout.addWidget(self.pallet_combo)
        frame_layout.addLayout(pallet_layout)
        
        # 层数输入 - 左侧无边框无底色，右侧有边框有底色
        layers_layout = QHBoxLayout()
        layers_label = QLabel("层数:")
        layers_label.setFont(QFont("Microsoft YaHei", 12))
        layers_label.setStyleSheet("color: #4a5568; font-weight: bold; background-color: transparent; border: none;")
        layers_label.setFixedWidth(100)
        
        self.layers_spinbox = QSpinBox()
        self.layers_spinbox.setRange(1, 50)
        self.layers_spinbox.setValue(1)
        self.layers_spinbox.setStyleSheet("""
            QSpinBox {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background-color: #f8f9fa;
                padding: 8px 12px;
                font-size: 12px;
                min-height: 20px;
            }
            QSpinBox:focus {
                border-color: #3182ce;
                background-color: #ffffff;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border: none;
                background-color: #f7fafc;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #edf2f7;
            }
        """)
        
        layers_layout.addWidget(layers_label)
        layers_layout.addWidget(self.layers_spinbox)
        frame_layout.addLayout(layers_layout)
        
        # 工件号输入 - 左侧无边框无底色，右侧有边框有底色
        parts_layout = QHBoxLayout()
        parts_label = QLabel("工作号:")
        parts_label.setFont(QFont("Microsoft YaHei", 12))
        parts_label.setStyleSheet("color: #4a5568; font-weight: bold; background-color: transparent; border: none;")
        parts_label.setFixedWidth(100)
        
        self.parts_spinbox = QSpinBox()
        self.parts_spinbox.setRange(1, 1000)
        self.parts_spinbox.setValue(1)
        self.parts_spinbox.setStyleSheet("""
            QSpinBox {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background-color: #f8f9fa;
                padding: 8px 12px;
                font-size: 12px;
                min-height: 20px;
            }
            QSpinBox:focus {
                border-color: #3182ce;
                background-color: #ffffff;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border: none;
                background-color: #f7fafc;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #edf2f7;
            }
        """)
        
        parts_layout.addWidget(parts_label)
        parts_layout.addWidget(self.parts_spinbox)
        frame_layout.addLayout(parts_layout)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        # 取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.setStyleSheet(self.get_button_style("cancel"))
        cancel_button.clicked.connect(self.reject)
        
        # 确认按钮
        confirm_button = QPushButton("确认启动")
        confirm_button.setStyleSheet(self.get_button_style("confirm"))
        confirm_button.clicked.connect(self.accept_settings)
        
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(confirm_button)
        frame_layout.addLayout(button_layout)
        
        main_layout.addWidget(self.main_frame)
    
    def get_button_style(self, button_type):
        """获取按钮样式"""
        if button_type == "cancel":
            return """
                QPushButton {
                    background-color: #e2e8f0;
                    color: #4a5568;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #cbd5e0;
                }
                QPushButton:pressed {
                    background-color: #a0aec0;
                }
            """
        else:  # confirm
            return """
                QPushButton {
                    background-color: #22c55e;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #16a34a;
                }
                QPushButton:pressed {
                    background-color: #15803d;
                }
            """
    
    def setup_animations(self):
        """设置动画效果"""
        self.animation = QPropertyAnimation(self.main_frame, b"geometry")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def showEvent(self, event):
        """显示事件 - 添加动画效果"""
        super().showEvent(event)
        
        # 设置初始位置（稍微向上）
        start_rect = QRect(self.main_frame.x(), self.main_frame.y() - 20, 
                          self.main_frame.width(), self.main_frame.height())
        end_rect = QRect(self.main_frame.x(), self.main_frame.y(), 
                        self.main_frame.width(), self.main_frame.height())
        
        self.animation.setStartValue(start_rect)
        self.animation.setEndValue(end_rect)
        self.animation.start()
    
    def accept_settings(self):
        """接受设置"""
        # 获取用户输入的值
        self.selected_pallet = "left_pallet" if self.pallet_combo.currentText() == "左垛盘" else "right_pallet"
        self.target_layers = self.layers_spinbox.value()
        self.target_parts = self.parts_spinbox.value()
        self.result_accepted = True
        self.accept()
    
    def get_settings(self):
        """获取设置值"""
        return {
            'pallet_id': self.selected_pallet,
            'target_layers': self.target_layers,
            'target_parts': self.target_parts
        }

class ResumeWorkDialog(CustomDialog):
    """恢复工作确认对话框"""
    
    def __init__(self, parent=None, pallet_name="垛盘", paused_layer=5, paused_work_number=3):
        self.pallet_name = pallet_name
        self.paused_layer = paused_layer
        self.paused_work_number = paused_work_number
        
        title = "是否从当前位置开始作业？"
        message = f"检测到{pallet_name}有未完成的作业\n\n是否从以下位置继续？"
        
        super().__init__(parent, title, message, "question")
        self.setup_resume_ui()
    
    def setup_resume_ui(self):
        """设置恢复工作UI"""
        # 在消息标签后添加层数和工作号显示
        frame_layout = self.main_frame.layout()
        
        # 数据显示布局
        data_layout = QHBoxLayout()
        data_layout.setSpacing(30)
        
        # 层数显示
        layers_frame = QFrame()
        layers_frame.setFixedSize(120, 60)
        layers_frame.setStyleSheet("""
            QFrame {
                background-color: #e2e8f0;
                border-radius: 12px;
                border: 2px solid #cbd5e0;
            }
        """)
        
        layers_layout = QVBoxLayout(layers_frame)
        layers_layout.setContentsMargins(10, 8, 10, 8)
        layers_layout.setSpacing(2)
        
        layers_title = QLabel("层数")
        layers_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layers_title.setStyleSheet("color: #4a5568; font-size: 12px; font-weight: bold; background: transparent; border: none;")
        
        layers_value = QLabel(str(self.paused_layer))
        layers_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layers_value.setStyleSheet("color: #2d3748; font-size: 18px; font-weight: bold; background: transparent; border: none;")
        
        layers_layout.addWidget(layers_title)
        layers_layout.addWidget(layers_value)
        
        # 工作号显示
        work_frame = QFrame()
        work_frame.setFixedSize(120, 60)
        work_frame.setStyleSheet("""
            QFrame {
                background-color: #e2e8f0;
                border-radius: 12px;
                border: 2px solid #cbd5e0;
            }
        """)
        
        work_layout = QVBoxLayout(work_frame)
        work_layout.setContentsMargins(10, 8, 10, 8)
        work_layout.setSpacing(2)
        
        work_title = QLabel("工作号")
        work_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        work_title.setStyleSheet("color: #4a5568; font-size: 12px; font-weight: bold; background: transparent; border: none;")
        
        work_value = QLabel(str(self.paused_work_number))
        work_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        work_value.setStyleSheet("color: #2d3748; font-size: 18px; font-weight: bold; background: transparent; border: none;")
        
        work_layout.addWidget(work_title)
        work_layout.addWidget(work_value)
        
        data_layout.addStretch()
        data_layout.addWidget(layers_frame)
        data_layout.addWidget(work_frame)
        data_layout.addStretch()
        
        # 插入到消息标签后面
        frame_layout.insertLayout(2, data_layout)
    
    @staticmethod
    def show_resume_work(parent, pallet_name, paused_layer, paused_work_number):
        """显示恢复工作确认对话框"""
        dialog = ResumeWorkDialog(parent, pallet_name, paused_layer, paused_work_number)
        result = dialog.exec()
        return result == QDialog.DialogCode.Accepted