# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'teachingPage_manual.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON>rush, QColor, QC<PERSON>alGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QGridLayout, QGroupBox, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QSizePolicy,
    QSlider, QSpacerItem, QVBoxLayout, QWidget)

from ..resources.manual import resources_manual_rc

class Ui_Form_2(object):
    def setupUi(self, Form_2):
        if not Form_2.objectName():
            Form_2.setObjectName(u"Form_2")
        Form_2.resize(839, 430)
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(Form_2.sizePolicy().hasHeightForWidth())
        Form_2.setSizePolicy(sizePolicy)
        Form_2.setMinimumSize(QSize(600, 300))
        self.horizontalLayout = QHBoxLayout(Form_2)
        self.horizontalLayout.setSpacing(4)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(6, 6, 6, 6)
        self.groupBox_5 = QGroupBox(Form_2)
        self.groupBox_5.setObjectName(u"groupBox_5")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy1.setHorizontalStretch(1)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.groupBox_5.sizePolicy().hasHeightForWidth())
        self.groupBox_5.setSizePolicy(sizePolicy1)
        self.groupBox_5.setMinimumSize(QSize(160, 0))
        self.groupBox_5.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"opacity: 0.91;")
        self.verticalLayout_3 = QVBoxLayout(self.groupBox_5)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalSpacer_2 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_3.addItem(self.verticalSpacer_2)

        self.label = QLabel(self.groupBox_5)
        self.label.setObjectName(u"label")
        self.label.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 600;\n"
"font-size: 14px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.verticalLayout_3.addWidget(self.label)

        self.groupBox_8 = QGroupBox(self.groupBox_5)
        self.groupBox_8.setObjectName(u"groupBox_8")
        sizePolicy.setHeightForWidth(self.groupBox_8.sizePolicy().hasHeightForWidth())
        self.groupBox_8.setSizePolicy(sizePolicy)
        self.groupBox_8.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;\n"
"opacity: 0.91;")
        self.verticalLayout_4 = QVBoxLayout(self.groupBox_8)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.horizontalLayout_11 = QHBoxLayout()
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.label_41 = QLabel(self.groupBox_8)
        self.label_41.setObjectName(u"label_41")
        self.label_41.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_11.addWidget(self.label_41)

        self.lineEdit_35 = QLineEdit(self.groupBox_8)
        self.lineEdit_35.setObjectName(u"lineEdit_35")
        self.lineEdit_35.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.horizontalLayout_11.addWidget(self.lineEdit_35)

        self.label_42 = QLabel(self.groupBox_8)
        self.label_42.setObjectName(u"label_42")
        self.label_42.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_11.addWidget(self.label_42)


        self.verticalLayout_4.addLayout(self.horizontalLayout_11)

        self.gridLayout = QGridLayout()
        self.gridLayout.setObjectName(u"gridLayout")
        self.J1label = QLabel(self.groupBox_8)
        self.J1label.setObjectName(u"J1label")

        self.gridLayout.addWidget(self.J1label, 0, 0, 1, 1)

        self.J1lineEdit = QLineEdit(self.groupBox_8)
        self.J1lineEdit.setObjectName(u"J1lineEdit")
        self.J1lineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.gridLayout.addWidget(self.J1lineEdit, 0, 1, 1, 1)

        self.J1DButton = QPushButton(self.groupBox_8)
        self.J1DButton.setObjectName(u"J1DButton")
        self.J1DButton.setStyleSheet(u"background-image: url(:/j1/\u51cf\u53f7 J1.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J1DButton, 1, 0, 1, 1)

        self.J1Slider = QSlider(self.groupBox_8)
        self.J1Slider.setObjectName(u"J1Slider")
        self.J1Slider.setOrientation(Qt.Orientation.Horizontal)

        self.gridLayout.addWidget(self.J1Slider, 1, 1, 1, 1)

        self.J2label = QLabel(self.groupBox_8)
        self.J2label.setObjectName(u"J2label")

        self.gridLayout.addWidget(self.J2label, 2, 0, 1, 1)

        self.J2lineEdit = QLineEdit(self.groupBox_8)
        self.J2lineEdit.setObjectName(u"J2lineEdit")
        self.J2lineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.gridLayout.addWidget(self.J2lineEdit, 2, 1, 1, 1)

        self.J2DButton = QPushButton(self.groupBox_8)
        self.J2DButton.setObjectName(u"J2DButton")
        self.J2DButton.setStyleSheet(u"background-image: url(:/J2/\u51cf\u53f7 J2.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J2DButton, 3, 0, 1, 1)

        self.J2Slider = QSlider(self.groupBox_8)
        self.J2Slider.setObjectName(u"J2Slider")
        self.J2Slider.setOrientation(Qt.Orientation.Horizontal)

        self.gridLayout.addWidget(self.J2Slider, 3, 1, 1, 1)

        self.J2UButton = QPushButton(self.groupBox_8)
        self.J2UButton.setObjectName(u"J2UButton")
        self.J2UButton.setStyleSheet(u"background-image: url(:/J2/\u52a0\u53f7 J2.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J2UButton, 3, 2, 1, 1)

        self.J3label = QLabel(self.groupBox_8)
        self.J3label.setObjectName(u"J3label")

        self.gridLayout.addWidget(self.J3label, 4, 0, 1, 1)

        self.J3lineEdit = QLineEdit(self.groupBox_8)
        self.J3lineEdit.setObjectName(u"J3lineEdit")
        self.J3lineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.gridLayout.addWidget(self.J3lineEdit, 4, 1, 1, 1)

        self.J3DButton = QPushButton(self.groupBox_8)
        self.J3DButton.setObjectName(u"J3DButton")
        self.J3DButton.setStyleSheet(u"background-image: url(:/J3/\u51cf\u53f7 J3.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J3DButton, 5, 0, 1, 1)

        self.J3Slider = QSlider(self.groupBox_8)
        self.J3Slider.setObjectName(u"J3Slider")
        self.J3Slider.setOrientation(Qt.Orientation.Horizontal)

        self.gridLayout.addWidget(self.J3Slider, 5, 1, 1, 1)

        self.J3UButton = QPushButton(self.groupBox_8)
        self.J3UButton.setObjectName(u"J3UButton")
        self.J3UButton.setStyleSheet(u"background-image: url(:/J3/\u52a0\u53f7 J3.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J3UButton, 5, 2, 1, 1)

        self.J4label = QLabel(self.groupBox_8)
        self.J4label.setObjectName(u"J4label")

        self.gridLayout.addWidget(self.J4label, 6, 0, 1, 1)

        self.J4lineEdit = QLineEdit(self.groupBox_8)
        self.J4lineEdit.setObjectName(u"J4lineEdit")
        self.J4lineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.gridLayout.addWidget(self.J4lineEdit, 6, 1, 1, 1)

        self.J4DButton = QPushButton(self.groupBox_8)
        self.J4DButton.setObjectName(u"J4DButton")
        self.J4DButton.setStyleSheet(u"background-image: url(:/J4/\u51cf\u53f7 J4.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J4DButton, 7, 0, 1, 1)

        self.J4Slider = QSlider(self.groupBox_8)
        self.J4Slider.setObjectName(u"J4Slider")
        self.J4Slider.setOrientation(Qt.Orientation.Horizontal)

        self.gridLayout.addWidget(self.J4Slider, 7, 1, 1, 1)

        self.J4UButton = QPushButton(self.groupBox_8)
        self.J4UButton.setObjectName(u"J4UButton")
        self.J4UButton.setStyleSheet(u"background-image: url(:/J4/\u52a0\u53f7 J4.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J4UButton, 7, 2, 1, 1)

        self.J5label = QLabel(self.groupBox_8)
        self.J5label.setObjectName(u"J5label")

        self.gridLayout.addWidget(self.J5label, 8, 0, 1, 1)

        self.J5lineEdit = QLineEdit(self.groupBox_8)
        self.J5lineEdit.setObjectName(u"J5lineEdit")
        self.J5lineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.gridLayout.addWidget(self.J5lineEdit, 8, 1, 1, 1)

        self.J5DButton = QPushButton(self.groupBox_8)
        self.J5DButton.setObjectName(u"J5DButton")
        self.J5DButton.setStyleSheet(u"background-image: url(:/J5/\u51cf\u53f7 J5.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J5DButton, 9, 0, 1, 1)

        self.J5Slider = QSlider(self.groupBox_8)
        self.J5Slider.setObjectName(u"J5Slider")
        self.J5Slider.setOrientation(Qt.Orientation.Horizontal)

        self.gridLayout.addWidget(self.J5Slider, 9, 1, 1, 1)

        self.J5UButton = QPushButton(self.groupBox_8)
        self.J5UButton.setObjectName(u"J5UButton")
        self.J5UButton.setStyleSheet(u"background-image: url(:/J5/\u52a0\u53f7 J5.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J5UButton, 9, 2, 1, 1)

        self.J6label = QLabel(self.groupBox_8)
        self.J6label.setObjectName(u"J6label")

        self.gridLayout.addWidget(self.J6label, 10, 0, 1, 1)

        self.J6lineEdit = QLineEdit(self.groupBox_8)
        self.J6lineEdit.setObjectName(u"J6lineEdit")
        self.J6lineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.gridLayout.addWidget(self.J6lineEdit, 10, 1, 1, 1)

        self.J6DButton = QPushButton(self.groupBox_8)
        self.J6DButton.setObjectName(u"J6DButton")
        self.J6DButton.setStyleSheet(u"background-image: url(:/J6/\u51cf\u53f7 J6.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J6DButton, 11, 0, 1, 1)

        self.J6Slider = QSlider(self.groupBox_8)
        self.J6Slider.setObjectName(u"J6Slider")
        self.J6Slider.setOrientation(Qt.Orientation.Horizontal)

        self.gridLayout.addWidget(self.J6Slider, 11, 1, 1, 1)

        self.J6UButton = QPushButton(self.groupBox_8)
        self.J6UButton.setObjectName(u"J6UButton")
        self.J6UButton.setStyleSheet(u"background-image: url(:/J6/\u52a0\u53f7 J6.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J6UButton, 11, 2, 1, 1)

        self.J1UButton = QPushButton(self.groupBox_8)
        self.J1UButton.setObjectName(u"J1UButton")
        self.J1UButton.setStyleSheet(u"background-image: url(:/j1/\u52a0\u53f7 J1.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout.addWidget(self.J1UButton, 1, 2, 1, 1)


        self.verticalLayout_4.addLayout(self.gridLayout)


        self.verticalLayout_3.addWidget(self.groupBox_8)

        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_3.addItem(self.verticalSpacer)


        self.horizontalLayout.addWidget(self.groupBox_5)

        self.groupBox_6 = QGroupBox(Form_2)
        self.groupBox_6.setObjectName(u"groupBox_6")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy2.setHorizontalStretch(2)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.groupBox_6.sizePolicy().hasHeightForWidth())
        self.groupBox_6.setSizePolicy(sizePolicy2)
        self.groupBox_6.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;")
        self.verticalLayout = QVBoxLayout(self.groupBox_6)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalSpacer_4 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout.addItem(self.verticalSpacer_4)

        self.label_17 = QLabel(self.groupBox_6)
        self.label_17.setObjectName(u"label_17")
        self.label_17.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 600;\n"
"font-size: 14px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.verticalLayout.addWidget(self.label_17)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.label_18 = QLabel(self.groupBox_6)
        self.label_18.setObjectName(u"label_18")
        self.label_18.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_3.addWidget(self.label_18)

        self.lineEdit_17 = QLineEdit(self.groupBox_6)
        self.lineEdit_17.setObjectName(u"lineEdit_17")
        self.lineEdit_17.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.horizontalLayout_3.addWidget(self.lineEdit_17)


        self.verticalLayout.addLayout(self.horizontalLayout_3)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer)

        self.ZDButton = QPushButton(self.groupBox_6)
        self.ZDButton.setObjectName(u"ZDButton")
        self.ZDButton.setMinimumSize(QSize(59, 44))
        self.ZDButton.setStyleSheet(u"background-image: url(:/z/Z-.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.horizontalLayout_4.addWidget(self.ZDButton)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer_2)

        self.ZUButton = QPushButton(self.groupBox_6)
        self.ZUButton.setObjectName(u"ZUButton")
        self.ZUButton.setMinimumSize(QSize(59, 44))
        self.ZUButton.setStyleSheet(u"background-image: url(:/z/Z+.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.horizontalLayout_4.addWidget(self.ZUButton)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer_3)


        self.verticalLayout.addLayout(self.horizontalLayout_4)

        self.gridLayout_2 = QGridLayout()
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.XDButton = QPushButton(self.groupBox_6)
        self.XDButton.setObjectName(u"XDButton")
        self.XDButton.setMinimumSize(QSize(83, 50))
        self.XDButton.setStyleSheet(u"background-image: url(:/x/X-.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout_2.addWidget(self.XDButton, 0, 1, 1, 1)

        self.YDButton = QPushButton(self.groupBox_6)
        self.YDButton.setObjectName(u"YDButton")
        self.YDButton.setMinimumSize(QSize(68, 47))
        self.YDButton.setStyleSheet(u"background-image: url(:/y/Y-.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout_2.addWidget(self.YDButton, 1, 0, 1, 1)

        self.YUButton = QPushButton(self.groupBox_6)
        self.YUButton.setObjectName(u"YUButton")
        self.YUButton.setMinimumSize(QSize(68, 47))
        self.YUButton.setStyleSheet(u"background-image: url(:/y/Y+.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout_2.addWidget(self.YUButton, 1, 2, 1, 1)

        self.XUButton = QPushButton(self.groupBox_6)
        self.XUButton.setObjectName(u"XUButton")
        self.XUButton.setMinimumSize(QSize(83, 50))
        self.XUButton.setStyleSheet(u"background-image: url(:/x/X-.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout_2.addWidget(self.XUButton, 2, 1, 1, 1)


        self.verticalLayout.addLayout(self.gridLayout_2)

        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.Xlabel = QLabel(self.groupBox_6)
        self.Xlabel.setObjectName(u"Xlabel")
        self.Xlabel.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_5.addWidget(self.Xlabel)

        self.XlineEdit = QLineEdit(self.groupBox_6)
        self.XlineEdit.setObjectName(u"XlineEdit")
        self.XlineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.horizontalLayout_5.addWidget(self.XlineEdit)

        self.Ylabel = QLabel(self.groupBox_6)
        self.Ylabel.setObjectName(u"Ylabel")
        self.Ylabel.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_5.addWidget(self.Ylabel)

        self.YlineEdit = QLineEdit(self.groupBox_6)
        self.YlineEdit.setObjectName(u"YlineEdit")
        self.YlineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.horizontalLayout_5.addWidget(self.YlineEdit)

        self.Zlabel = QLabel(self.groupBox_6)
        self.Zlabel.setObjectName(u"Zlabel")
        self.Zlabel.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_5.addWidget(self.Zlabel)

        self.ZlineEdit = QLineEdit(self.groupBox_6)
        self.ZlineEdit.setObjectName(u"ZlineEdit")
        self.ZlineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.horizontalLayout_5.addWidget(self.ZlineEdit)


        self.verticalLayout.addLayout(self.horizontalLayout_5)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer_4)

        self.RZUButton = QPushButton(self.groupBox_6)
        self.RZUButton.setObjectName(u"RZUButton")
        self.RZUButton.setMinimumSize(QSize(59, 44))
        self.RZUButton.setStyleSheet(u"background-image: url(:/rz/RZ+.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.horizontalLayout_6.addWidget(self.RZUButton)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer_5)

        self.RZDButton = QPushButton(self.groupBox_6)
        self.RZDButton.setObjectName(u"RZDButton")
        self.RZDButton.setMinimumSize(QSize(59, 44))
        self.RZDButton.setStyleSheet(u"background-image: url(:/rz/RZ-.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.horizontalLayout_6.addWidget(self.RZDButton)

        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer_6)


        self.verticalLayout.addLayout(self.horizontalLayout_6)

        self.gridLayout_3 = QGridLayout()
        self.gridLayout_3.setObjectName(u"gridLayout_3")
        self.RYDButton = QPushButton(self.groupBox_6)
        self.RYDButton.setObjectName(u"RYDButton")
        self.RYDButton.setMinimumSize(QSize(83, 50))
        self.RYDButton.setStyleSheet(u"background-image: url(:/ry/RY-.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout_3.addWidget(self.RYDButton, 0, 1, 1, 1)

        self.RXUButton = QPushButton(self.groupBox_6)
        self.RXUButton.setObjectName(u"RXUButton")
        self.RXUButton.setMinimumSize(QSize(68, 47))
        self.RXUButton.setStyleSheet(u"background-image: url(:/rx/RX+.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout_3.addWidget(self.RXUButton, 1, 0, 1, 1)

        self.RYUButton = QPushButton(self.groupBox_6)
        self.RYUButton.setObjectName(u"RYUButton")
        self.RYUButton.setMinimumSize(QSize(83, 50))
        self.RYUButton.setStyleSheet(u"background-image: url(:/ry/RY+.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout_3.addWidget(self.RYUButton, 1, 1, 1, 1)

        self.RXDButton = QPushButton(self.groupBox_6)
        self.RXDButton.setObjectName(u"RXDButton")
        self.RXDButton.setMinimumSize(QSize(68, 47))
        self.RXDButton.setStyleSheet(u"background-image: url(:/rx/RX-.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.gridLayout_3.addWidget(self.RXDButton, 1, 2, 1, 1)


        self.verticalLayout.addLayout(self.gridLayout_3)

        self.horizontalLayout_7 = QHBoxLayout()
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.RXlabel = QLabel(self.groupBox_6)
        self.RXlabel.setObjectName(u"RXlabel")
        self.RXlabel.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_7.addWidget(self.RXlabel)

        self.RXlineEdit = QLineEdit(self.groupBox_6)
        self.RXlineEdit.setObjectName(u"RXlineEdit")
        self.RXlineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.horizontalLayout_7.addWidget(self.RXlineEdit)

        self.RYlabel = QLabel(self.groupBox_6)
        self.RYlabel.setObjectName(u"RYlabel")
        self.RYlabel.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_7.addWidget(self.RYlabel)

        self.RYlineEdit = QLineEdit(self.groupBox_6)
        self.RYlineEdit.setObjectName(u"RYlineEdit")
        self.RYlineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.horizontalLayout_7.addWidget(self.RYlineEdit)

        self.RZlabel = QLabel(self.groupBox_6)
        self.RZlabel.setObjectName(u"RZlabel")
        self.RZlabel.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_7.addWidget(self.RZlabel)

        self.RZlineEdit = QLineEdit(self.groupBox_6)
        self.RZlineEdit.setObjectName(u"RZlineEdit")
        self.RZlineEdit.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.horizontalLayout_7.addWidget(self.RZlineEdit)


        self.verticalLayout.addLayout(self.horizontalLayout_7)

        self.verticalSpacer_3 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout.addItem(self.verticalSpacer_3)


        self.horizontalLayout.addWidget(self.groupBox_6)

        self.groupBox_7 = QGroupBox(Form_2)
        self.groupBox_7.setObjectName(u"groupBox_7")
        sizePolicy1.setHeightForWidth(self.groupBox_7.sizePolicy().hasHeightForWidth())
        self.groupBox_7.setSizePolicy(sizePolicy1)
        self.verticalLayout_2 = QVBoxLayout(self.groupBox_7)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.groupBox_3 = QGroupBox(self.groupBox_7)
        self.groupBox_3.setObjectName(u"groupBox_3")
        sizePolicy3 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(3)
        sizePolicy3.setHeightForWidth(self.groupBox_3.sizePolicy().hasHeightForWidth())
        self.groupBox_3.setSizePolicy(sizePolicy3)
        self.groupBox_3.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;")
        self.verticalLayout_5 = QVBoxLayout(self.groupBox_3)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.verticalSpacer_6 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_5.addItem(self.verticalSpacer_6)

        self.label_38 = QLabel(self.groupBox_3)
        self.label_38.setObjectName(u"label_38")
        self.label_38.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 600;\n"
"font-size: 14px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.verticalLayout_5.addWidget(self.label_38)

        self.horizontalLayout_8 = QHBoxLayout()
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.label_39 = QLabel(self.groupBox_3)
        self.label_39.setObjectName(u"label_39")
        self.label_39.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 400;\n"
"font-size: 12px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.horizontalLayout_8.addWidget(self.label_39)

        self.lineEdit_34 = QLineEdit(self.groupBox_3)
        self.lineEdit_34.setObjectName(u"lineEdit_34")
        self.lineEdit_34.setStyleSheet(u"background: #E2EAF5;\n"
"border-radius: 10px 10px 10px 10px;")

        self.horizontalLayout_8.addWidget(self.lineEdit_34)


        self.verticalLayout_5.addLayout(self.horizontalLayout_8)

        self.horizontalLayout_9 = QHBoxLayout()
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_7)

        self.groupBox = QGroupBox(self.groupBox_3)
        self.groupBox.setObjectName(u"groupBox")
        self.groupBox.setMinimumSize(QSize(100, 170))
        self.groupBox.setStyleSheet(u"background: #E2EAF5;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;")
        self.verticalLayout_6 = QVBoxLayout(self.groupBox)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.UpButton = QPushButton(self.groupBox)
        self.UpButton.setObjectName(u"UpButton")
        sizePolicy4 = QSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        sizePolicy4.setHorizontalStretch(0)
        sizePolicy4.setVerticalStretch(0)
        sizePolicy4.setHeightForWidth(self.UpButton.sizePolicy().hasHeightForWidth())
        self.UpButton.setSizePolicy(sizePolicy4)
        self.UpButton.setMinimumSize(QSize(66, 67))
        self.UpButton.setStyleSheet(u"background-image: url(:/ud/UP.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.verticalLayout_6.addWidget(self.UpButton)

        self.DownButton = QPushButton(self.groupBox)
        self.DownButton.setObjectName(u"DownButton")
        sizePolicy4.setHeightForWidth(self.DownButton.sizePolicy().hasHeightForWidth())
        self.DownButton.setSizePolicy(sizePolicy4)
        self.DownButton.setMinimumSize(QSize(66, 67))
        self.DownButton.setStyleSheet(u"background-image: url(:/ud/DOWN.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.verticalLayout_6.addWidget(self.DownButton)


        self.horizontalLayout_9.addWidget(self.groupBox)

        self.horizontalSpacer_8 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_8)


        self.verticalLayout_5.addLayout(self.horizontalLayout_9)


        self.verticalLayout_2.addWidget(self.groupBox_3)

        self.groupBox_4 = QGroupBox(self.groupBox_7)
        self.groupBox_4.setObjectName(u"groupBox_4")
        sizePolicy5 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy5.setHorizontalStretch(0)
        sizePolicy5.setVerticalStretch(1)
        sizePolicy5.setHeightForWidth(self.groupBox_4.sizePolicy().hasHeightForWidth())
        self.groupBox_4.setSizePolicy(sizePolicy5)
        self.groupBox_4.setStyleSheet(u"background: #FFFFFF;\n"
"box-shadow: 4px 0px 0px 0px #E2EAF5;\n"
"border-radius: 20px 20px 20px 20px;")
        self.verticalLayout_7 = QVBoxLayout(self.groupBox_4)
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.label_40 = QLabel(self.groupBox_4)
        self.label_40.setObjectName(u"label_40")
        self.label_40.setStyleSheet(u"font-family: MiSans, MiSans;\n"
"font-weight: 600;\n"
"font-size: 14px;\n"
"color: #000000;\n"
"line-height: 14px;\n"
"text-align: left;\n"
"font-style: normal;\n"
"text-transform: none;")

        self.verticalLayout_7.addWidget(self.label_40)

        self.horizontalLayout_10 = QHBoxLayout()
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.fetchingButton = QPushButton(self.groupBox_4)
        self.fetchingButton.setObjectName(u"fetchingButton")
        sizePolicy6 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        sizePolicy6.setHorizontalStretch(0)
        sizePolicy6.setVerticalStretch(0)
        sizePolicy6.setHeightForWidth(self.fetchingButton.sizePolicy().hasHeightForWidth())
        self.fetchingButton.setSizePolicy(sizePolicy6)
        self.fetchingButton.setMinimumSize(QSize(89, 34))
        self.fetchingButton.setStyleSheet(u"background-image: url(:/fr/\u6293\u53d6.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.horizontalLayout_10.addWidget(self.fetchingButton)

        self.ReleaseButton = QPushButton(self.groupBox_4)
        self.ReleaseButton.setObjectName(u"ReleaseButton")
        sizePolicy6.setHeightForWidth(self.ReleaseButton.sizePolicy().hasHeightForWidth())
        self.ReleaseButton.setSizePolicy(sizePolicy6)
        self.ReleaseButton.setMinimumSize(QSize(89, 34))
        self.ReleaseButton.setStyleSheet(u"background-image: url(:/fr/\u91ca\u653e.png);\n"
"background-repeat: no-repeat; \n"
"background-position: center;\n"
"background-size: contain;\n"
"border: none;")

        self.horizontalLayout_10.addWidget(self.ReleaseButton)


        self.verticalLayout_7.addLayout(self.horizontalLayout_10)


        self.verticalLayout_2.addWidget(self.groupBox_4)

        self.verticalSpacer_5 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_2.addItem(self.verticalSpacer_5)

        self.verticalLayout_2.setStretch(0, 3)
        self.verticalLayout_2.setStretch(1, 1)

        self.horizontalLayout.addWidget(self.groupBox_7)


        self.retranslateUi(Form_2)

        QMetaObject.connectSlotsByName(Form_2)
    # setupUi

    def retranslateUi(self, Form_2):
        self.groupBox_5.setTitle("")
        self.label.setText(QCoreApplication.translate("Form_2", u"6\u8f74\u70b9\u52a8", None))
        self.groupBox_8.setTitle("")
        self.label_41.setText(QCoreApplication.translate("Form_2", u"\u5355\u6b21\u957f\u6309\u9608\u503c", None))
        self.lineEdit_35.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.label_42.setText(QCoreApplication.translate("Form_2", u"(\u00b0)", None))
        self.J1label.setText(QCoreApplication.translate("Form_2", u"J1", None))
        self.J1lineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.J1DButton.setText("")
        self.J2label.setText(QCoreApplication.translate("Form_2", u"J2", None))
        self.J2lineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.J2DButton.setText("")
        self.J2UButton.setText("")
        self.J3label.setText(QCoreApplication.translate("Form_2", u"J3", None))
        self.J3lineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.J3DButton.setText("")
        self.J3UButton.setText("")
        self.J4label.setText(QCoreApplication.translate("Form_2", u"J4", None))
        self.J4lineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.J4DButton.setText("")
        self.J4UButton.setText("")
        self.J5label.setText(QCoreApplication.translate("Form_2", u"J5", None))
        self.J5lineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.J5DButton.setText("")
        self.J5UButton.setText("")
        self.J6label.setText(QCoreApplication.translate("Form_2", u"J6", None))
        self.J6lineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.J6DButton.setText("")
        self.J6UButton.setText("")
        self.J1UButton.setText("")
        self.groupBox_6.setTitle("")
        self.label_17.setText(QCoreApplication.translate("Form_2", u"\u7b1b\u5361\u5c14\u7a7a\u95f4\u79fb\u52a8", None))
        self.label_18.setText(QCoreApplication.translate("Form_2", u"\u5355\u6b21\u957f\u6309\u9608\u503c", None))
        self.lineEdit_17.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.ZDButton.setText("")
        self.ZUButton.setText("")
        self.XDButton.setText("")
        self.YDButton.setText("")
        self.YUButton.setText("")
        self.XUButton.setText("")
        self.Xlabel.setText(QCoreApplication.translate("Form_2", u"X", None))
        self.XlineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.Ylabel.setText(QCoreApplication.translate("Form_2", u"Y", None))
        self.YlineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.Zlabel.setText(QCoreApplication.translate("Form_2", u"Z", None))
        self.ZlineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.RZUButton.setText("")
        self.RZDButton.setText("")
        self.RYDButton.setText("")
        self.RXUButton.setText("")
        self.RYUButton.setText("")
        self.RXDButton.setText("")
        self.RXlabel.setText(QCoreApplication.translate("Form_2", u"RX", None))
        self.RXlineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.RYlabel.setText(QCoreApplication.translate("Form_2", u"RY", None))
        self.RYlineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.RZlabel.setText(QCoreApplication.translate("Form_2", u"RZ", None))
        self.RZlineEdit.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.groupBox_7.setTitle("")
        self.groupBox_3.setTitle("")
        self.label_38.setText(QCoreApplication.translate("Form_2", u"\u5347\u964d\u53f0", None))
        self.label_39.setText(QCoreApplication.translate("Form_2", u"\u5355\u6b21\u957f\u6309\u9608\u503c", None))
        self.lineEdit_34.setText(QCoreApplication.translate("Form_2", u"A", None))
        self.groupBox.setTitle("")
        self.UpButton.setText("")
        self.DownButton.setText("")
        self.groupBox_4.setTitle("")
        self.label_40.setText(QCoreApplication.translate("Form_2", u"\u5de5\u5177\u63a7\u5236", None))
        self.fetchingButton.setText("")
        self.ReleaseButton.setText("")
        pass
    # retranslateUi

