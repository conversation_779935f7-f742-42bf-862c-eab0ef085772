<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form_2</class>
 <widget class="QWidget" name="Form_2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>839</width>
    <height>430</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>600</width>
    <height>300</height>
   </size>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>2</number>
   </property>
   <property name="leftMargin">
    <number>3</number>
   </property>
   <property name="topMargin">
    <number>3</number>
   </property>
   <property name="rightMargin">
    <number>3</number>
   </property>
   <property name="bottomMargin">
    <number>3</number>
   </property>
   <item>
    <widget class="QGroupBox" name="groupBox_5">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>1</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>140</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
opacity: 0.91;</string>
     </property>
     <property name="title">
      <string/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <spacer name="verticalSpacer_2">
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>15</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label">
        <property name="styleSheet">
         <string notr="true">font-family: MiSans, MiSans;
font-weight: 600;
font-size: 14px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
        </property>
        <property name="text">
         <string>6轴点动</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_8">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;
opacity: 0.91;</string>
        </property>
        <property name="title">
         <string/>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_11">
           <item>
            <widget class="QLabel" name="label_41">
             <property name="styleSheet">
              <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
             </property>
             <property name="text">
              <string>单次长按阈值</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit_35">
             <property name="styleSheet">
              <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
             </property>
             <property name="text">
              <string>A</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_42">
             <property name="styleSheet">
              <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
             </property>
             <property name="text">
              <string>(°)</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout">
           <item row="0" column="0">
            <widget class="QLabel" name="J1label">
             <property name="text">
              <string>J1</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="J1lineEdit">
             <property name="styleSheet">
              <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
             </property>
             <property name="text">
              <string>A</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QPushButton" name="J1DButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/j1/减号 J1.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QSlider" name="J1Slider">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="J2label">
             <property name="text">
              <string>J2</string>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="J2lineEdit">
             <property name="styleSheet">
              <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
             </property>
             <property name="text">
              <string>A</string>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QPushButton" name="J2DButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J2/减号 J2.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QSlider" name="J2Slider">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="3" column="2">
            <widget class="QPushButton" name="J2UButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J2/加号 J2.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="J3label">
             <property name="text">
              <string>J3</string>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <widget class="QLineEdit" name="J3lineEdit">
             <property name="styleSheet">
              <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
             </property>
             <property name="text">
              <string>A</string>
             </property>
            </widget>
           </item>
           <item row="5" column="0">
            <widget class="QPushButton" name="J3DButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J3/减号 J3.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="5" column="1">
            <widget class="QSlider" name="J3Slider">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="5" column="2">
            <widget class="QPushButton" name="J3UButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J3/加号 J3.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <widget class="QLabel" name="J4label">
             <property name="text">
              <string>J4</string>
             </property>
            </widget>
           </item>
           <item row="6" column="1">
            <widget class="QLineEdit" name="J4lineEdit">
             <property name="styleSheet">
              <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
             </property>
             <property name="text">
              <string>A</string>
             </property>
            </widget>
           </item>
           <item row="7" column="0">
            <widget class="QPushButton" name="J4DButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J4/减号 J4.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="7" column="1">
            <widget class="QSlider" name="J4Slider">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="7" column="2">
            <widget class="QPushButton" name="J4UButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J4/加号 J4.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="8" column="0">
            <widget class="QLabel" name="J5label">
             <property name="text">
              <string>J5</string>
             </property>
            </widget>
           </item>
           <item row="8" column="1">
            <widget class="QLineEdit" name="J5lineEdit">
             <property name="styleSheet">
              <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
             </property>
             <property name="text">
              <string>A</string>
             </property>
            </widget>
           </item>
           <item row="9" column="0">
            <widget class="QPushButton" name="J5DButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J5/减号 J5.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="9" column="1">
            <widget class="QSlider" name="J5Slider">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="9" column="2">
            <widget class="QPushButton" name="J5UButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J5/加号 J5.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="10" column="0">
            <widget class="QLabel" name="J6label">
             <property name="text">
              <string>J6</string>
             </property>
            </widget>
           </item>
           <item row="10" column="1">
            <widget class="QLineEdit" name="J6lineEdit">
             <property name="styleSheet">
              <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
             </property>
             <property name="text">
              <string>A</string>
             </property>
            </widget>
           </item>
           <item row="11" column="0">
            <widget class="QPushButton" name="J6DButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J6/减号 J6.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="11" column="1">
            <widget class="QSlider" name="J6Slider">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="11" column="2">
            <widget class="QPushButton" name="J6UButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/J6/加号 J6.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QPushButton" name="J1UButton">
             <property name="styleSheet">
              <string notr="true">background-image: url(:/j1/加号 J1.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_6">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>2</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;</string>
     </property>
     <property name="title">
      <string/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <spacer name="verticalSpacer_4">
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>10</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label_17">
        <property name="styleSheet">
         <string notr="true">font-family: MiSans, MiSans;
font-weight: 600;
font-size: 14px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
        </property>
        <property name="text">
         <string>笛卡尔空间移动</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QLabel" name="label_18">
          <property name="styleSheet">
           <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
          </property>
          <property name="text">
           <string>单次长按阈值</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEdit_17">
          <property name="styleSheet">
           <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
          </property>
          <property name="text">
           <string>A</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>15</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="ZDButton">
          <property name="minimumSize">
           <size>
            <width>59</width>
            <height>44</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/z/Z-.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>15</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="ZUButton">
          <property name="minimumSize">
           <size>
            <width>59</width>
            <height>44</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/z/Z+.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="1">
         <widget class="QPushButton" name="XDButton">
          <property name="minimumSize">
           <size>
            <width>83</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/x/X-.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QPushButton" name="YDButton">
          <property name="minimumSize">
           <size>
            <width>68</width>
            <height>47</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/y/Y-.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QPushButton" name="YUButton">
          <property name="minimumSize">
           <size>
            <width>68</width>
            <height>47</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/y/Y+.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QPushButton" name="XUButton">
          <property name="minimumSize">
           <size>
            <width>83</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/x/X-.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <item>
         <widget class="QLabel" name="Xlabel">
          <property name="styleSheet">
           <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
          </property>
          <property name="text">
           <string>X</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="XlineEdit">
          <property name="styleSheet">
           <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
          </property>
          <property name="text">
           <string>A</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="Ylabel">
          <property name="styleSheet">
           <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
          </property>
          <property name="text">
           <string>Y</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="YlineEdit">
          <property name="styleSheet">
           <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
          </property>
          <property name="text">
           <string>A</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="Zlabel">
          <property name="styleSheet">
           <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
          </property>
          <property name="text">
           <string>Z</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="ZlineEdit">
          <property name="styleSheet">
           <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
          </property>
          <property name="text">
           <string>A</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_6">
        <item>
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="RZUButton">
          <property name="minimumSize">
           <size>
            <width>59</width>
            <height>44</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/rz/RZ+.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="RZDButton">
          <property name="minimumSize">
           <size>
            <width>59</width>
            <height>44</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/rz/RZ-.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_6">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="0" column="1">
         <widget class="QPushButton" name="RYDButton">
          <property name="minimumSize">
           <size>
            <width>83</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/ry/RY-.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QPushButton" name="RXUButton">
          <property name="minimumSize">
           <size>
            <width>68</width>
            <height>47</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/rx/RX+.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QPushButton" name="RYUButton">
          <property name="minimumSize">
           <size>
            <width>83</width>
            <height>50</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/ry/RY+.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="1" column="2">
         <widget class="QPushButton" name="RXDButton">
          <property name="minimumSize">
           <size>
            <width>68</width>
            <height>47</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">background-image: url(:/rx/RX-.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_7">
        <item>
         <widget class="QLabel" name="RXlabel">
          <property name="styleSheet">
           <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
          </property>
          <property name="text">
           <string>RX</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="RXlineEdit">
          <property name="styleSheet">
           <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
          </property>
          <property name="text">
           <string>A</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="RYlabel">
          <property name="styleSheet">
           <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
          </property>
          <property name="text">
           <string>RY</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="RYlineEdit">
          <property name="styleSheet">
           <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
          </property>
          <property name="text">
           <string>A</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="RZlabel">
          <property name="styleSheet">
           <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
          </property>
          <property name="text">
           <string>RZ</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="RZlineEdit">
          <property name="styleSheet">
           <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
          </property>
          <property name="text">
           <string>A</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer_3">
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_7">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>1</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="title">
      <string/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2" stretch="3,1,0">
      <item>
       <widget class="QGroupBox" name="groupBox_3">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>3</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;</string>
        </property>
        <property name="title">
         <string/>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_5">
         <item>
          <spacer name="verticalSpacer_6">
           <property name="orientation">
            <enum>Qt::Orientation::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_38">
           <property name="styleSheet">
            <string notr="true">font-family: MiSans, MiSans;
font-weight: 600;
font-size: 14px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
           </property>
           <property name="text">
            <string>升降台</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_8">
           <item>
            <widget class="QLabel" name="label_39">
             <property name="styleSheet">
              <string notr="true">font-family: MiSans, MiSans;
font-weight: 400;
font-size: 12px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
             </property>
             <property name="text">
              <string>单次长按阈值</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit_34">
             <property name="styleSheet">
              <string notr="true">background: #E2EAF5;
border-radius: 10px 10px 10px 10px;</string>
             </property>
             <property name="text">
              <string>A</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_9">
           <item>
            <spacer name="horizontalSpacer_7">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox">
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>170</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">background: #E2EAF5;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;</string>
             </property>
             <property name="title">
              <string/>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_6">
              <item>
               <widget class="QPushButton" name="UpButton">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>66</width>
                  <height>67</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">background-image: url(:/ud/UP.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="DownButton">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>66</width>
                  <height>67</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">background-image: url(:/ud/DOWN.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_8">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_4">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>1</verstretch>
         </sizepolicy>
        </property>
        <property name="styleSheet">
         <string notr="true">background: #FFFFFF;
box-shadow: 4px 0px 0px 0px #E2EAF5;
border-radius: 20px 20px 20px 20px;</string>
        </property>
        <property name="title">
         <string/>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_7">
         <item>
          <widget class="QLabel" name="label_40">
           <property name="styleSheet">
            <string notr="true">font-family: MiSans, MiSans;
font-weight: 600;
font-size: 14px;
color: #000000;
line-height: 14px;
text-align: left;
font-style: normal;
text-transform: none;</string>
           </property>
           <property name="text">
            <string>工具控制</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_10">
           <item>
            <widget class="QPushButton" name="fetchingButton">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>89</width>
               <height>34</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">background-image: url(:/fr/抓取.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="ReleaseButton">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>89</width>
               <height>34</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">background-image: url(:/fr/释放.png);
background-repeat: no-repeat; 
background-position: center;
background-size: contain;
border: none;</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_5">
        <property name="orientation">
         <enum>Qt::Orientation::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../resources/manual/resources_manual.qrc"/>
 </resources>
 <connections/>
</ui>
